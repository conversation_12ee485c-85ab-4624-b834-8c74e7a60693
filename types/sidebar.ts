export interface SidebarPreferences {
  sidebarOpen: boolean;
  sidebarWidth: number;
  sidebarPosition: 'left' | 'right';
}

export interface SidebarState {
  isOpen: boolean;
  isMobile: boolean;
  setOpen: (open: boolean) => void;
  setMobile: (isMobile: boolean) => void;
  toggle: () => void;
}

export interface SidebarMenuItem {
  id: string;
  title: string;
  icon?: React.ReactNode;
  href: string;
  isActive?: boolean;
  children?: SidebarMenuItem[];
}

export interface SidebarSection {
  id: string;
  title: string;
  items: SidebarMenuItem[];
} 