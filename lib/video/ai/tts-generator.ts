import { EventEmitter } from 'events';
import { VideoSystemConfig, TTSRequest, TTSResponse } from '../types/video-types';

/**
 * Text-to-Speech Generator
 * Integrates with various TTS services for audio generation
 */
export class TTSGenerator extends EventEmitter {
  private config: VideoSystemConfig;
  private activeRequests: Map<string, AbortController> = new Map();

  constructor(config: VideoSystemConfig) {
    super();
    this.config = config;
  }

  async generateSpeech(request: TTSRequest): Promise<TTSResponse> {
    const requestId = `tts-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const abortController = new AbortController();
    this.activeRequests.set(requestId, abortController);

    try {
      this.emit('tts:started', { requestId, request });

      let response: TTSResponse;

      switch (request.provider) {
        case 'elevenlabs':
          response = await this.generateWithElevenLabs(request, requestId, abortController.signal);
          break;
        case 'openai':
          response = await this.generateWithOpenAI(request, requestId, abortController.signal);
          break;
        case 'google':
          response = await this.generateWithGoogle(request, requestId, abortController.signal);
          break;
        case 'azure':
          response = await this.generateWithAzure(request, requestId, abortController.signal);
          break;
        default:
          throw new Error(`Unsupported TTS provider: ${request.provider}`);
      }

      this.emit('tts:completed', { requestId, response });
      return response;

    } catch (error) {
      this.emit('tts:failed', { requestId, error });
      throw error;
    } finally {
      this.activeRequests.delete(requestId);
    }
  }

  private async generateWithElevenLabs(
    request: TTSRequest,
    requestId: string,
    signal: AbortSignal
  ): Promise<TTSResponse> {
    const apiKey = this.config.aiProviders?.elevenlabs?.apiKey;
    if (!apiKey) {
      throw new Error('ElevenLabs API key not configured');
    }

    const elevenLabsRequest = {
      text: request.text,
      model_id: request.model || 'eleven_monolingual_v1',
      voice_settings: {
        stability: 0.5,
        similarity_boost: 0.5,
        style: 0.0,
        use_speaker_boost: true,
      },
    };

    try {
      const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${request.voice}`, {
        method: 'POST',
        headers: {
          'Accept': 'audio/mpeg',
          'Content-Type': 'application/json',
          'xi-api-key': apiKey,
        },
        body: JSON.stringify(elevenLabsRequest),
        signal,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`ElevenLabs API error: ${response.statusText} - ${errorText}`);
      }

      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);

      // Get audio duration (approximate)
      const duration = await this.getAudioDuration(audioBlob);

      return {
        id: requestId,
        audioUrl,
        duration,
        format: 'mp3',
        metadata: {
          sampleRate: 44100,
          bitrate: 128000,
          channels: 1,
        },
      };

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('TTS generation cancelled');
      }
      throw error;
    }
  }

  private async generateWithOpenAI(
    request: TTSRequest,
    requestId: string,
    signal: AbortSignal
  ): Promise<TTSResponse> {
    const apiKey = this.config.aiProviders?.openai?.apiKey;
    if (!apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const openAIRequest = {
      model: request.model || 'tts-1',
      input: request.text,
      voice: request.voice,
      response_format: 'mp3',
      speed: request.speed || 1.0,
    };

    try {
      const response = await fetch('https://api.openai.com/v1/audio/speech', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(openAIRequest),
        signal,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`OpenAI API error: ${response.statusText} - ${errorText}`);
      }

      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);
      const duration = await this.getAudioDuration(audioBlob);

      return {
        id: requestId,
        audioUrl,
        duration,
        format: 'mp3',
        metadata: {
          sampleRate: 24000,
          bitrate: 64000,
          channels: 1,
        },
      };

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('TTS generation cancelled');
      }
      throw error;
    }
  }

  private async generateWithGoogle(
    request: TTSRequest,
    requestId: string,
    signal: AbortSignal
  ): Promise<TTSResponse> {
    // Google Cloud Text-to-Speech API integration
    const apiKey = this.config.aiProviders?.google?.apiKey;
    if (!apiKey) {
      throw new Error('Google API key not configured');
    }

    const googleRequest = {
      input: { text: request.text },
      voice: {
        languageCode: request.language,
        name: request.voice,
        ssmlGender: 'NEUTRAL',
      },
      audioConfig: {
        audioEncoding: 'MP3',
        speakingRate: request.speed || 1.0,
        pitch: request.pitch || 0.0,
        volumeGainDb: this.volumeToDb(request.volume || 1.0),
      },
    };

    try {
      const response = await fetch(`https://texttospeech.googleapis.com/v1/text:synthesize?key=${apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(googleRequest),
        signal,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Google TTS API error: ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      const audioBlob = this.base64ToBlob(data.audioContent, 'audio/mpeg');
      const audioUrl = URL.createObjectURL(audioBlob);
      const duration = await this.getAudioDuration(audioBlob);

      return {
        id: requestId,
        audioUrl,
        duration,
        format: 'mp3',
        metadata: {
          sampleRate: 24000,
          bitrate: 64000,
          channels: 1,
        },
      };

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('TTS generation cancelled');
      }
      throw error;
    }
  }

  private async generateWithAzure(
    request: TTSRequest,
    requestId: string,
    signal: AbortSignal
  ): Promise<TTSResponse> {
    // Azure Cognitive Services Speech API integration
    const apiKey = this.config.aiProviders?.azure?.apiKey;
    const region = this.config.aiProviders?.azure?.region || 'eastus';
    
    if (!apiKey) {
      throw new Error('Azure API key not configured');
    }

    const ssml = this.createSSML(request);

    try {
      const response = await fetch(`https://${region}.tts.speech.microsoft.com/cognitiveservices/v1`, {
        method: 'POST',
        headers: {
          'Ocp-Apim-Subscription-Key': apiKey,
          'Content-Type': 'application/ssml+xml',
          'X-Microsoft-OutputFormat': 'audio-24khz-48kbitrate-mono-mp3',
          'User-Agent': 'VideoSystem',
        },
        body: ssml,
        signal,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Azure TTS API error: ${response.statusText} - ${errorText}`);
      }

      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);
      const duration = await this.getAudioDuration(audioBlob);

      return {
        id: requestId,
        audioUrl,
        duration,
        format: 'mp3',
        metadata: {
          sampleRate: 24000,
          bitrate: 48000,
          channels: 1,
        },
      };

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('TTS generation cancelled');
      }
      throw error;
    }
  }

  // Voice cloning (ElevenLabs specific)
  async cloneVoice(
    name: string,
    audioSamples: File[],
    description?: string
  ): Promise<string> {
    const apiKey = this.config.aiProviders?.elevenlabs?.apiKey;
    if (!apiKey) {
      throw new Error('ElevenLabs API key not configured');
    }

    const formData = new FormData();
    formData.append('name', name);
    if (description) {
      formData.append('description', description);
    }

    audioSamples.forEach((sample, index) => {
      formData.append('files', sample, `sample_${index}.wav`);
    });

    try {
      const response = await fetch('https://api.elevenlabs.io/v1/voices/add', {
        method: 'POST',
        headers: {
          'xi-api-key': apiKey,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Voice cloning failed: ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      this.emit('voice:cloned', { voiceId: data.voice_id, name });
      
      return data.voice_id;

    } catch (error) {
      this.emit('voice:clone:failed', { error });
      throw error;
    }
  }

  // Get available voices
  async getAvailableVoices(provider: string): Promise<any[]> {
    switch (provider) {
      case 'elevenlabs':
        return this.getElevenLabsVoices();
      case 'openai':
        return this.getOpenAIVoices();
      case 'google':
        return this.getGoogleVoices();
      case 'azure':
        return this.getAzureVoices();
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  private async getElevenLabsVoices(): Promise<any[]> {
    const apiKey = this.config.aiProviders?.elevenlabs?.apiKey;
    if (!apiKey) return [];

    try {
      const response = await fetch('https://api.elevenlabs.io/v1/voices', {
        headers: {
          'xi-api-key': apiKey,
        },
      });

      if (!response.ok) return [];

      const data = await response.json();
      return data.voices;

    } catch (error) {
      console.error('Failed to fetch ElevenLabs voices:', error);
      return [];
    }
  }

  private getOpenAIVoices(): any[] {
    return [
      { id: 'alloy', name: 'Alloy' },
      { id: 'echo', name: 'Echo' },
      { id: 'fable', name: 'Fable' },
      { id: 'onyx', name: 'Onyx' },
      { id: 'nova', name: 'Nova' },
      { id: 'shimmer', name: 'Shimmer' },
    ];
  }

  private async getGoogleVoices(): Promise<any[]> {
    // This would require a proper API call to Google Cloud TTS
    return [
      { id: 'en-US-Wavenet-A', name: 'English (US) - Wavenet A', language: 'en-US' },
      { id: 'en-US-Wavenet-B', name: 'English (US) - Wavenet B', language: 'en-US' },
      { id: 'en-US-Wavenet-C', name: 'English (US) - Wavenet C', language: 'en-US' },
    ];
  }

  private async getAzureVoices(): Promise<any[]> {
    // This would require a proper API call to Azure Cognitive Services
    return [
      { id: 'en-US-AriaNeural', name: 'Aria (Neural)', language: 'en-US' },
      { id: 'en-US-JennyNeural', name: 'Jenny (Neural)', language: 'en-US' },
      { id: 'en-US-GuyNeural', name: 'Guy (Neural)', language: 'en-US' },
    ];
  }

  // Batch TTS generation
  async generateBatch(requests: TTSRequest[]): Promise<TTSResponse[]> {
    const results = await Promise.allSettled(
      requests.map(request => this.generateSpeech(request))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          id: `batch-${index}`,
          audioUrl: '',
          duration: 0,
          format: 'mp3',
          metadata: {
            sampleRate: 0,
            bitrate: 0,
            channels: 0,
          },
          error: result.reason.message,
        };
      }
    });
  }

  // Utility methods
  private async getAudioDuration(audioBlob: Blob): Promise<number> {
    return new Promise((resolve) => {
      const audio = new Audio();
      audio.addEventListener('loadedmetadata', () => {
        resolve(audio.duration);
      });
      audio.addEventListener('error', () => {
        resolve(0); // Fallback duration
      });
      audio.src = URL.createObjectURL(audioBlob);
    });
  }

  private base64ToBlob(base64: string, mimeType: string): Blob {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }

  private volumeToDb(volume: number): number {
    return 20 * Math.log10(Math.max(0.01, volume));
  }

  private createSSML(request: TTSRequest): string {
    const rate = request.speed ? `${request.speed * 100}%` : '100%';
    const pitch = request.pitch ? `${request.pitch > 0 ? '+' : ''}${request.pitch}Hz` : '0Hz';
    
    return `
      <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="${request.language}">
        <voice name="${request.voice}">
          <prosody rate="${rate}" pitch="${pitch}">
            ${request.text}
          </prosody>
        </voice>
      </speak>
    `.trim();
  }

  // Cancel TTS generation
  async cancelGeneration(requestId: string): Promise<void> {
    const controller = this.activeRequests.get(requestId);
    if (controller) {
      controller.abort();
      this.activeRequests.delete(requestId);
      this.emit('tts:cancelled', { requestId });
    }
  }

  // Configuration
  updateConfig(config: VideoSystemConfig): void {
    this.config = { ...this.config, ...config };
  }

  // Cleanup
  async dispose(): Promise<void> {
    // Cancel all active requests
    for (const [requestId] of this.activeRequests) {
      await this.cancelGeneration(requestId);
    }
    
    this.removeAllListeners();
  }
}