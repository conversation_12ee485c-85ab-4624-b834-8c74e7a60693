import { EventEmitter } from 'events';
import { 
  VideoSystemConfig, 
  AIVideoGenerationRequest, 
  AIVideoGenerationResponse 
} from '../types/video-types';

/**
 * AI Video Generator
 * Integrates with various AI video generation services
 */
export class AIVideoGenerator extends EventEmitter {
  private config: VideoSystemConfig;
  private activeRequests: Map<string, AbortController> = new Map();

  constructor(config: VideoSystemConfig) {
    super();
    this.config = config;
  }

  async generateVideo(
    request: AIVideoGenerationRequest,
    progressCallback?: (progress: number) => void
  ): Promise<AIVideoGenerationResponse> {
    const requestId = `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const abortController = new AbortController();
    this.activeRequests.set(requestId, abortController);

    try {
      this.emit('generation:started', { requestId, request });

      // Choose provider based on model preference
      const provider = request.model || 'runway';
      let response: AIVideoGenerationResponse;

      switch (provider) {
        case 'runway':
          response = await this.generateWithRunway(request, requestId, progressCallback, abortController.signal);
          break;
        case 'pika':
          response = await this.generateWithPika(request, requestId, progressCallback, abortController.signal);
          break;
        case 'stability':
          response = await this.generateWithStability(request, requestId, progressCallback, abortController.signal);
          break;
        default:
          throw new Error(`Unsupported AI model: ${provider}`);
      }

      this.emit('generation:completed', { requestId, response });
      return response;

    } catch (error) {
      this.emit('generation:failed', { requestId, error });
      throw error;
    } finally {
      this.activeRequests.delete(requestId);
    }
  }

  private async generateWithRunway(
    request: AIVideoGenerationRequest,
    requestId: string,
    progressCallback?: (progress: number) => void,
    signal?: AbortSignal
  ): Promise<AIVideoGenerationResponse> {
    const apiKey = this.config.aiProviders?.runway?.apiKey;
    if (!apiKey) {
      throw new Error('Runway API key not configured');
    }

    // Runway ML API integration
    const runwayRequest = {
      text_prompt: request.prompt,
      duration: request.duration,
      resolution: `${request.resolution.width}x${request.resolution.height}`,
      fps: request.fps,
      style: request.style || 'realistic',
      seed: request.seed,
    };

    try {
      // Submit generation request
      const submitResponse = await fetch('https://api.runwayml.com/v1/video/generate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(runwayRequest),
        signal,
      });

      if (!submitResponse.ok) {
        throw new Error(`Runway API error: ${submitResponse.statusText}`);
      }

      const submitData = await submitResponse.json();
      const jobId = submitData.id;

      // Poll for completion
      return await this.pollRunwayJob(jobId, requestId, progressCallback, signal);

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Generation cancelled');
      }
      throw error;
    }
  }

  private async pollRunwayJob(
    jobId: string,
    requestId: string,
    progressCallback?: (progress: number) => void,
    signal?: AbortSignal
  ): Promise<AIVideoGenerationResponse> {
    const apiKey = this.config.aiProviders?.runway?.apiKey!;
    const maxAttempts = 120; // 10 minutes with 5-second intervals
    let attempts = 0;

    while (attempts < maxAttempts) {
      if (signal?.aborted) {
        throw new Error('Generation cancelled');
      }

      try {
        const statusResponse = await fetch(`https://api.runwayml.com/v1/video/generate/${jobId}`, {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
          },
          signal,
        });

        if (!statusResponse.ok) {
          throw new Error(`Runway status check failed: ${statusResponse.statusText}`);
        }

        const statusData = await statusResponse.json();
        
        // Update progress
        const progress = statusData.progress || (attempts / maxAttempts) * 90;
        if (progressCallback) {
          progressCallback(progress);
        }

        if (statusData.status === 'completed') {
          return {
            id: requestId,
            status: 'completed',
            videoUrl: statusData.output_url,
            thumbnailUrl: statusData.thumbnail_url,
            duration: statusData.duration,
            metadata: {
              width: statusData.width,
              height: statusData.height,
              fps: statusData.fps,
              format: 'mp4',
            },
          };
        }

        if (statusData.status === 'failed') {
          throw new Error(`Runway generation failed: ${statusData.error || 'Unknown error'}`);
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, 5000));
        attempts++;

      } catch (error) {
        if (error instanceof Error && error.name === 'AbortError') {
          throw new Error('Generation cancelled');
        }
        throw error;
      }
    }

    throw new Error('Generation timeout - job did not complete in time');
  }

  private async generateWithPika(
    request: AIVideoGenerationRequest,
    requestId: string,
    progressCallback?: (progress: number) => void,
    signal?: AbortSignal
  ): Promise<AIVideoGenerationResponse> {
    const apiKey = this.config.aiProviders?.pika?.apiKey;
    if (!apiKey) {
      throw new Error('Pika API key not configured');
    }

    // Pika Labs API integration (hypothetical API structure)
    const pikaRequest = {
      prompt: request.prompt,
      duration: request.duration,
      width: request.resolution.width,
      height: request.resolution.height,
      fps: request.fps,
      style: request.style || 'realistic',
      seed: request.seed,
    };

    try {
      const response = await fetch('https://api.pikalabs.ai/v1/generate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(pikaRequest),
        signal,
      });

      if (!response.ok) {
        throw new Error(`Pika API error: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Simulate progress updates
      if (progressCallback) {
        for (let i = 0; i <= 100; i += 10) {
          if (signal?.aborted) break;
          progressCallback(i);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      return {
        id: requestId,
        status: 'completed',
        videoUrl: data.video_url,
        thumbnailUrl: data.thumbnail_url,
        duration: data.duration,
        metadata: {
          width: data.width,
          height: data.height,
          fps: data.fps,
          format: 'mp4',
        },
      };

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Generation cancelled');
      }
      throw error;
    }
  }

  private async generateWithStability(
    request: AIVideoGenerationRequest,
    requestId: string,
    progressCallback?: (progress: number) => void,
    signal?: AbortSignal
  ): Promise<AIVideoGenerationResponse> {
    const apiKey = this.config.aiProviders?.stability?.apiKey;
    if (!apiKey) {
      throw new Error('Stability AI API key not configured');
    }

    // Stability AI Video API integration
    const stabilityRequest = {
      text_prompts: [{ text: request.prompt, weight: 1.0 }],
      cfg_scale: 7.0,
      clip_guidance_preset: 'FAST_BLUE',
      height: request.resolution.height,
      width: request.resolution.width,
      samples: 1,
      steps: 30,
      seed: request.seed || Math.floor(Math.random() * 1000000),
    };

    try {
      const response = await fetch('https://api.stability.ai/v1/generation/stable-video-diffusion-1-1/text-to-video', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(stabilityRequest),
        signal,
      });

      if (!response.ok) {
        throw new Error(`Stability AI error: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Simulate progress updates
      if (progressCallback) {
        for (let i = 0; i <= 100; i += 5) {
          if (signal?.aborted) break;
          progressCallback(i);
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // Convert base64 video to blob URL
      const videoBlob = this.base64ToBlob(data.artifacts[0].base64, 'video/mp4');
      const videoUrl = URL.createObjectURL(videoBlob);

      return {
        id: requestId,
        status: 'completed',
        videoUrl,
        duration: request.duration,
        metadata: {
          width: request.resolution.width,
          height: request.resolution.height,
          fps: request.fps,
          format: 'mp4',
        },
      };

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Generation cancelled');
      }
      throw error;
    }
  }

  // Image-to-Video generation
  async generateVideoFromImage(
    imageSource: string | File | Blob,
    prompt: string,
    duration: number = 5,
    model: string = 'runway'
  ): Promise<AIVideoGenerationResponse> {
    const requestId = `img2vid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      let imageUrl: string;
      
      if (typeof imageSource === 'string') {
        imageUrl = imageSource;
      } else {
        // Upload image to temporary storage or convert to base64
        imageUrl = await this.uploadImage(imageSource);
      }

      const request: AIVideoGenerationRequest = {
        prompt,
        duration,
        resolution: { width: 1024, height: 576, aspectRatio: '16:9' },
        fps: 24,
        model,
      };

      // Add image URL to request (API-specific implementation)
      const extendedRequest = {
        ...request,
        image_url: imageUrl,
      };

      return await this.generateVideo(extendedRequest);

    } catch (error) {
      this.emit('generation:failed', { requestId, error });
      throw error;
    }
  }

  // Video-to-Video generation (style transfer, etc.)
  async generateVideoFromVideo(
    videoSource: string | File | Blob,
    prompt: string,
    model: string = 'runway'
  ): Promise<AIVideoGenerationResponse> {
    const requestId = `vid2vid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      let videoUrl: string;
      
      if (typeof videoSource === 'string') {
        videoUrl = videoSource;
      } else {
        videoUrl = await this.uploadVideo(videoSource);
      }

      // This would be implemented based on specific API requirements
      throw new Error('Video-to-video generation not yet implemented');

    } catch (error) {
      this.emit('generation:failed', { requestId, error });
      throw error;
    }
  }

  // Utility methods
  private async uploadImage(image: File | Blob): Promise<string> {
    // Convert to base64 or upload to temporary storage
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(image);
    });
  }

  private async uploadVideo(video: File | Blob): Promise<string> {
    // Similar to uploadImage but for video files
    return URL.createObjectURL(video);
  }

  private base64ToBlob(base64: string, mimeType: string): Blob {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }

  // Batch generation
  async generateBatch(
    requests: AIVideoGenerationRequest[]
  ): Promise<AIVideoGenerationResponse[]> {
    const results = await Promise.allSettled(
      requests.map(request => this.generateVideo(request))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          id: `batch-${index}`,
          status: 'failed',
          error: result.reason.message,
        };
      }
    });
  }

  // Cancel generation
  async cancelGeneration(requestId: string): Promise<void> {
    const controller = this.activeRequests.get(requestId);
    if (controller) {
      controller.abort();
      this.activeRequests.delete(requestId);
      this.emit('generation:cancelled', { requestId });
    }
  }

  // Get available models
  getAvailableModels(): string[] {
    const models: string[] = [];
    
    if (this.config.aiProviders?.runway?.apiKey) {
      models.push('runway');
    }
    if (this.config.aiProviders?.pika?.apiKey) {
      models.push('pika');
    }
    if (this.config.aiProviders?.stability?.apiKey) {
      models.push('stability');
    }

    return models;
  }

  // Configuration
  updateConfig(config: VideoSystemConfig): void {
    this.config = { ...this.config, ...config };
  }

  // Cleanup
  async dispose(): Promise<void> {
    // Cancel all active requests
    for (const [requestId] of this.activeRequests) {
      await this.cancelGeneration(requestId);
    }
    
    this.removeAllListeners();
  }
}