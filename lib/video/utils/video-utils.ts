import { VideoMetadata, VideoResolution, VideoAnalytics } from '../types/video-types';

/**
 * Video Utility Functions
 * Common utilities for video processing and manipulation
 */

// Export standalone utility functions
export function formatTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

export class VideoUtils {
  /**
   * Format time in seconds to MM:SS format
   */
  static formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * Convert time in seconds to timecode format (HH:MM:SS:FF)
   */
  static secondsToTimecode(seconds: number, fps: number = 30): string {
    const totalFrames = Math.floor(seconds * fps);
    const frames = totalFrames % fps;
    const totalSeconds = Math.floor(totalFrames / fps);
    const secs = totalSeconds % 60;
    const totalMinutes = Math.floor(totalSeconds / 60);
    const mins = totalMinutes % 60;
    const hours = Math.floor(totalMinutes / 60);

    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}:${frames.toString().padStart(2, '0')}`;
  }

  /**
   * Convert timecode format to seconds
   */
  static timecodeToSeconds(timecode: string, fps: number = 30): number {
    const parts = timecode.split(':');
    if (parts.length !== 4) {
      throw new Error('Invalid timecode format. Expected HH:MM:SS:FF');
    }

    const [hours, minutes, seconds, frames] = parts.map(Number);
    return hours * 3600 + minutes * 60 + seconds + frames / fps;
  }

  /**
   * Format duration in a human-readable way
   */
  static formatDuration(seconds: number): string {
    if (seconds < 60) {
      return `${Math.round(seconds)}s`;
    } else if (seconds < 3600) {
      const mins = Math.floor(seconds / 60);
      const secs = Math.round(seconds % 60);
      return `${mins}m ${secs}s`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const mins = Math.floor((seconds % 3600) / 60);
      const secs = Math.round(seconds % 60);
      return `${hours}h ${mins}m ${secs}s`;
    }
  }

  /**
   * Calculate aspect ratio from width and height
   */
  static calculateAspectRatio(width: number, height: number): string {
    const gcd = this.greatestCommonDivisor(width, height);
    const ratioWidth = width / gcd;
    const ratioHeight = height / gcd;
    
    // Common aspect ratios
    const commonRatios: Record<string, string> = {
      '16:9': '16:9',
      '4:3': '4:3',
      '1:1': '1:1',
      '21:9': '21:9',
      '9:16': '9:16', // Vertical/mobile
      '3:4': '3:4',   // Vertical 4:3
    };

    const ratioString = `${ratioWidth}:${ratioHeight}`;
    return commonRatios[ratioString] || ratioString;
  }

  /**
   * Get standard resolutions for a given aspect ratio
   */
  static getStandardResolutions(aspectRatio: string): VideoResolution[] {
    const resolutions: Record<string, VideoResolution[]> = {
      '16:9': [
        { width: 1920, height: 1080, aspectRatio: '16:9' }, // 1080p
        { width: 1280, height: 720, aspectRatio: '16:9' },  // 720p
        { width: 3840, height: 2160, aspectRatio: '16:9' }, // 4K
        { width: 854, height: 480, aspectRatio: '16:9' },   // 480p
      ],
      '4:3': [
        { width: 1024, height: 768, aspectRatio: '4:3' },
        { width: 800, height: 600, aspectRatio: '4:3' },
        { width: 640, height: 480, aspectRatio: '4:3' },
      ],
      '1:1': [
        { width: 1080, height: 1080, aspectRatio: '1:1' },
        { width: 720, height: 720, aspectRatio: '1:1' },
        { width: 480, height: 480, aspectRatio: '1:1' },
      ],
      '9:16': [
        { width: 1080, height: 1920, aspectRatio: '9:16' },
        { width: 720, height: 1280, aspectRatio: '9:16' },
        { width: 480, height: 854, aspectRatio: '9:16' },
      ],
    };

    return resolutions[aspectRatio] || [];
  }

  /**
   * Calculate file size from bitrate and duration
   */
  static calculateFileSize(bitrate: number, duration: number): number {
    // bitrate in bits per second, duration in seconds
    // returns size in bytes
    return (bitrate * duration) / 8;
  }

  /**
   * Format file size in human-readable format
   */
  static formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * Validate video file format
   */
  static isValidVideoFormat(filename: string): boolean {
    const validExtensions = [
      '.mp4', '.mov', '.avi', '.mkv', '.webm', '.flv', '.wmv', '.m4v', '.3gp'
    ];
    
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    return validExtensions.includes(extension);
  }

  /**
   * Validate audio file format
   */
  static isValidAudioFormat(filename: string): boolean {
    const validExtensions = [
      '.mp3', '.wav', '.aac', '.ogg', '.flac', '.m4a', '.wma'
    ];
    
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    return validExtensions.includes(extension);
  }

  /**
   * Validate image file format
   */
  static isValidImageFormat(filename: string): boolean {
    const validExtensions = [
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'
    ];
    
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    return validExtensions.includes(extension);
  }

  /**
   * Get optimal bitrate for resolution and quality
   */
  static getOptimalBitrate(resolution: VideoResolution, quality: 'low' | 'medium' | 'high' | 'ultra'): number {
    const pixelCount = resolution.width * resolution.height;
    
    // Base bitrate per pixel (bits per second per pixel)
    const baseRates = {
      low: 0.1,
      medium: 0.2,
      high: 0.4,
      ultra: 0.8,
    };

    return Math.round(pixelCount * baseRates[quality]);
  }

  /**
   * Calculate optimal frame rate for content type
   */
  static getOptimalFrameRate(contentType: 'film' | 'tv' | 'web' | 'gaming' | 'animation'): number {
    const frameRates = {
      film: 24,
      tv: 30,
      web: 30,
      gaming: 60,
      animation: 24,
    };

    return frameRates[contentType] || 30;
  }

  /**
   * Generate thumbnail from video at specific time
   */
  static async generateThumbnail(
    videoElement: HTMLVideoElement,
    time: number,
    width: number = 160,
    height: number = 90
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        reject(new Error('Could not get canvas context'));
        return;
      }

      canvas.width = width;
      canvas.height = height;

      const onSeeked = () => {
        ctx.drawImage(videoElement, 0, 0, width, height);
        const dataURL = canvas.toDataURL('image/jpeg', 0.8);
        videoElement.removeEventListener('seeked', onSeeked);
        resolve(dataURL);
      };

      videoElement.addEventListener('seeked', onSeeked);
      videoElement.currentTime = time;
    });
  }

  /**
   * Extract frames from video at regular intervals
   */
  static async extractFrames(
    videoElement: HTMLVideoElement,
    count: number = 10,
    width: number = 160,
    height: number = 90
  ): Promise<string[]> {
    const frames: string[] = [];
    const interval = videoElement.duration / count;

    for (let i = 0; i < count; i++) {
      const time = i * interval;
      try {
        const frame = await this.generateThumbnail(videoElement, time, width, height);
        frames.push(frame);
      } catch (error) {
        console.warn(`Failed to extract frame at ${time}s:`, error);
      }
    }

    return frames;
  }

  /**
   * Analyze video color palette
   */
  static analyzeColorPalette(
    canvas: HTMLCanvasElement,
    sampleSize: number = 1000
  ): { dominant: string; palette: string[] } {
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      return { dominant: '#000000', palette: [] };
    }

    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    const colorCounts: Record<string, number> = {};

    // Sample pixels
    const step = Math.max(1, Math.floor(data.length / (sampleSize * 4)));
    
    for (let i = 0; i < data.length; i += step * 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const alpha = data[i + 3];

      if (alpha < 128) continue; // Skip transparent pixels

      // Quantize colors to reduce palette size
      const qR = Math.floor(r / 32) * 32;
      const qG = Math.floor(g / 32) * 32;
      const qB = Math.floor(b / 32) * 32;
      
      const color = `rgb(${qR},${qG},${qB})`;
      colorCounts[color] = (colorCounts[color] || 0) + 1;
    }

    // Sort colors by frequency
    const sortedColors = Object.entries(colorCounts)
      .sort(([, a], [, b]) => b - a)
      .map(([color]) => color);

    const dominant = sortedColors[0] || '#000000';
    const palette = sortedColors.slice(0, 10);

    return { dominant, palette };
  }

  /**
   * Calculate video complexity score (for encoding optimization)
   */
  static calculateComplexityScore(
    canvas: HTMLCanvasElement,
    previousCanvas?: HTMLCanvasElement
  ): number {
    const ctx = canvas.getContext('2d');
    if (!ctx) return 0;

    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    
    let complexity = 0;
    let motionComplexity = 0;

    // Calculate spatial complexity (edge detection)
    for (let i = 0; i < data.length - 4; i += 4) {
      const r1 = data[i];
      const g1 = data[i + 1];
      const b1 = data[i + 2];
      
      const r2 = data[i + 4];
      const g2 = data[i + 5];
      const b2 = data[i + 6];

      const diff = Math.abs(r1 - r2) + Math.abs(g1 - g2) + Math.abs(b1 - b2);
      complexity += diff;
    }

    // Calculate temporal complexity (motion)
    if (previousCanvas) {
      const prevCtx = previousCanvas.getContext('2d');
      if (prevCtx) {
        const prevImageData = prevCtx.getImageData(0, 0, canvas.width, canvas.height);
        const prevData = prevImageData.data;

        for (let i = 0; i < data.length; i += 4) {
          const rDiff = Math.abs(data[i] - prevData[i]);
          const gDiff = Math.abs(data[i + 1] - prevData[i + 1]);
          const bDiff = Math.abs(data[i + 2] - prevData[i + 2]);
          
          motionComplexity += rDiff + gDiff + bDiff;
        }
      }
    }

    // Normalize scores
    const pixelCount = data.length / 4;
    const spatialScore = complexity / (pixelCount * 255 * 3);
    const temporalScore = motionComplexity / (pixelCount * 255 * 3);

    return (spatialScore + temporalScore) / 2;
  }

  /**
   * Detect scene changes in video
   */
  static detectSceneChange(
    currentFrame: ImageData,
    previousFrame: ImageData,
    threshold: number = 0.3
  ): boolean {
    if (currentFrame.data.length !== previousFrame.data.length) {
      return true;
    }

    let totalDiff = 0;
    const data1 = currentFrame.data;
    const data2 = previousFrame.data;

    for (let i = 0; i < data1.length; i += 4) {
      const rDiff = Math.abs(data1[i] - data2[i]);
      const gDiff = Math.abs(data1[i + 1] - data2[i + 1]);
      const bDiff = Math.abs(data1[i + 2] - data2[i + 2]);
      
      totalDiff += (rDiff + gDiff + bDiff) / 3;
    }

    const avgDiff = totalDiff / (data1.length / 4);
    const normalizedDiff = avgDiff / 255;

    return normalizedDiff > threshold;
  }

  /**
   * Convert color format
   */
  static rgbToHex(r: number, g: number, b: number): string {
    return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
  }

  static hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  /**
   * Calculate greatest common divisor
   */
  private static greatestCommonDivisor(a: number, b: number): number {
    return b === 0 ? a : this.greatestCommonDivisor(b, a % b);
  }

  /**
   * Clamp value between min and max
   */
  static clamp(value: number, min: number, max: number): number {
    return Math.min(Math.max(value, min), max);
  }

  /**
   * Linear interpolation
   */
  static lerp(start: number, end: number, factor: number): number {
    return start + (end - start) * factor;
  }

  /**
   * Smooth step interpolation
   */
  static smoothStep(start: number, end: number, factor: number): number {
    const t = this.clamp((factor - start) / (end - start), 0, 1);
    return t * t * (3 - 2 * t);
  }

  /**
   * Convert degrees to radians
   */
  static degToRad(degrees: number): number {
    return degrees * Math.PI / 180;
  }

  /**
   * Convert radians to degrees
   */
  static radToDeg(radians: number): number {
    return radians * 180 / Math.PI;
  }

  /**
   * Generate unique ID
   */
  static generateId(prefix: string = 'id'): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Deep clone object
   */
  static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime()) as unknown as T;
    }

    if (obj instanceof Array) {
      return obj.map(item => this.deepClone(item)) as unknown as T;
    }

    if (typeof obj === 'object') {
      const cloned = {} as T;
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          cloned[key] = this.deepClone(obj[key]);
        }
      }
      return cloned;
    }

    return obj;
  }

  /**
   * Debounce function
   */
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

  /**
   * Throttle function
   */
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
}