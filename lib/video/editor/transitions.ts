import { Transition, TransitionType, EasingFunction } from '../types/video-types';

/**
 * Video Transitions Library
 * Provides pre-built transitions and transition utilities
 */

export interface TransitionPreset {
  name: string;
  type: TransitionType;
  description: string;
  parameters: Record<string, any>;
  duration: number;
  category: 'fade' | 'slide' | 'zoom' | 'rotate' | 'creative';
  preview?: string; // Base64 preview image or video
}

export class TransitionsLibrary {
  private static presets: TransitionPreset[] = [
    // Fade Transitions
    {
      name: 'Fade In/Out',
      type: 'fade',
      description: 'Classic fade transition',
      category: 'fade',
      duration: 1.0,
      parameters: {
        type: 'cross',
        curve: 'linear',
      },
    },
    {
      name: 'Fade to Black',
      type: 'fade',
      description: 'Fade through black',
      category: 'fade',
      duration: 1.5,
      parameters: {
        type: 'through-black',
        holdDuration: 0.2,
      },
    },
    {
      name: 'Fade to White',
      type: 'fade',
      description: 'Fade through white',
      category: 'fade',
      duration: 1.5,
      parameters: {
        type: 'through-white',
        holdDuration: 0.2,
      },
    },
    {
      name: 'Dissolve',
      type: 'dissolve',
      description: 'Smooth dissolve transition',
      category: 'fade',
      duration: 2.0,
      parameters: {
        softness: 0.5,
        pattern: 'random',
      },
    },

    // Slide Transitions
    {
      name: 'Slide Left',
      type: 'slide',
      description: 'Slide from right to left',
      category: 'slide',
      duration: 1.0,
      parameters: {
        direction: 'left',
        easing: 'ease-out',
      },
    },
    {
      name: 'Slide Right',
      type: 'slide',
      description: 'Slide from left to right',
      category: 'slide',
      duration: 1.0,
      parameters: {
        direction: 'right',
        easing: 'ease-out',
      },
    },
    {
      name: 'Slide Up',
      type: 'slide',
      description: 'Slide from bottom to top',
      category: 'slide',
      duration: 1.0,
      parameters: {
        direction: 'up',
        easing: 'ease-out',
      },
    },
    {
      name: 'Slide Down',
      type: 'slide',
      description: 'Slide from top to bottom',
      category: 'slide',
      duration: 1.0,
      parameters: {
        direction: 'down',
        easing: 'ease-out',
      },
    },

    // Push Transitions
    {
      name: 'Push Left',
      type: 'push',
      description: 'Push current clip to the left',
      category: 'slide',
      duration: 1.2,
      parameters: {
        direction: 'left',
        easing: 'ease-in-out',
      },
    },
    {
      name: 'Push Right',
      type: 'push',
      description: 'Push current clip to the right',
      category: 'slide',
      duration: 1.2,
      parameters: {
        direction: 'right',
        easing: 'ease-in-out',
      },
    },

    // Wipe Transitions
    {
      name: 'Wipe Left',
      type: 'wipe',
      description: 'Wipe from right to left',
      category: 'slide',
      duration: 1.0,
      parameters: {
        direction: 'left',
        softness: 0.1,
      },
    },
    {
      name: 'Wipe Right',
      type: 'wipe',
      description: 'Wipe from left to right',
      category: 'slide',
      duration: 1.0,
      parameters: {
        direction: 'right',
        softness: 0.1,
      },
    },
    {
      name: 'Wipe Up',
      type: 'wipe',
      description: 'Wipe from bottom to top',
      category: 'slide',
      duration: 1.0,
      parameters: {
        direction: 'up',
        softness: 0.1,
      },
    },
    {
      name: 'Wipe Down',
      type: 'wipe',
      description: 'Wipe from top to bottom',
      category: 'slide',
      duration: 1.0,
      parameters: {
        direction: 'down',
        softness: 0.1,
      },
    },

    // Zoom Transitions
    {
      name: 'Zoom In',
      type: 'zoom',
      description: 'Zoom into the next clip',
      category: 'zoom',
      duration: 1.5,
      parameters: {
        type: 'in',
        centerX: 0.5,
        centerY: 0.5,
        maxScale: 2.0,
      },
    },
    {
      name: 'Zoom Out',
      type: 'zoom',
      description: 'Zoom out from current clip',
      category: 'zoom',
      duration: 1.5,
      parameters: {
        type: 'out',
        centerX: 0.5,
        centerY: 0.5,
        maxScale: 2.0,
      },
    },

    // Rotation Transitions
    {
      name: 'Rotate Left',
      type: 'rotate',
      description: 'Rotate counterclockwise',
      category: 'rotate',
      duration: 1.0,
      parameters: {
        degrees: -90,
        centerX: 0.5,
        centerY: 0.5,
        easing: 'ease-in-out',
      },
    },
    {
      name: 'Rotate Right',
      type: 'rotate',
      description: 'Rotate clockwise',
      category: 'rotate',
      duration: 1.0,
      parameters: {
        degrees: 90,
        centerX: 0.5,
        centerY: 0.5,
        easing: 'ease-in-out',
      },
    },
    {
      name: 'Spin',
      type: 'rotate',
      description: 'Full 360 degree spin',
      category: 'rotate',
      duration: 2.0,
      parameters: {
        degrees: 360,
        centerX: 0.5,
        centerY: 0.5,
        easing: 'linear',
      },
    },

    // Creative Transitions
    {
      name: 'Iris In',
      type: 'iris',
      description: 'Circular iris opening',
      category: 'creative',
      duration: 1.0,
      parameters: {
        type: 'in',
        centerX: 0.5,
        centerY: 0.5,
        shape: 'circle',
      },
    },
    {
      name: 'Iris Out',
      type: 'iris',
      description: 'Circular iris closing',
      category: 'creative',
      duration: 1.0,
      parameters: {
        type: 'out',
        centerX: 0.5,
        centerY: 0.5,
        shape: 'circle',
      },
    },
    {
      name: 'Flip Horizontal',
      type: 'flip',
      description: 'Horizontal flip transition',
      category: 'creative',
      duration: 1.0,
      parameters: {
        axis: 'horizontal',
        perspective: 1000,
      },
    },
    {
      name: 'Flip Vertical',
      type: 'flip',
      description: 'Vertical flip transition',
      category: 'creative',
      duration: 1.0,
      parameters: {
        axis: 'vertical',
        perspective: 1000,
      },
    },
    {
      name: 'Cube Left',
      type: 'cube',
      description: '3D cube rotation to left',
      category: 'creative',
      duration: 1.5,
      parameters: {
        direction: 'left',
        perspective: 1200,
      },
    },
    {
      name: 'Cube Right',
      type: 'cube',
      description: '3D cube rotation to right',
      category: 'creative',
      duration: 1.5,
      parameters: {
        direction: 'right',
        perspective: 1200,
      },
    },
    {
      name: 'Page Turn',
      type: 'page-turn',
      description: 'Page turning effect',
      category: 'creative',
      duration: 2.0,
      parameters: {
        direction: 'right',
        curl: 0.3,
        shadow: true,
      },
    },
  ];

  static getPresets(): TransitionPreset[] {
    return [...this.presets];
  }

  static getPresetsByCategory(category: TransitionPreset['category']): TransitionPreset[] {
    return this.presets.filter(preset => preset.category === category);
  }

  static getPreset(name: string): TransitionPreset | undefined {
    return this.presets.find(preset => preset.name === name);
  }

  static createTransition(preset: TransitionPreset, customDuration?: number): Transition {
    return {
      id: `transition-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: preset.type,
      duration: customDuration || preset.duration,
      parameters: { ...preset.parameters },
      easing: 'ease-in-out',
    };
  }

  static addCustomPreset(preset: TransitionPreset): void {
    this.presets.push(preset);
  }
}

/**
 * Transition Parameter Definitions
 */
export interface TransitionParameterDefinition {
  name: string;
  type: 'number' | 'boolean' | 'string' | 'color' | 'select';
  min?: number;
  max?: number;
  step?: number;
  default: any;
  options?: string[];
  description: string;
}

export class TransitionParameters {
  private static definitions: Record<TransitionType, TransitionParameterDefinition[]> = {
    fade: [
      {
        name: 'type',
        type: 'select',
        options: ['cross', 'through-black', 'through-white'],
        default: 'cross',
        description: 'Type of fade transition',
      },
      {
        name: 'curve',
        type: 'select',
        options: ['linear', 'ease-in', 'ease-out', 'ease-in-out'],
        default: 'linear',
        description: 'Fade curve',
      },
    ],
    dissolve: [
      {
        name: 'softness',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5,
        description: 'Edge softness',
      },
      {
        name: 'pattern',
        type: 'select',
        options: ['random', 'noise', 'grid'],
        default: 'random',
        description: 'Dissolve pattern',
      },
    ],
    wipe: [
      {
        name: 'direction',
        type: 'select',
        options: ['left', 'right', 'up', 'down'],
        default: 'left',
        description: 'Wipe direction',
      },
      {
        name: 'softness',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.1,
        description: 'Edge softness',
      },
      {
        name: 'angle',
        type: 'number',
        min: -45,
        max: 45,
        step: 1,
        default: 0,
        description: 'Wipe angle in degrees',
      },
    ],
    slide: [
      {
        name: 'direction',
        type: 'select',
        options: ['left', 'right', 'up', 'down'],
        default: 'left',
        description: 'Slide direction',
      },
      {
        name: 'easing',
        type: 'select',
        options: ['linear', 'ease-in', 'ease-out', 'ease-in-out'],
        default: 'ease-out',
        description: 'Easing function',
      },
    ],
    push: [
      {
        name: 'direction',
        type: 'select',
        options: ['left', 'right', 'up', 'down'],
        default: 'left',
        description: 'Push direction',
      },
      {
        name: 'easing',
        type: 'select',
        options: ['linear', 'ease-in', 'ease-out', 'ease-in-out'],
        default: 'ease-in-out',
        description: 'Easing function',
      },
    ],
    iris: [
      {
        name: 'type',
        type: 'select',
        options: ['in', 'out'],
        default: 'in',
        description: 'Iris direction',
      },
      {
        name: 'centerX',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5,
        description: 'Center X position (0-1)',
      },
      {
        name: 'centerY',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5,
        description: 'Center Y position (0-1)',
      },
      {
        name: 'shape',
        type: 'select',
        options: ['circle', 'square', 'diamond'],
        default: 'circle',
        description: 'Iris shape',
      },
    ],
    zoom: [
      {
        name: 'type',
        type: 'select',
        options: ['in', 'out'],
        default: 'in',
        description: 'Zoom direction',
      },
      {
        name: 'centerX',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5,
        description: 'Zoom center X (0-1)',
      },
      {
        name: 'centerY',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5,
        description: 'Zoom center Y (0-1)',
      },
      {
        name: 'maxScale',
        type: 'number',
        min: 1.1,
        max: 5,
        step: 0.1,
        default: 2.0,
        description: 'Maximum zoom scale',
      },
    ],
    rotate: [
      {
        name: 'degrees',
        type: 'number',
        min: -360,
        max: 360,
        step: 1,
        default: 90,
        description: 'Rotation degrees',
      },
      {
        name: 'centerX',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5,
        description: 'Rotation center X (0-1)',
      },
      {
        name: 'centerY',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5,
        description: 'Rotation center Y (0-1)',
      },
    ],
    flip: [
      {
        name: 'axis',
        type: 'select',
        options: ['horizontal', 'vertical'],
        default: 'horizontal',
        description: 'Flip axis',
      },
      {
        name: 'perspective',
        type: 'number',
        min: 500,
        max: 2000,
        step: 50,
        default: 1000,
        description: '3D perspective distance',
      },
    ],
    cube: [
      {
        name: 'direction',
        type: 'select',
        options: ['left', 'right', 'up', 'down'],
        default: 'left',
        description: 'Cube rotation direction',
      },
      {
        name: 'perspective',
        type: 'number',
        min: 800,
        max: 2000,
        step: 50,
        default: 1200,
        description: '3D perspective distance',
      },
    ],
    'page-turn': [
      {
        name: 'direction',
        type: 'select',
        options: ['left', 'right'],
        default: 'right',
        description: 'Page turn direction',
      },
      {
        name: 'curl',
        type: 'number',
        min: 0.1,
        max: 0.5,
        step: 0.01,
        default: 0.3,
        description: 'Page curl amount',
      },
      {
        name: 'shadow',
        type: 'boolean',
        default: true,
        description: 'Enable shadow effect',
      },
    ],
  };

  static getParameterDefinitions(transitionType: TransitionType): TransitionParameterDefinition[] {
    return this.definitions[transitionType] || [];
  }

  static validateParameters(transitionType: TransitionType, parameters: Record<string, any>): boolean {
    const definitions = this.getParameterDefinitions(transitionType);
    
    for (const def of definitions) {
      const value = parameters[def.name];
      
      if (value === undefined) {
        parameters[def.name] = def.default;
        continue;
      }

      // Type validation
      if (def.type === 'number' && typeof value !== 'number') {
        return false;
      }
      if (def.type === 'boolean' && typeof value !== 'boolean') {
        return false;
      }
      if (def.type === 'string' && typeof value !== 'string') {
        return false;
      }

      // Range validation for numbers
      if (def.type === 'number') {
        if (def.min !== undefined && value < def.min) {
          return false;
        }
        if (def.max !== undefined && value > def.max) {
          return false;
        }
      }

      // Options validation for select
      if (def.type === 'select' && def.options && !def.options.includes(value)) {
        return false;
      }
    }

    return true;
  }

  static sanitizeParameters(transitionType: TransitionType, parameters: Record<string, any>): Record<string, any> {
    const definitions = this.getParameterDefinitions(transitionType);
    const sanitized: Record<string, any> = {};

    for (const def of definitions) {
      let value = parameters[def.name];
      
      if (value === undefined) {
        value = def.default;
      }

      // Clamp numbers to range
      if (def.type === 'number') {
        if (def.min !== undefined) {
          value = Math.max(value, def.min);
        }
        if (def.max !== undefined) {
          value = Math.min(value, def.max);
        }
      }

      // Validate select options
      if (def.type === 'select' && def.options && !def.options.includes(value)) {
        value = def.default;
      }

      sanitized[def.name] = value;
    }

    return sanitized;
  }
}

/**
 * Transition Animation System
 */
export class TransitionAnimator {
  static calculateProgress(
    startTime: number,
    duration: number,
    currentTime: number,
    easing: EasingFunction = 'linear'
  ): number {
    if (currentTime <= startTime) return 0;
    if (currentTime >= startTime + duration) return 1;

    const rawProgress = (currentTime - startTime) / duration;
    return this.applyEasing(rawProgress, easing);
  }

  static applyEasing(progress: number, easing: EasingFunction): number {
    switch (easing) {
      case 'ease-in':
        return progress * progress;
      case 'ease-out':
        return 1 - Math.pow(1 - progress, 2);
      case 'ease-in-out':
        return progress < 0.5 
          ? 2 * progress * progress 
          : 1 - Math.pow(-2 * progress + 2, 2) / 2;
      case 'cubic-bezier':
        // Simplified cubic-bezier (0.25, 0.1, 0.25, 1.0)
        return progress * progress * (3 - 2 * progress);
      case 'linear':
      default:
        return progress;
    }
  }

  static generateTransitionFrames(
    transition: Transition,
    fps: number = 30
  ): Array<{ time: number; progress: number; parameters: Record<string, any> }> {
    const frames: Array<{ time: number; progress: number; parameters: Record<string, any> }> = [];
    const frameCount = Math.ceil(transition.duration * fps);
    const frameTime = transition.duration / frameCount;

    for (let i = 0; i <= frameCount; i++) {
      const time = i * frameTime;
      const progress = this.calculateProgress(0, transition.duration, time, transition.easing);
      
      frames.push({
        time,
        progress,
        parameters: this.interpolateTransitionParameters(transition, progress),
      });
    }

    return frames;
  }

  private static interpolateTransitionParameters(
    transition: Transition,
    progress: number
  ): Record<string, any> {
    const interpolated: Record<string, any> = { ...transition.parameters };
    
    // Add progress-based parameters for common transition types
    switch (transition.type) {
      case 'fade':
        interpolated.opacity = progress;
        break;
      case 'slide':
        const direction = transition.parameters.direction || 'left';
        const distance = progress;
        
        switch (direction) {
          case 'left':
            interpolated.translateX = -distance;
            break;
          case 'right':
            interpolated.translateX = distance;
            break;
          case 'up':
            interpolated.translateY = -distance;
            break;
          case 'down':
            interpolated.translateY = distance;
            break;
        }
        break;
      case 'zoom':
        const zoomType = transition.parameters.type || 'in';
        const maxScale = transition.parameters.maxScale || 2.0;
        
        if (zoomType === 'in') {
          interpolated.scale = 1 + (maxScale - 1) * progress;
        } else {
          interpolated.scale = maxScale - (maxScale - 1) * progress;
        }
        break;
      case 'rotate':
        const degrees = transition.parameters.degrees || 90;
        interpolated.rotation = degrees * progress;
        break;
    }

    return interpolated;
  }
}

/**
 * Transition Renderer
 * Handles the actual rendering of transitions between clips
 */
export class TransitionRenderer {
  static renderTransition(
    ctx: CanvasRenderingContext2D | OffscreenCanvasRenderingContext2D,
    fromFrame: ImageData | HTMLCanvasElement | HTMLVideoElement,
    toFrame: ImageData | HTMLCanvasElement | HTMLVideoElement,
    transition: Transition,
    progress: number
  ): void {
    const canvas = ctx.canvas;
    const width = canvas.width;
    const height = canvas.height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    switch (transition.type) {
      case 'fade':
        this.renderFadeTransition(ctx, fromFrame, toFrame, transition, progress);
        break;
      case 'slide':
        this.renderSlideTransition(ctx, fromFrame, toFrame, transition, progress);
        break;
      case 'wipe':
        this.renderWipeTransition(ctx, fromFrame, toFrame, transition, progress);
        break;
      case 'zoom':
        this.renderZoomTransition(ctx, fromFrame, toFrame, transition, progress);
        break;
      case 'rotate':
        this.renderRotateTransition(ctx, fromFrame, toFrame, transition, progress);
        break;
      default:
        // Fallback to simple fade
        this.renderFadeTransition(ctx, fromFrame, toFrame, transition, progress);
        break;
    }
  }

  private static renderFadeTransition(
    ctx: CanvasRenderingContext2D | OffscreenCanvasRenderingContext2D,
    fromFrame: any,
    toFrame: any,
    transition: Transition,
    progress: number
  ): void {
    const width = ctx.canvas.width;
    const height = ctx.canvas.height;

    // Draw from frame with decreasing opacity
    ctx.globalAlpha = 1 - progress;
    ctx.drawImage(fromFrame, 0, 0, width, height);

    // Draw to frame with increasing opacity
    ctx.globalAlpha = progress;
    ctx.drawImage(toFrame, 0, 0, width, height);

    // Reset alpha
    ctx.globalAlpha = 1;
  }

  private static renderSlideTransition(
    ctx: CanvasRenderingContext2D | OffscreenCanvasRenderingContext2D,
    fromFrame: any,
    toFrame: any,
    transition: Transition,
    progress: number
  ): void {
    const width = ctx.canvas.width;
    const height = ctx.canvas.height;
    const direction = transition.parameters.direction || 'left';

    let fromX = 0, fromY = 0, toX = 0, toY = 0;

    switch (direction) {
      case 'left':
        fromX = -width * progress;
        toX = width * (1 - progress);
        break;
      case 'right':
        fromX = width * progress;
        toX = -width * (1 - progress);
        break;
      case 'up':
        fromY = -height * progress;
        toY = height * (1 - progress);
        break;
      case 'down':
        fromY = height * progress;
        toY = -height * (1 - progress);
        break;
    }

    // Draw frames at calculated positions
    ctx.drawImage(fromFrame, fromX, fromY, width, height);
    ctx.drawImage(toFrame, toX, toY, width, height);
  }

  private static renderWipeTransition(
    ctx: CanvasRenderingContext2D | OffscreenCanvasRenderingContext2D,
    fromFrame: any,
    toFrame: any,
    transition: Transition,
    progress: number
  ): void {
    const width = ctx.canvas.width;
    const height = ctx.canvas.height;
    const direction = transition.parameters.direction || 'left';

    // Draw from frame
    ctx.drawImage(fromFrame, 0, 0, width, height);

    // Create clipping path for wipe
    ctx.save();
    ctx.beginPath();

    switch (direction) {
      case 'left':
        ctx.rect(0, 0, width * progress, height);
        break;
      case 'right':
        ctx.rect(width * (1 - progress), 0, width * progress, height);
        break;
      case 'up':
        ctx.rect(0, 0, width, height * progress);
        break;
      case 'down':
        ctx.rect(0, height * (1 - progress), width, height * progress);
        break;
    }

    ctx.clip();
    ctx.drawImage(toFrame, 0, 0, width, height);
    ctx.restore();
  }

  private static renderZoomTransition(
    ctx: CanvasRenderingContext2D | OffscreenCanvasRenderingContext2D,
    fromFrame: any,
    toFrame: any,
    transition: Transition,
    progress: number
  ): void {
    const width = ctx.canvas.width;
    const height = ctx.canvas.height;
    const centerX = (transition.parameters.centerX || 0.5) * width;
    const centerY = (transition.parameters.centerY || 0.5) * height;
    const maxScale = transition.parameters.maxScale || 2.0;
    const zoomType = transition.parameters.type || 'in';

    ctx.save();

    if (zoomType === 'in') {
      // Zoom into to-frame
      ctx.drawImage(fromFrame, 0, 0, width, height);
      
      const scale = 1 + (maxScale - 1) * progress;
      ctx.translate(centerX, centerY);
      ctx.scale(scale, scale);
      ctx.translate(-centerX, -centerY);
      ctx.globalAlpha = progress;
      ctx.drawImage(toFrame, 0, 0, width, height);
    } else {
      // Zoom out from from-frame
      const scale = maxScale - (maxScale - 1) * progress;
      ctx.translate(centerX, centerY);
      ctx.scale(scale, scale);
      ctx.translate(-centerX, -centerY);
      ctx.drawImage(fromFrame, 0, 0, width, height);
      
      ctx.restore();
      ctx.globalAlpha = progress;
      ctx.drawImage(toFrame, 0, 0, width, height);
    }

    ctx.restore();
  }

  private static renderRotateTransition(
    ctx: CanvasRenderingContext2D | OffscreenCanvasRenderingContext2D,
    fromFrame: any,
    toFrame: any,
    transition: Transition,
    progress: number
  ): void {
    const width = ctx.canvas.width;
    const height = ctx.canvas.height;
    const centerX = (transition.parameters.centerX || 0.5) * width;
    const centerY = (transition.parameters.centerY || 0.5) * height;
    const degrees = transition.parameters.degrees || 90;
    const rotation = (degrees * Math.PI / 180) * progress;

    // Draw to frame as background
    ctx.drawImage(toFrame, 0, 0, width, height);

    // Draw rotating from frame
    ctx.save();
    ctx.translate(centerX, centerY);
    ctx.rotate(rotation);
    ctx.translate(-centerX, -centerY);
    ctx.globalAlpha = 1 - progress;
    ctx.drawImage(fromFrame, 0, 0, width, height);
    ctx.restore();
  }
}