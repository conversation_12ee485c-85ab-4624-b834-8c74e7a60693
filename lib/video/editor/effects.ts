import { Effect, EffectType } from '../types/video-types';

/**
 * Video Effects Library
 * Provides pre-built effects and effect utilities
 */

export interface EffectPreset {
  name: string;
  type: EffectType;
  description: string;
  parameters: Record<string, any>;
  category: 'color' | 'blur' | 'transform' | 'artistic' | 'correction';
  preview?: string; // Base64 preview image
}

export class EffectsLibrary {
  private static presets: EffectPreset[] = [
    // Color Effects
    {
      name: 'Vintage',
      type: 'sepia',
      description: 'Classic vintage sepia tone effect',
      category: 'color',
      parameters: {
        intensity: 0.8,
        warmth: 1.2,
      },
    },
    {
      name: 'Black & White',
      type: 'grayscale',
      description: 'Convert to black and white',
      category: 'color',
      parameters: {
        intensity: 1.0,
      },
    },
    {
      name: 'High Contrast',
      type: 'contrast',
      description: 'Increase contrast for dramatic effect',
      category: 'color',
      parameters: {
        value: 1.5,
      },
    },
    {
      name: 'Bright & Vibrant',
      type: 'brightness',
      description: 'Brighten and enhance colors',
      category: 'color',
      parameters: {
        brightness: 1.2,
        saturation: 1.3,
      },
    },
    {
      name: 'Cool Tone',
      type: 'hue',
      description: 'Apply cool blue tones',
      category: 'color',
      parameters: {
        hue: 200,
        intensity: 0.3,
      },
    },
    {
      name: 'Warm Tone',
      type: 'hue',
      description: 'Apply warm orange tones',
      category: 'color',
      parameters: {
        hue: 30,
        intensity: 0.3,
      },
    },

    // Blur Effects
    {
      name: 'Soft Blur',
      type: 'blur',
      description: 'Gentle blur for dreamy effect',
      category: 'blur',
      parameters: {
        radius: 3,
        type: 'gaussian',
      },
    },
    {
      name: 'Motion Blur',
      type: 'blur',
      description: 'Directional motion blur',
      category: 'blur',
      parameters: {
        radius: 8,
        angle: 0,
        type: 'motion',
      },
    },
    {
      name: 'Radial Blur',
      type: 'blur',
      description: 'Blur radiating from center',
      category: 'blur',
      parameters: {
        radius: 10,
        centerX: 0.5,
        centerY: 0.5,
        type: 'radial',
      },
    },

    // Transform Effects
    {
      name: 'Zoom In',
      type: 'scale',
      description: 'Gradual zoom in effect',
      category: 'transform',
      parameters: {
        startScale: 1.0,
        endScale: 1.2,
        easing: 'ease-in-out',
      },
    },
    {
      name: 'Zoom Out',
      type: 'scale',
      description: 'Gradual zoom out effect',
      category: 'transform',
      parameters: {
        startScale: 1.2,
        endScale: 1.0,
        easing: 'ease-in-out',
      },
    },
    {
      name: 'Rotate 360',
      type: 'rotate',
      description: 'Full 360 degree rotation',
      category: 'transform',
      parameters: {
        degrees: 360,
        easing: 'linear',
      },
    },
    {
      name: 'Shake',
      type: 'translate',
      description: 'Camera shake effect',
      category: 'transform',
      parameters: {
        intensity: 5,
        frequency: 10,
        decay: 0.9,
      },
    },

    // Artistic Effects
    {
      name: 'Film Grain',
      type: 'noise-reduction',
      description: 'Add vintage film grain',
      category: 'artistic',
      parameters: {
        amount: 0.3,
        size: 1.0,
        type: 'grain',
        invert: true, // Add noise instead of reducing
      },
    },
    {
      name: 'Vignette',
      type: 'opacity',
      description: 'Dark edges vignette effect',
      category: 'artistic',
      parameters: {
        type: 'vignette',
        intensity: 0.5,
        size: 0.8,
      },
    },

    // Color Correction
    {
      name: 'Auto Color Correct',
      type: 'color-correction',
      description: 'Automatic color correction',
      category: 'correction',
      parameters: {
        auto: true,
        shadows: 0,
        midtones: 0,
        highlights: 0,
      },
    },
    {
      name: 'Green Screen',
      type: 'chroma-key',
      description: 'Remove green background',
      category: 'correction',
      parameters: {
        color: '#00ff00',
        tolerance: 0.3,
        softness: 0.1,
      },
    },
  ];

  static getPresets(): EffectPreset[] {
    return [...this.presets];
  }

  static getPresetsByCategory(category: EffectPreset['category']): EffectPreset[] {
    return this.presets.filter(preset => preset.category === category);
  }

  static getPreset(name: string): EffectPreset | undefined {
    return this.presets.find(preset => preset.name === name);
  }

  static createEffect(preset: EffectPreset, startTime: number = 0, endTime: number = 10): Effect {
    return {
      id: `effect-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: preset.type,
      name: preset.name,
      parameters: { ...preset.parameters },
      startTime,
      endTime,
      enabled: true,
    };
  }

  static addCustomPreset(preset: EffectPreset): void {
    this.presets.push(preset);
  }
}

/**
 * Effect Parameter Definitions
 * Defines the parameters and their constraints for each effect type
 */
export interface EffectParameterDefinition {
  name: string;
  type: 'number' | 'boolean' | 'string' | 'color' | 'select';
  min?: number;
  max?: number;
  step?: number;
  default: any;
  options?: string[];
  description: string;
}

export class EffectParameters {
  private static definitions: Record<EffectType, EffectParameterDefinition[]> = {
    blur: [
      {
        name: 'radius',
        type: 'number',
        min: 0,
        max: 50,
        step: 0.1,
        default: 5,
        description: 'Blur radius in pixels',
      },
      {
        name: 'type',
        type: 'select',
        options: ['gaussian', 'motion', 'radial'],
        default: 'gaussian',
        description: 'Type of blur effect',
      },
    ],
    brightness: [
      {
        name: 'value',
        type: 'number',
        min: 0,
        max: 3,
        step: 0.01,
        default: 1,
        description: 'Brightness multiplier',
      },
    ],
    contrast: [
      {
        name: 'value',
        type: 'number',
        min: 0,
        max: 3,
        step: 0.01,
        default: 1,
        description: 'Contrast multiplier',
      },
    ],
    saturation: [
      {
        name: 'value',
        type: 'number',
        min: 0,
        max: 3,
        step: 0.01,
        default: 1,
        description: 'Saturation multiplier',
      },
    ],
    hue: [
      {
        name: 'degrees',
        type: 'number',
        min: -180,
        max: 180,
        step: 1,
        default: 0,
        description: 'Hue rotation in degrees',
      },
    ],
    sepia: [
      {
        name: 'intensity',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 1,
        description: 'Sepia effect intensity',
      },
    ],
    grayscale: [
      {
        name: 'intensity',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 1,
        description: 'Grayscale effect intensity',
      },
    ],
    invert: [
      {
        name: 'intensity',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 1,
        description: 'Invert effect intensity',
      },
    ],
    opacity: [
      {
        name: 'value',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 1,
        description: 'Opacity value',
      },
    ],
    scale: [
      {
        name: 'value',
        type: 'number',
        min: 0.1,
        max: 5,
        step: 0.01,
        default: 1,
        description: 'Scale multiplier',
      },
      {
        name: 'centerX',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5,
        description: 'Scale center X (0-1)',
      },
      {
        name: 'centerY',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5,
        description: 'Scale center Y (0-1)',
      },
    ],
    rotate: [
      {
        name: 'degrees',
        type: 'number',
        min: -360,
        max: 360,
        step: 1,
        default: 0,
        description: 'Rotation in degrees',
      },
      {
        name: 'centerX',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5,
        description: 'Rotation center X (0-1)',
      },
      {
        name: 'centerY',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5,
        description: 'Rotation center Y (0-1)',
      },
    ],
    translate: [
      {
        name: 'x',
        type: 'number',
        min: -1000,
        max: 1000,
        step: 1,
        default: 0,
        description: 'X translation in pixels',
      },
      {
        name: 'y',
        type: 'number',
        min: -1000,
        max: 1000,
        step: 1,
        default: 0,
        description: 'Y translation in pixels',
      },
    ],
    crop: [
      {
        name: 'x',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0,
        description: 'Crop X position (0-1)',
      },
      {
        name: 'y',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0,
        description: 'Crop Y position (0-1)',
      },
      {
        name: 'width',
        type: 'number',
        min: 0.1,
        max: 1,
        step: 0.01,
        default: 1,
        description: 'Crop width (0-1)',
      },
      {
        name: 'height',
        type: 'number',
        min: 0.1,
        max: 1,
        step: 0.01,
        default: 1,
        description: 'Crop height (0-1)',
      },
    ],
    fade: [
      {
        name: 'type',
        type: 'select',
        options: ['in', 'out', 'in-out'],
        default: 'in',
        description: 'Fade type',
      },
      {
        name: 'duration',
        type: 'number',
        min: 0.1,
        max: 10,
        step: 0.1,
        default: 1,
        description: 'Fade duration in seconds',
      },
    ],
    zoom: [
      {
        name: 'startScale',
        type: 'number',
        min: 0.1,
        max: 5,
        step: 0.01,
        default: 1,
        description: 'Starting scale',
      },
      {
        name: 'endScale',
        type: 'number',
        min: 0.1,
        max: 5,
        step: 0.01,
        default: 1.2,
        description: 'Ending scale',
      },
    ],
    stabilization: [
      {
        name: 'strength',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5,
        description: 'Stabilization strength',
      },
      {
        name: 'smoothness',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5,
        description: 'Smoothness factor',
      },
    ],
    'noise-reduction': [
      {
        name: 'strength',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5,
        description: 'Noise reduction strength',
      },
      {
        name: 'preserveDetails',
        type: 'boolean',
        default: true,
        description: 'Preserve fine details',
      },
    ],
    'color-correction': [
      {
        name: 'shadows',
        type: 'number',
        min: -100,
        max: 100,
        step: 1,
        default: 0,
        description: 'Shadow adjustment',
      },
      {
        name: 'midtones',
        type: 'number',
        min: -100,
        max: 100,
        step: 1,
        default: 0,
        description: 'Midtone adjustment',
      },
      {
        name: 'highlights',
        type: 'number',
        min: -100,
        max: 100,
        step: 1,
        default: 0,
        description: 'Highlight adjustment',
      },
      {
        name: 'temperature',
        type: 'number',
        min: -100,
        max: 100,
        step: 1,
        default: 0,
        description: 'Color temperature',
      },
      {
        name: 'tint',
        type: 'number',
        min: -100,
        max: 100,
        step: 1,
        default: 0,
        description: 'Color tint',
      },
    ],
    'chroma-key': [
      {
        name: 'color',
        type: 'color',
        default: '#00ff00',
        description: 'Key color to remove',
      },
      {
        name: 'tolerance',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.3,
        description: 'Color tolerance',
      },
      {
        name: 'softness',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.1,
        description: 'Edge softness',
      },
      {
        name: 'spillSuppression',
        type: 'number',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5,
        description: 'Spill suppression',
      },
    ],
  };

  static getParameterDefinitions(effectType: EffectType): EffectParameterDefinition[] {
    return this.definitions[effectType] || [];
  }

  static validateParameters(effectType: EffectType, parameters: Record<string, any>): boolean {
    const definitions = this.getParameterDefinitions(effectType);
    
    for (const def of definitions) {
      const value = parameters[def.name];
      
      if (value === undefined) {
        parameters[def.name] = def.default;
        continue;
      }

      // Type validation
      if (def.type === 'number' && typeof value !== 'number') {
        return false;
      }
      if (def.type === 'boolean' && typeof value !== 'boolean') {
        return false;
      }
      if (def.type === 'string' && typeof value !== 'string') {
        return false;
      }

      // Range validation for numbers
      if (def.type === 'number') {
        if (def.min !== undefined && value < def.min) {
          return false;
        }
        if (def.max !== undefined && value > def.max) {
          return false;
        }
      }

      // Options validation for select
      if (def.type === 'select' && def.options && !def.options.includes(value)) {
        return false;
      }
    }

    return true;
  }

  static sanitizeParameters(effectType: EffectType, parameters: Record<string, any>): Record<string, any> {
    const definitions = this.getParameterDefinitions(effectType);
    const sanitized: Record<string, any> = {};

    for (const def of definitions) {
      let value = parameters[def.name];
      
      if (value === undefined) {
        value = def.default;
      }

      // Clamp numbers to range
      if (def.type === 'number') {
        if (def.min !== undefined) {
          value = Math.max(value, def.min);
        }
        if (def.max !== undefined) {
          value = Math.min(value, def.max);
        }
      }

      // Validate select options
      if (def.type === 'select' && def.options && !def.options.includes(value)) {
        value = def.default;
      }

      sanitized[def.name] = value;
    }

    return sanitized;
  }
}

/**
 * Effect Animation System
 * Handles keyframe-based effect animations
 */
export interface EffectKeyframe {
  time: number; // Time in seconds relative to effect start
  parameters: Record<string, any>;
  easing?: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'cubic-bezier';
}

export class EffectAnimator {
  static interpolateParameters(
    keyframes: EffectKeyframe[],
    currentTime: number
  ): Record<string, any> {
    if (keyframes.length === 0) return {};
    if (keyframes.length === 1) return keyframes[0].parameters;

    // Sort keyframes by time
    const sortedKeyframes = [...keyframes].sort((a, b) => a.time - b.time);

    // Find surrounding keyframes
    let beforeKeyframe = sortedKeyframes[0];
    let afterKeyframe = sortedKeyframes[sortedKeyframes.length - 1];

    for (let i = 0; i < sortedKeyframes.length - 1; i++) {
      if (currentTime >= sortedKeyframes[i].time && currentTime <= sortedKeyframes[i + 1].time) {
        beforeKeyframe = sortedKeyframes[i];
        afterKeyframe = sortedKeyframes[i + 1];
        break;
      }
    }

    // If current time is before first keyframe or after last keyframe
    if (currentTime <= beforeKeyframe.time) {
      return beforeKeyframe.parameters;
    }
    if (currentTime >= afterKeyframe.time) {
      return afterKeyframe.parameters;
    }

    // Interpolate between keyframes
    const timeDiff = afterKeyframe.time - beforeKeyframe.time;
    const progress = (currentTime - beforeKeyframe.time) / timeDiff;
    
    const interpolated: Record<string, any> = {};
    const beforeParams = beforeKeyframe.parameters;
    const afterParams = afterKeyframe.parameters;

    // Get all parameter keys
    const allKeys = new Set([...Object.keys(beforeParams), ...Object.keys(afterParams)]);

    for (const key of allKeys) {
      const beforeValue = beforeParams[key];
      const afterValue = afterParams[key];

      if (typeof beforeValue === 'number' && typeof afterValue === 'number') {
        // Apply easing
        const easedProgress = this.applyEasing(progress, afterKeyframe.easing || 'linear');
        interpolated[key] = beforeValue + (afterValue - beforeValue) * easedProgress;
      } else {
        // For non-numeric values, use step interpolation
        interpolated[key] = progress < 0.5 ? beforeValue : afterValue;
      }
    }

    return interpolated;
  }

  private static applyEasing(progress: number, easing: string): number {
    switch (easing) {
      case 'ease-in':
        return progress * progress;
      case 'ease-out':
        return 1 - Math.pow(1 - progress, 2);
      case 'ease-in-out':
        return progress < 0.5 
          ? 2 * progress * progress 
          : 1 - Math.pow(-2 * progress + 2, 2) / 2;
      case 'linear':
      default:
        return progress;
    }
  }
}