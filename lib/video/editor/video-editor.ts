import { EventEmitter } from 'events';
import { 
  VideoSystemConfig, 
  VideoClip, 
  VideoEditorState, 
  EditorAction,
  Effect,
  Transition 
} from '../types/video-types';

/**
 * Video Editor
 * Handles video editing operations, state management, and undo/redo
 */
export class VideoEditor extends EventEmitter {
  private config: VideoSystemConfig;
  private state: VideoEditorState;
  private maxHistorySize: number = 100;

  constructor(config: VideoSystemConfig) {
    super();
    this.config = config;
    this.state = this.createInitialState();
  }

  private createInitialState(): VideoEditorState {
    return {
      timeline: {
        id: 'default',
        name: 'New Project',
        duration: 60,
        fps: 30,
        resolution: { width: 1920, height: 1080, aspectRatio: '16:9' },
        tracks: [],
        clips: [],
        createdAt: new Date(),
        modifiedAt: new Date(),
      },
      selectedClips: [],
      currentTime: 0,
      isPlaying: false,
      zoom: 1.0,
      snapToGrid: true,
      renderSettings: {
        resolution: { width: 1920, height: 1080, aspectRatio: '16:9' },
        fps: 30,
        bitrate: 5000000,
        codec: 'h264',
        format: 'mp4',
        quality: 'high',
        hardwareAcceleration: true,
      },
      history: [],
      historyIndex: -1,
    };
  }

  // Clip Management
  async addClip(clipData: Omit<VideoClip, 'id'>): Promise<VideoClip> {
    const clip: VideoClip = {
      ...clipData,
      id: `clip-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };

    const action: EditorAction = {
      id: `action-${Date.now()}`,
      type: 'ADD_CLIP',
      timestamp: new Date(),
      data: { clip },
      description: `Added clip: ${clip.type}`,
    };

    this.executeAction(action);
    this.emit('clip:added', { clip });
    
    return clip;
  }

  async removeClip(clipId: string): Promise<void> {
    const clip = this.state.timeline.clips.find(c => c.id === clipId);
    if (!clip) {
      throw new Error(`Clip with id ${clipId} not found`);
    }

    const action: EditorAction = {
      id: `action-${Date.now()}`,
      type: 'REMOVE_CLIP',
      timestamp: new Date(),
      data: { clipId, clip },
      description: `Removed clip: ${clip.type}`,
    };

    this.executeAction(action);
    this.emit('clip:removed', { clipId });
  }

  async updateClip(clipId: string, updates: Partial<VideoClip>): Promise<VideoClip> {
    const clipIndex = this.state.timeline.clips.findIndex(c => c.id === clipId);
    if (clipIndex === -1) {
      throw new Error(`Clip with id ${clipId} not found`);
    }

    const oldClip = { ...this.state.timeline.clips[clipIndex] };
    const newClip = { ...oldClip, ...updates };

    const action: EditorAction = {
      id: `action-${Date.now()}`,
      type: 'UPDATE_CLIP',
      timestamp: new Date(),
      data: { clipId, oldClip, newClip },
      description: `Updated clip: ${clipId}`,
    };

    this.executeAction(action);
    this.emit('clip:updated', { clip: newClip });
    
    return newClip;
  }

  async duplicateClip(clipId: string): Promise<VideoClip> {
    const originalClip = this.state.timeline.clips.find(c => c.id === clipId);
    if (!originalClip) {
      throw new Error(`Clip with id ${clipId} not found`);
    }

    const duplicatedClip: VideoClip = {
      ...originalClip,
      id: `clip-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      position: originalClip.position + originalClip.duration,
    };

    return this.addClip(duplicatedClip);
  }

  async splitClip(clipId: string, splitTime: number): Promise<VideoClip[]> {
    const clip = this.state.timeline.clips.find(c => c.id === clipId);
    if (!clip) {
      throw new Error(`Clip with id ${clipId} not found`);
    }

    if (splitTime <= clip.startTime || splitTime >= clip.endTime) {
      throw new Error('Split time must be within clip boundaries');
    }

    const firstClip: VideoClip = {
      ...clip,
      id: `clip-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      endTime: splitTime,
      duration: splitTime - clip.startTime,
    };

    const secondClip: VideoClip = {
      ...clip,
      id: `clip-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      startTime: splitTime,
      position: clip.position + (splitTime - clip.startTime),
      duration: clip.endTime - splitTime,
    };

    const action: EditorAction = {
      id: `action-${Date.now()}`,
      type: 'SPLIT_CLIP',
      timestamp: new Date(),
      data: { originalClip: clip, firstClip, secondClip },
      description: `Split clip: ${clipId}`,
    };

    this.executeAction(action);
    this.emit('clip:split', { originalClip: clip, newClips: [firstClip, secondClip] });
    
    return [firstClip, secondClip];
  }

  // Selection Management
  selectClip(clipId: string): void {
    if (!this.state.selectedClips.includes(clipId)) {
      this.state.selectedClips.push(clipId);
      this.emit('selection:changed', { selectedClips: this.state.selectedClips });
    }
  }

  deselectClip(clipId: string): void {
    const index = this.state.selectedClips.indexOf(clipId);
    if (index > -1) {
      this.state.selectedClips.splice(index, 1);
      this.emit('selection:changed', { selectedClips: this.state.selectedClips });
    }
  }

  selectMultipleClips(clipIds: string[]): void {
    this.state.selectedClips = [...clipIds];
    this.emit('selection:changed', { selectedClips: this.state.selectedClips });
  }

  clearSelection(): void {
    this.state.selectedClips = [];
    this.emit('selection:changed', { selectedClips: this.state.selectedClips });
  }

  // Effect Management
  async addEffect(clipId: string, effect: Omit<Effect, 'id'>): Promise<Effect> {
    const clip = this.state.timeline.clips.find(c => c.id === clipId);
    if (!clip) {
      throw new Error(`Clip with id ${clipId} not found`);
    }

    const newEffect: Effect = {
      ...effect,
      id: `effect-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };

    if (!clip.effects) {
      clip.effects = [];
    }
    clip.effects.push(newEffect);

    const action: EditorAction = {
      id: `action-${Date.now()}`,
      type: 'ADD_EFFECT',
      timestamp: new Date(),
      data: { clipId, effect: newEffect },
      description: `Added effect: ${effect.type}`,
    };

    this.executeAction(action);
    this.emit('effect:added', { clipId, effect: newEffect });
    
    return newEffect;
  }

  async removeEffect(clipId: string, effectId: string): Promise<void> {
    const clip = this.state.timeline.clips.find(c => c.id === clipId);
    if (!clip || !clip.effects) {
      throw new Error(`Clip or effect not found`);
    }

    const effectIndex = clip.effects.findIndex(e => e.id === effectId);
    if (effectIndex === -1) {
      throw new Error(`Effect with id ${effectId} not found`);
    }

    const removedEffect = clip.effects.splice(effectIndex, 1)[0];

    const action: EditorAction = {
      id: `action-${Date.now()}`,
      type: 'REMOVE_EFFECT',
      timestamp: new Date(),
      data: { clipId, effectId, effect: removedEffect },
      description: `Removed effect: ${removedEffect.type}`,
    };

    this.executeAction(action);
    this.emit('effect:removed', { clipId, effectId });
  }

  async updateEffect(clipId: string, effectId: string, updates: Partial<Effect>): Promise<Effect> {
    const clip = this.state.timeline.clips.find(c => c.id === clipId);
    if (!clip || !clip.effects) {
      throw new Error(`Clip or effect not found`);
    }

    const effectIndex = clip.effects.findIndex(e => e.id === effectId);
    if (effectIndex === -1) {
      throw new Error(`Effect with id ${effectId} not found`);
    }

    const oldEffect = { ...clip.effects[effectIndex] };
    const newEffect = { ...oldEffect, ...updates };
    clip.effects[effectIndex] = newEffect;

    const action: EditorAction = {
      id: `action-${Date.now()}`,
      type: 'UPDATE_EFFECT',
      timestamp: new Date(),
      data: { clipId, effectId, oldEffect, newEffect },
      description: `Updated effect: ${effectId}`,
    };

    this.executeAction(action);
    this.emit('effect:updated', { clipId, effect: newEffect });
    
    return newEffect;
  }

  // Transition Management
  async addTransition(fromClipId: string, toClipId: string, transition: Omit<Transition, 'id'>): Promise<Transition> {
    const fromClip = this.state.timeline.clips.find(c => c.id === fromClipId);
    const toClip = this.state.timeline.clips.find(c => c.id === toClipId);
    
    if (!fromClip || !toClip) {
      throw new Error('One or both clips not found');
    }

    const newTransition: Transition = {
      ...transition,
      id: `transition-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };

    if (!fromClip.transitions) {
      fromClip.transitions = [];
    }
    fromClip.transitions.push(newTransition);

    const action: EditorAction = {
      id: `action-${Date.now()}`,
      type: 'ADD_TRANSITION',
      timestamp: new Date(),
      data: { fromClipId, toClipId, transition: newTransition },
      description: `Added transition: ${transition.type}`,
    };

    this.executeAction(action);
    this.emit('transition:added', { fromClipId, toClipId, transition: newTransition });
    
    return newTransition;
  }

  // Timeline Operations
  async trimClip(clipId: string, newStartTime: number, newEndTime: number): Promise<VideoClip> {
    const clip = this.state.timeline.clips.find(c => c.id === clipId);
    if (!clip) {
      throw new Error(`Clip with id ${clipId} not found`);
    }

    const updates: Partial<VideoClip> = {
      startTime: newStartTime,
      endTime: newEndTime,
      duration: newEndTime - newStartTime,
    };

    return this.updateClip(clipId, updates);
  }

  async moveClip(clipId: string, newPosition: number, newTrack?: number): Promise<VideoClip> {
    const updates: Partial<VideoClip> = { position: newPosition };
    if (newTrack !== undefined) {
      updates.track = newTrack;
    }

    return this.updateClip(clipId, updates);
  }

  async rippleDelete(clipId: string): Promise<void> {
    const clip = this.state.timeline.clips.find(c => c.id === clipId);
    if (!clip) {
      throw new Error(`Clip with id ${clipId} not found`);
    }

    const clipEndPosition = clip.position + clip.duration;
    
    // Remove the clip
    await this.removeClip(clipId);

    // Move all clips after this one to the left
    const clipsToMove = this.state.timeline.clips.filter(c => 
      c.track === clip.track && c.position >= clipEndPosition
    );

    for (const clipToMove of clipsToMove) {
      await this.updateClip(clipToMove.id, {
        position: clipToMove.position - clip.duration,
      });
    }

    this.emit('ripple:delete', { clipId, affectedClips: clipsToMove.map(c => c.id) });
  }

  // Playback Control
  setCurrentTime(time: number): void {
    this.state.currentTime = Math.max(0, Math.min(time, this.state.timeline.duration));
    this.emit('playback:timeupdate', { currentTime: this.state.currentTime });
  }

  setPlaying(playing: boolean): void {
    this.state.isPlaying = playing;
    this.emit(playing ? 'playback:started' : 'playback:paused', { 
      currentTime: this.state.currentTime 
    });
  }

  // Zoom and View
  setZoom(zoom: number): void {
    this.state.zoom = Math.max(0.1, Math.min(zoom, 10));
    this.emit('view:zoom', { zoom: this.state.zoom });
  }

  setSnapToGrid(snap: boolean): void {
    this.state.snapToGrid = snap;
    this.emit('view:snap', { snapToGrid: snap });
  }

  // History Management (Undo/Redo)
  private executeAction(action: EditorAction): void {
    // Remove any actions after current index (when undoing then doing new action)
    this.state.history = this.state.history.slice(0, this.state.historyIndex + 1);
    
    // Add new action
    this.state.history.push(action);
    this.state.historyIndex++;

    // Limit history size
    if (this.state.history.length > this.maxHistorySize) {
      this.state.history.shift();
      this.state.historyIndex--;
    }

    // Apply the action
    this.applyAction(action);
    
    this.emit('history:changed', { 
      canUndo: this.canUndo(), 
      canRedo: this.canRedo() 
    });
  }

  private applyAction(action: EditorAction): void {
    switch (action.type) {
      case 'ADD_CLIP':
        this.state.timeline.clips.push(action.data.clip);
        break;
      case 'REMOVE_CLIP':
        this.state.timeline.clips = this.state.timeline.clips.filter(
          c => c.id !== action.data.clipId
        );
        break;
      case 'UPDATE_CLIP':
        const clipIndex = this.state.timeline.clips.findIndex(
          c => c.id === action.data.clipId
        );
        if (clipIndex > -1) {
          this.state.timeline.clips[clipIndex] = action.data.newClip;
        }
        break;
      case 'SPLIT_CLIP':
        const originalIndex = this.state.timeline.clips.findIndex(
          c => c.id === action.data.originalClip.id
        );
        if (originalIndex > -1) {
          this.state.timeline.clips.splice(
            originalIndex, 
            1, 
            action.data.firstClip, 
            action.data.secondClip
          );
        }
        break;
      // Add more action types as needed
    }

    this.state.timeline.modifiedAt = new Date();
  }

  private reverseAction(action: EditorAction): void {
    switch (action.type) {
      case 'ADD_CLIP':
        this.state.timeline.clips = this.state.timeline.clips.filter(
          c => c.id !== action.data.clip.id
        );
        break;
      case 'REMOVE_CLIP':
        this.state.timeline.clips.push(action.data.clip);
        break;
      case 'UPDATE_CLIP':
        const clipIndex = this.state.timeline.clips.findIndex(
          c => c.id === action.data.clipId
        );
        if (clipIndex > -1) {
          this.state.timeline.clips[clipIndex] = action.data.oldClip;
        }
        break;
      case 'SPLIT_CLIP':
        const firstIndex = this.state.timeline.clips.findIndex(
          c => c.id === action.data.firstClip.id
        );
        const secondIndex = this.state.timeline.clips.findIndex(
          c => c.id === action.data.secondClip.id
        );
        
        if (firstIndex > -1 && secondIndex > -1) {
          // Remove both split clips and restore original
          this.state.timeline.clips = this.state.timeline.clips.filter(
            c => c.id !== action.data.firstClip.id && c.id !== action.data.secondClip.id
          );
          this.state.timeline.clips.push(action.data.originalClip);
        }
        break;
    }

    this.state.timeline.modifiedAt = new Date();
  }

  canUndo(): boolean {
    return this.state.historyIndex >= 0;
  }

  canRedo(): boolean {
    return this.state.historyIndex < this.state.history.length - 1;
  }

  undo(): void {
    if (!this.canUndo()) return;

    const action = this.state.history[this.state.historyIndex];
    this.reverseAction(action);
    this.state.historyIndex--;

    this.emit('action:undone', { action });
    this.emit('history:changed', { 
      canUndo: this.canUndo(), 
      canRedo: this.canRedo() 
    });
  }

  redo(): void {
    if (!this.canRedo()) return;

    this.state.historyIndex++;
    const action = this.state.history[this.state.historyIndex];
    this.applyAction(action);

    this.emit('action:redone', { action });
    this.emit('history:changed', { 
      canUndo: this.canUndo(), 
      canRedo: this.canRedo() 
    });
  }

  // State Management
  getState(): VideoEditorState {
    return { ...this.state };
  }

  setState(newState: Partial<VideoEditorState>): void {
    this.state = { ...this.state, ...newState };
    this.emit('state:changed', { state: this.state });
  }

  // Export/Import
  exportProject(): string {
    return JSON.stringify({
      timeline: this.state.timeline,
      renderSettings: this.state.renderSettings,
      exportedAt: new Date().toISOString(),
    }, null, 2);
  }

  importProject(projectData: string): void {
    try {
      const data = JSON.parse(projectData);
      
      this.state.timeline = data.timeline;
      this.state.renderSettings = data.renderSettings || this.state.renderSettings;
      this.state.history = [];
      this.state.historyIndex = -1;
      this.state.selectedClips = [];

      this.emit('project:imported', { timeline: this.state.timeline });
    } catch (error) {
      throw new Error('Invalid project data format');
    }
  }

  // Configuration
  updateConfig(config: VideoSystemConfig): void {
    this.config = { ...this.config, ...config };
  }

  // Cleanup
  async dispose(): Promise<void> {
    this.removeAllListeners();
  }
}