import { EventEmitter } from 'events';
import { 
  Timeline as TimelineType, 
  VideoClip, 
  Track, 
  VideoResolution 
} from '../types/video-types';

/**
 * Timeline Management System
 * Handles timeline creation, manipulation, and clip organization
 */
export class Timeline extends EventEmitter {
  private currentTimeline: TimelineType | null = null;
  private timelines: Map<string, TimelineType> = new Map();
  private snapThreshold: number = 0.1; // seconds

  constructor() {
    super();
  }

  async create(
    name: string, 
    duration: number = 60, 
    fps: number = 30,
    resolution: VideoResolution = { width: 1920, height: 1080, aspectRatio: '16:9' }
  ): Promise<TimelineType> {
    const timeline: TimelineType = {
      id: `timeline-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name,
      duration,
      fps,
      resolution,
      tracks: this.createDefaultTracks(),
      clips: [],
      createdAt: new Date(),
      modifiedAt: new Date(),
    };

    this.timelines.set(timeline.id, timeline);
    this.currentTimeline = timeline;
    
    this.emit('timeline:created', { timeline });
    return timeline;
  }

  async load(timelineId: string): Promise<TimelineType> {
    const timeline = this.timelines.get(timelineId);
    if (!timeline) {
      throw new Error(`Timeline with id ${timelineId} not found`);
    }

    this.currentTimeline = timeline;
    this.emit('timeline:loaded', { timeline });
    return timeline;
  }

  async save(timeline: TimelineType): Promise<void> {
    timeline.modifiedAt = new Date();
    this.timelines.set(timeline.id, timeline);
    
    if (this.currentTimeline?.id === timeline.id) {
      this.currentTimeline = timeline;
    }

    this.emit('timeline:saved', { timeline });
  }

  async duplicate(timelineId: string, newName?: string): Promise<TimelineType> {
    const originalTimeline = this.timelines.get(timelineId);
    if (!originalTimeline) {
      throw new Error(`Timeline with id ${timelineId} not found`);
    }

    const duplicatedTimeline: TimelineType = {
      ...originalTimeline,
      id: `timeline-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: newName || `${originalTimeline.name} (Copy)`,
      clips: originalTimeline.clips.map(clip => ({
        ...clip,
        id: `clip-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      })),
      tracks: originalTimeline.tracks.map(track => ({
        ...track,
        id: `track-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      })),
      createdAt: new Date(),
      modifiedAt: new Date(),
    };

    this.timelines.set(duplicatedTimeline.id, duplicatedTimeline);
    this.emit('timeline:duplicated', { original: originalTimeline, duplicate: duplicatedTimeline });
    
    return duplicatedTimeline;
  }

  async delete(timelineId: string): Promise<void> {
    const timeline = this.timelines.get(timelineId);
    if (!timeline) {
      throw new Error(`Timeline with id ${timelineId} not found`);
    }

    this.timelines.delete(timelineId);
    
    if (this.currentTimeline?.id === timelineId) {
      this.currentTimeline = null;
    }

    this.emit('timeline:deleted', { timelineId });
  }

  // Clip Management
  async addClip(clip: Omit<VideoClip, 'id'>): Promise<VideoClip> {
    if (!this.currentTimeline) {
      throw new Error('No active timeline');
    }

    const newClip: VideoClip = {
      ...clip,
      id: `clip-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };

    // Validate clip placement
    this.validateClipPlacement(newClip);

    // Auto-snap to grid if enabled
    if (this.shouldSnapToGrid(newClip.position)) {
      newClip.position = this.snapToGrid(newClip.position);
    }

    this.currentTimeline.clips.push(newClip);
    this.currentTimeline.modifiedAt = new Date();

    this.emit('clip:added', { clip: newClip, timeline: this.currentTimeline });
    return newClip;
  }

  async removeClip(clipId: string): Promise<void> {
    if (!this.currentTimeline) {
      throw new Error('No active timeline');
    }

    const clipIndex = this.currentTimeline.clips.findIndex(clip => clip.id === clipId);
    if (clipIndex === -1) {
      throw new Error(`Clip with id ${clipId} not found`);
    }

    const removedClip = this.currentTimeline.clips.splice(clipIndex, 1)[0];
    this.currentTimeline.modifiedAt = new Date();

    this.emit('clip:removed', { clip: removedClip, timeline: this.currentTimeline });
  }

  async updateClip(clipId: string, updates: Partial<VideoClip>): Promise<VideoClip> {
    if (!this.currentTimeline) {
      throw new Error('No active timeline');
    }

    const clipIndex = this.currentTimeline.clips.findIndex(clip => clip.id === clipId);
    if (clipIndex === -1) {
      throw new Error(`Clip with id ${clipId} not found`);
    }

    const updatedClip = { ...this.currentTimeline.clips[clipIndex], ...updates };
    
    // Validate updated clip
    this.validateClipPlacement(updatedClip);

    this.currentTimeline.clips[clipIndex] = updatedClip;
    this.currentTimeline.modifiedAt = new Date();

    this.emit('clip:updated', { clip: updatedClip, timeline: this.currentTimeline });
    return updatedClip;
  }

  async moveClip(clipId: string, newPosition: number, newTrack?: number): Promise<VideoClip> {
    const updates: Partial<VideoClip> = { position: newPosition };
    if (newTrack !== undefined) {
      updates.track = newTrack;
    }

    return this.updateClip(clipId, updates);
  }

  async splitClip(clipId: string, splitTime: number): Promise<VideoClip[]> {
    if (!this.currentTimeline) {
      throw new Error('No active timeline');
    }

    const clip = this.currentTimeline.clips.find(c => c.id === clipId);
    if (!clip) {
      throw new Error(`Clip with id ${clipId} not found`);
    }

    if (splitTime <= clip.startTime || splitTime >= clip.endTime) {
      throw new Error('Split time must be within clip boundaries');
    }

    // Create two new clips
    const firstClip: VideoClip = {
      ...clip,
      id: `clip-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      endTime: splitTime,
      duration: splitTime - clip.startTime,
    };

    const secondClip: VideoClip = {
      ...clip,
      id: `clip-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      startTime: splitTime,
      position: clip.position + (splitTime - clip.startTime),
      duration: clip.endTime - splitTime,
    };

    // Remove original clip and add new ones
    await this.removeClip(clipId);
    await this.addClip(firstClip);
    await this.addClip(secondClip);

    this.emit('clip:split', { 
      originalClip: clip, 
      newClips: [firstClip, secondClip],
      timeline: this.currentTimeline 
    });

    return [firstClip, secondClip];
  }

  // Track Management
  async addTrack(type: Track['type'], name?: string): Promise<Track> {
    if (!this.currentTimeline) {
      throw new Error('No active timeline');
    }

    const track: Track = {
      id: `track-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      name: name || `${type.charAt(0).toUpperCase() + type.slice(1)} Track ${this.currentTimeline.tracks.length + 1}`,
      index: this.currentTimeline.tracks.length,
      enabled: true,
      locked: false,
      volume: type === 'audio' ? 1.0 : undefined,
      opacity: type === 'video' ? 1.0 : undefined,
    };

    this.currentTimeline.tracks.push(track);
    this.currentTimeline.modifiedAt = new Date();

    this.emit('track:added', { track, timeline: this.currentTimeline });
    return track;
  }

  async removeTrack(trackId: string): Promise<void> {
    if (!this.currentTimeline) {
      throw new Error('No active timeline');
    }

    const trackIndex = this.currentTimeline.tracks.findIndex(track => track.id === trackId);
    if (trackIndex === -1) {
      throw new Error(`Track with id ${trackId} not found`);
    }

    // Remove all clips on this track
    const clipsToRemove = this.currentTimeline.clips.filter(clip => clip.track === trackIndex);
    for (const clip of clipsToRemove) {
      await this.removeClip(clip.id);
    }

    const removedTrack = this.currentTimeline.tracks.splice(trackIndex, 1)[0];
    
    // Update track indices for remaining tracks
    this.currentTimeline.tracks.forEach((track, index) => {
      track.index = index;
    });

    this.currentTimeline.modifiedAt = new Date();

    this.emit('track:removed', { track: removedTrack, timeline: this.currentTimeline });
  }

  async updateTrack(trackId: string, updates: Partial<Track>): Promise<Track> {
    if (!this.currentTimeline) {
      throw new Error('No active timeline');
    }

    const trackIndex = this.currentTimeline.tracks.findIndex(track => track.id === trackId);
    if (trackIndex === -1) {
      throw new Error(`Track with id ${trackId} not found`);
    }

    const updatedTrack = { ...this.currentTimeline.tracks[trackIndex], ...updates };
    this.currentTimeline.tracks[trackIndex] = updatedTrack;
    this.currentTimeline.modifiedAt = new Date();

    this.emit('track:updated', { track: updatedTrack, timeline: this.currentTimeline });
    return updatedTrack;
  }

  // Timeline Utilities
  getClipsAtTime(time: number): VideoClip[] {
    if (!this.currentTimeline) return [];

    return this.currentTimeline.clips.filter(clip => 
      time >= clip.position && time < clip.position + clip.duration
    );
  }

  getClipsInRange(startTime: number, endTime: number): VideoClip[] {
    if (!this.currentTimeline) return [];

    return this.currentTimeline.clips.filter(clip => 
      !(clip.position + clip.duration <= startTime || clip.position >= endTime)
    );
  }

  getClipsOnTrack(trackIndex: number): VideoClip[] {
    if (!this.currentTimeline) return [];

    return this.currentTimeline.clips.filter(clip => clip.track === trackIndex);
  }

  findGaps(trackIndex?: number): Array<{ start: number; end: number; track?: number }> {
    if (!this.currentTimeline) return [];

    const clips = trackIndex !== undefined 
      ? this.getClipsOnTrack(trackIndex)
      : this.currentTimeline.clips;

    const sortedClips = clips.sort((a, b) => a.position - b.position);
    const gaps: Array<{ start: number; end: number; track?: number }> = [];

    for (let i = 0; i < sortedClips.length - 1; i++) {
      const currentClip = sortedClips[i];
      const nextClip = sortedClips[i + 1];
      const gapStart = currentClip.position + currentClip.duration;
      const gapEnd = nextClip.position;

      if (gapEnd > gapStart) {
        gaps.push({
          start: gapStart,
          end: gapEnd,
          track: trackIndex,
        });
      }
    }

    return gaps;
  }

  // Validation and Utilities
  private validateClipPlacement(clip: VideoClip): void {
    if (clip.position < 0) {
      throw new Error('Clip position cannot be negative');
    }

    if (clip.duration <= 0) {
      throw new Error('Clip duration must be positive');
    }

    if (clip.startTime < 0 || clip.endTime <= clip.startTime) {
      throw new Error('Invalid clip start/end times');
    }

    if (!this.currentTimeline) return;

    if (clip.track < 0 || clip.track >= this.currentTimeline.tracks.length) {
      throw new Error('Invalid track index');
    }
  }

  private shouldSnapToGrid(position: number): boolean {
    // Simple snap-to-grid logic - can be enhanced
    return true;
  }

  private snapToGrid(position: number): number {
    if (!this.currentTimeline) return position;
    
    const gridSize = 1 / this.currentTimeline.fps; // Snap to frame boundaries
    return Math.round(position / gridSize) * gridSize;
  }

  private createDefaultTracks(): Track[] {
    return [
      {
        id: 'track-video-1',
        type: 'video',
        name: 'Video Track 1',
        index: 0,
        enabled: true,
        locked: false,
        opacity: 1.0,
      },
      {
        id: 'track-audio-1',
        type: 'audio',
        name: 'Audio Track 1',
        index: 1,
        enabled: true,
        locked: false,
        volume: 1.0,
      },
    ];
  }

  // Getters
  getCurrentTimeline(): TimelineType | null {
    return this.currentTimeline;
  }

  getAllTimelines(): TimelineType[] {
    return Array.from(this.timelines.values());
  }

  getDuration(): number {
    return this.currentTimeline?.duration || 0;
  }

  getFPS(): number {
    return this.currentTimeline?.fps || 30;
  }

  getResolution(): VideoResolution | null {
    return this.currentTimeline?.resolution || null;
  }

  // Export/Import
  async exportTimeline(timelineId: string): Promise<string> {
    const timeline = this.timelines.get(timelineId);
    if (!timeline) {
      throw new Error(`Timeline with id ${timelineId} not found`);
    }

    return JSON.stringify(timeline, null, 2);
  }

  async importTimeline(timelineData: string): Promise<TimelineType> {
    try {
      const timeline: TimelineType = JSON.parse(timelineData);
      
      // Generate new ID to avoid conflicts
      timeline.id = `timeline-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      timeline.createdAt = new Date();
      timeline.modifiedAt = new Date();

      this.timelines.set(timeline.id, timeline);
      this.emit('timeline:imported', { timeline });
      
      return timeline;
    } catch (error) {
      throw new Error('Invalid timeline data format');
    }
  }
}