# AI-Powered Video Generation and Editing System

A comprehensive, modular video generation and editing library built with TypeScript, featuring AI video generation, text-to-speech, advanced effects, and real-time editing capabilities.

## Features

### 🎬 Core Video Editing
- **Timeline Management**: Multi-track timeline with drag-and-drop functionality
- **Real-time Preview**: Hardware-accelerated playback using WebCodecs
- **Clip Operations**: Cut, copy, paste, split, trim, and arrange clips
- **Undo/Redo**: Full history management with unlimited undo/redo
- **Multi-format Support**: MP4, WebM, MOV, AVI, and more

### 🤖 AI-Powered Generation
- **AI Video Generation**: Integration with Runway ML, Pika Labs, and Stability AI
- **Text-to-Video**: Generate videos from text prompts
- **Image-to-Video**: Animate static images
- **Style Transfer**: Apply artistic styles to existing videos

### 🎙️ Text-to-Speech
- **Multiple Providers**: ElevenLabs, OpenAI, Google Cloud, Azure
- **Voice Cloning**: Custom voice creation with ElevenLabs
- **Real-time Generation**: Instant audio generation and timeline integration
- **Voice Selection**: Wide variety of voices and languages

### ✨ Advanced Effects
- **Color Correction**: Brightness, contrast, saturation, hue adjustment
- **Visual Effects**: Blur, sepia, grayscale, invert, and more
- **Transform Effects**: Scale, rotate, translate, crop
- **Artistic Effects**: Film grain, vignette, noise reduction
- **Chroma Key**: Green screen removal and replacement

### 🔄 Smooth Transitions
- **Fade Transitions**: Cross-fade, fade to black/white, dissolve
- **Slide Transitions**: Slide, push, wipe in all directions
- **3D Transitions**: Cube rotation, flip, page turn
- **Zoom Transitions**: Zoom in/out with customizable centers
- **Creative Transitions**: Iris, rotate, and custom animations

### 🎨 Professional Tools
- **Color Grading**: Professional color correction tools
- **Audio Mixing**: Multi-track audio with volume and effects
- **Keyframe Animation**: Smooth parameter animations over time
- **Scene Detection**: Automatic scene change detection
- **Thumbnail Generation**: Automatic preview thumbnails

## Installation

```bash
npm install elevenlabs
# or
yarn add elevenlabs
```

## Quick Start

```typescript
import { VideoSystem } from '@/lib/video';

// Initialize the video system
const videoSystem = new VideoSystem({
  aiProviders: {
    runway: { apiKey: 'your-runway-api-key' },
    elevenlabs: { apiKey: 'your-elevenlabs-api-key' },
    openai: { apiKey: 'your-openai-api-key' },
  },
  webcodecs: {
    enabled: true,
    hardwareAcceleration: true,
  },
});

// Create a new timeline
const timeline = await videoSystem.createTimeline('My Project', 60, 30);

// Add a video clip
await videoSystem.addClip({
  type: 'video',
  source: 'path/to/video.mp4',
  startTime: 0,
  endTime: 10,
  duration: 10,
  track: 0,
  position: 0,
});

// Generate AI video
const aiVideo = await videoSystem.generateVideo({
  prompt: 'A beautiful sunset over the ocean',
  duration: 5,
  resolution: { width: 1920, height: 1080, aspectRatio: '16:9' },
  fps: 30,
  model: 'runway',
});

// Generate text-to-speech
const ttsAudio = await videoSystem.generateTTS({
  text: 'Hello, this is a generated voice!',
  voice: 'alloy',
  language: 'en-US',
  provider: 'openai',
  speed: 1.0,
  pitch: 0,
  volume: 1.0,
});

// Render final video
const outputPath = await videoSystem.renderVideo({
  resolution: { width: 1920, height: 1080, aspectRatio: '16:9' },
  fps: 30,
  bitrate: 5000000,
  codec: 'h264',
  format: 'mp4',
  quality: 'high',
  hardwareAcceleration: true,
});
```

## React Component Usage

```tsx
import { VideoEditor } from '@/components/video-editor';

function MyApp() {
  const config = {
    aiProviders: {
      runway: { apiKey: process.env.RUNWAY_API_KEY },
      elevenlabs: { apiKey: process.env.ELEVENLABS_API_KEY },
    },
  };

  return (
    <VideoEditor
      config={config}
      onProjectSave={(data) => console.log('Project saved:', data)}
      onVideoExport={(url) => console.log('Video exported:', url)}
    />
  );
}
```

## API Reference

### VideoSystem

The main class that orchestrates all video operations.

#### Constructor

```typescript
new VideoSystem(config: VideoSystemConfig)
```

#### Methods

##### Timeline Management
- `createTimeline(name: string, duration: number, fps: number): Promise<Timeline>`
- `loadTimeline(timelineId: string): Promise<Timeline>`
- `saveTimeline(timeline: Timeline): Promise<void>`

##### Clip Operations
- `addClip(clip: Omit<VideoClip, 'id'>): Promise<VideoClip>`
- `removeClip(clipId: string): Promise<void>`
- `updateClip(clipId: string, updates: Partial<VideoClip>): Promise<VideoClip>`

##### AI Generation
- `generateVideo(request: AIVideoGenerationRequest): Promise<string>`
- `generateTTS(request: TTSRequest): Promise<string>`

##### Playback Control
- `play(): Promise<void>`
- `pause(): Promise<void>`
- `stop(): Promise<void>`
- `seek(time: number): Promise<void>`

##### Rendering
- `renderVideo(settings: RenderSettings): Promise<string>`

### Effects Library

Pre-built effects for common video processing tasks.

```typescript
import { EffectsLibrary } from '@/lib/video/editor/effects';

// Get all available effects
const effects = EffectsLibrary.getPresets();

// Get effects by category
const colorEffects = EffectsLibrary.getPresetsByCategory('color');

// Create an effect
const vintageEffect = EffectsLibrary.createEffect(
  EffectsLibrary.getPreset('Vintage')!,
  0, // start time
  10 // end time
);
```

### Transitions Library

Smooth transitions between clips.

```typescript
import { TransitionsLibrary } from '@/lib/video/editor/transitions';

// Get all transitions
const transitions = TransitionsLibrary.getPresets();

// Create a transition
const fadeTransition = TransitionsLibrary.createTransition(
  TransitionsLibrary.getPreset('Fade In/Out')!,
  1.5 // custom duration
);
```

### Video Utils

Utility functions for video processing.

```typescript
import { VideoUtils } from '@/lib/video/utils/video-utils';

// Time formatting
const timecode = VideoUtils.secondsToTimecode(125.5, 30); // "00:02:05:15"
const seconds = VideoUtils.timecodeToSeconds("00:02:05:15", 30); // 125.5

// File validation
const isVideo = VideoUtils.isValidVideoFormat('video.mp4'); // true
const isAudio = VideoUtils.isValidAudioFormat('audio.mp3'); // true

// Resolution utilities
const aspectRatio = VideoUtils.calculateAspectRatio(1920, 1080); // "16:9"
const resolutions = VideoUtils.getStandardResolutions('16:9');

// File size calculation
const fileSize = VideoUtils.calculateFileSize(5000000, 60); // bytes
const formatted = VideoUtils.formatFileSize(fileSize); // "35.8 MB"
```

## Configuration

### AI Providers

Configure API keys for AI services:

```typescript
const config: VideoSystemConfig = {
  aiProviders: {
    runway: { apiKey: 'your-runway-api-key' },
    pika: { apiKey: 'your-pika-api-key' },
    stability: { apiKey: 'your-stability-api-key' },
    elevenlabs: { apiKey: 'your-elevenlabs-api-key' },
    openai: { apiKey: 'your-openai-api-key' },
  },
};
```

### WebCodecs

Enable hardware acceleration:

```typescript
const config: VideoSystemConfig = {
  webcodecs: {
    enabled: true,
    hardwareAcceleration: true,
  },
};
```

### File Handling

Configure file processing:

```typescript
const config: VideoSystemConfig = {
  tempDirectory: '/tmp/video-system',
  maxFileSize: 1024 * 1024 * 1024, // 1GB
  supportedFormats: ['mp4', 'webm', 'mov', 'avi'],
};
```

## Event System

The video system emits events for real-time updates:

```typescript
videoSystem.on('clip:added', ({ clip }) => {
  console.log('Clip added:', clip);
});

videoSystem.on('playback:timeupdate', ({ currentTime }) => {
  console.log('Current time:', currentTime);
});

videoSystem.on('render:progress', ({ progress }) => {
  console.log('Render progress:', progress);
});

videoSystem.on('ai:generation:completed', ({ result }) => {
  console.log('AI generation completed:', result);
});
```

## Advanced Usage

### Custom Effects

Create custom effects:

```typescript
import { Effect, EffectType } from '@/lib/video/types/video-types';

const customEffect: Effect = {
  id: 'custom-glow',
  type: 'blur', // base type
  name: 'Custom Glow',
  parameters: {
    radius: 10,
    intensity: 0.8,
    color: '#ff6b6b',
  },
  startTime: 0,
  endTime: 5,
  enabled: true,
};
```

### Keyframe Animation

Animate effect parameters:

```typescript
import { EffectAnimator, EffectKeyframe } from '@/lib/video/editor/effects';

const keyframes: EffectKeyframe[] = [
  { time: 0, parameters: { opacity: 0 } },
  { time: 1, parameters: { opacity: 1 }, easing: 'ease-in' },
  { time: 4, parameters: { opacity: 1 } },
  { time: 5, parameters: { opacity: 0 }, easing: 'ease-out' },
];

const currentParams = EffectAnimator.interpolateParameters(keyframes, 2.5);
```

### Batch Processing

Process multiple videos:

```typescript
const requests = [
  { prompt: 'Sunset over mountains', duration: 5 },
  { prompt: 'City at night', duration: 5 },
  { prompt: 'Ocean waves', duration: 5 },
];

const results = await videoSystem.generateBatch(requests);
```

## Browser Compatibility

- **Chrome 94+**: Full WebCodecs support
- **Firefox 90+**: Limited WebCodecs support
- **Safari 15+**: Partial WebCodecs support
- **Edge 94+**: Full WebCodecs support

For browsers without WebCodecs, the system falls back to traditional Canvas API rendering.

## Performance Tips

1. **Enable Hardware Acceleration**: Use WebCodecs when available
2. **Optimize Resolution**: Use appropriate resolution for your target
3. **Limit Concurrent Operations**: Process one AI generation at a time
4. **Use Efficient Codecs**: H.264 for compatibility, H.265 for size
5. **Cache Thumbnails**: Generate and cache preview thumbnails

## Troubleshooting

### Common Issues

**WebCodecs not available**
- Ensure you're using a supported browser
- Check if hardware acceleration is enabled
- Fall back to Canvas API rendering

**AI Generation fails**
- Verify API keys are correct
- Check network connectivity
- Monitor rate limits

**Audio sync issues**
- Ensure consistent frame rates
- Use proper audio sample rates
- Check for timing drift

**Memory issues**
- Limit concurrent operations
- Use appropriate video resolutions
- Clean up resources properly

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- GitHub Issues: [Create an issue](https://github.com/your-repo/issues)
- Documentation: [Full docs](https://your-docs-site.com)
- Discord: [Join our community](https://discord.gg/your-server)