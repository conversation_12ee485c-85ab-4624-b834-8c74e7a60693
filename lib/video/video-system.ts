import { EventEmitter } from 'events';
import { VideoEngine } from './core/video-engine';
import { Timeline } from './core/timeline';
import { VideoRenderer } from './core/renderer';
import { AIVideoGenerator } from './ai/video-generator';
import { TTSGenerator } from './ai/tts-generator';
import { VideoEditor } from './editor/video-editor';
import { 
  VideoSystemConfig, 
  VideoSystemEvents, 
  VideoSystemEventType, 
  VideoSystemEventHandler,
  VideoClip,
  Timeline as TimelineType,
  RenderSettings,
  AIVideoGenerationRequest,
  TTSRequest,
  VideoProcessingJob
} from './types/video-types';

/**
 * Main Video System Class
 * Orchestrates all video generation, editing, and processing capabilities
 */
export class VideoSystem extends EventEmitter {
  private config: VideoSystemConfig;
  private engine: VideoEngine;
  private timeline: Timeline;
  private renderer: VideoRenderer;
  private aiGenerator: AIVideoGenerator;
  private ttsGenerator: TTSGenerator;
  private editor: VideoEditor;
  private jobs: Map<string, VideoProcessingJob> = new Map();

  constructor(config: VideoSystemConfig = {}) {
    super();
    this.config = {
      tempDirectory: '/tmp/video-system',
      maxFileSize: 1024 * 1024 * 1024, // 1GB
      supportedFormats: ['mp4', 'webm', 'mov', 'avi', 'mkv'],
      webcodecs: {
        enabled: true,
        hardwareAcceleration: true,
      },
      ...config,
    };

    this.initializeComponents();
  }

  private initializeComponents(): void {
    // Initialize core components
    this.engine = new VideoEngine(this.config);
    this.timeline = new Timeline();
    this.renderer = new VideoRenderer(this.config);
    this.aiGenerator = new AIVideoGenerator(this.config);
    this.ttsGenerator = new TTSGenerator(this.config);
    this.editor = new VideoEditor(this.config);

    // Set up event forwarding
    this.setupEventForwarding();
  }

  private setupEventForwarding(): void {
    // Forward events from components to main system
    [this.engine, this.timeline, this.renderer, this.aiGenerator, this.ttsGenerator, this.editor]
      .forEach(component => {
        if (component && typeof component.on === 'function') {
          component.on('*', (eventType: string, data: any) => {
            this.emit(eventType, data);
          });
        }
      });
  }

  // Timeline Management
  async createTimeline(name: string, duration: number = 60, fps: number = 30): Promise<TimelineType> {
    const timelineData = await this.timeline.create(name, duration, fps);
    this.emit('timeline:created', { timeline: timelineData });
    return timelineData;
  }

  async loadTimeline(timelineId: string): Promise<TimelineType> {
    const timelineData = await this.timeline.load(timelineId);
    this.emit('timeline:loaded', { timeline: timelineData });
    return timelineData;
  }

  async saveTimeline(timeline: TimelineType): Promise<void> {
    await this.timeline.save(timeline);
    this.emit('timeline:saved', { timeline });
  }

  // Clip Management
  async addClip(clip: Omit<VideoClip, 'id'>): Promise<VideoClip> {
    const newClip = await this.editor.addClip(clip);
    this.emit('clip:added', { clip: newClip });
    return newClip;
  }

  async removeClip(clipId: string): Promise<void> {
    await this.editor.removeClip(clipId);
    this.emit('clip:removed', { clipId });
  }

  async updateClip(clipId: string, updates: Partial<VideoClip>): Promise<VideoClip> {
    const updatedClip = await this.editor.updateClip(clipId, updates);
    this.emit('clip:updated', { clip: updatedClip });
    return updatedClip;
  }

  // AI Video Generation
  async generateVideo(request: AIVideoGenerationRequest): Promise<string> {
    const jobId = this.createJob('ai-generation', request);
    
    try {
      this.emit('ai:generation:started', { requestId: jobId });
      
      const result = await this.aiGenerator.generateVideo(request, (progress) => {
        this.updateJobProgress(jobId, progress);
        this.emit('ai:generation:progress', { requestId: jobId, progress });
      });

      this.completeJob(jobId, result);
      this.emit('ai:generation:completed', { requestId: jobId, result });
      
      return result.videoUrl!;
    } catch (error) {
      this.failJob(jobId, error as Error);
      this.emit('ai:generation:failed', { requestId: jobId, error: (error as Error).message });
      throw error;
    }
  }

  // Text-to-Speech Generation
  async generateTTS(request: TTSRequest): Promise<string> {
    const jobId = this.createJob('tts', request);
    
    try {
      const result = await this.ttsGenerator.generateSpeech(request);
      this.completeJob(jobId, result);
      return result.audioUrl;
    } catch (error) {
      this.failJob(jobId, error as Error);
      throw error;
    }
  }

  // Video Rendering
  async renderVideo(settings: RenderSettings): Promise<string> {
    const jobId = this.createJob('render', settings);
    
    try {
      this.emit('render:started', { jobId });
      
      const outputPath = await this.renderer.render(
        this.timeline.getCurrentTimeline(),
        settings,
        (progress) => {
          this.updateJobProgress(jobId, progress);
          this.emit('render:progress', { jobId, progress });
        }
      );

      this.completeJob(jobId, { outputPath });
      this.emit('render:completed', { jobId, output: outputPath });
      
      return outputPath;
    } catch (error) {
      this.failJob(jobId, error as Error);
      this.emit('render:failed', { jobId, error: (error as Error).message });
      throw error;
    }
  }

  // Playback Control
  async play(): Promise<void> {
    await this.engine.play();
    this.emit('playback:started', { currentTime: this.engine.getCurrentTime() });
  }

  async pause(): Promise<void> {
    await this.engine.pause();
    this.emit('playback:paused', { currentTime: this.engine.getCurrentTime() });
  }

  async stop(): Promise<void> {
    await this.engine.stop();
    this.emit('playback:stopped', { currentTime: 0 });
  }

  async seek(time: number): Promise<void> {
    await this.engine.seek(time);
    this.emit('playback:timeupdate', { currentTime: time });
  }

  // Utility Methods
  getCurrentTime(): number {
    return this.engine.getCurrentTime();
  }

  getDuration(): number {
    return this.timeline.getDuration();
  }

  isPlaying(): boolean {
    return this.engine.isPlaying();
  }

  // Job Management
  private createJob(type: VideoProcessingJob['type'], input: any): string {
    const job: VideoProcessingJob = {
      id: `job-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      status: 'queued',
      progress: 0,
      startTime: new Date(),
      input,
    };
    
    this.jobs.set(job.id, job);
    return job.id;
  }

  private updateJobProgress(jobId: string, progress: number): void {
    const job = this.jobs.get(jobId);
    if (job) {
      job.progress = progress;
      job.status = 'processing';
    }
  }

  private completeJob(jobId: string, output: any): void {
    const job = this.jobs.get(jobId);
    if (job) {
      job.status = 'completed';
      job.progress = 100;
      job.endTime = new Date();
      job.output = output;
    }
  }

  private failJob(jobId: string, error: Error): void {
    const job = this.jobs.get(jobId);
    if (job) {
      job.status = 'failed';
      job.endTime = new Date();
      job.error = error.message;
    }
  }

  getJob(jobId: string): VideoProcessingJob | undefined {
    return this.jobs.get(jobId);
  }

  getAllJobs(): VideoProcessingJob[] {
    return Array.from(this.jobs.values());
  }

  // Event Handling
  on<T extends VideoSystemEventType>(
    eventType: T,
    handler: VideoSystemEventHandler<T>
  ): this {
    return super.on(eventType, handler);
  }

  off<T extends VideoSystemEventType>(
    eventType: T,
    handler: VideoSystemEventHandler<T>
  ): this {
    return super.off(eventType, handler);
  }

  emit<T extends VideoSystemEventType>(
    eventType: T,
    data: VideoSystemEvents[T]
  ): boolean {
    return super.emit(eventType, data);
  }

  // Cleanup
  async dispose(): Promise<void> {
    await this.engine.dispose();
    await this.renderer.dispose();
    await this.aiGenerator.dispose();
    await this.ttsGenerator.dispose();
    await this.editor.dispose();
    
    this.jobs.clear();
    this.removeAllListeners();
  }

  // Configuration
  updateConfig(newConfig: Partial<VideoSystemConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Update component configurations
    this.engine.updateConfig(this.config);
    this.renderer.updateConfig(this.config);
    this.aiGenerator.updateConfig(this.config);
    this.ttsGenerator.updateConfig(this.config);
    this.editor.updateConfig(this.config);
  }

  getConfig(): VideoSystemConfig {
    return { ...this.config };
  }
}