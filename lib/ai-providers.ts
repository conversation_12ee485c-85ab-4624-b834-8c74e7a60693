import { openai } from '@ai-sdk/openai';
import { google } from '@ai-sdk/google';

if (!process.env.OPENAI_API_KEY) {
  throw new Error('OPENAI_API_KEY is required');
}

if (!process.env.GOOGLE_API_KEY) {
  throw new Error('GOOGLE_API_KEY is required');
}

export const aiProviders = {
  openai: openai({
    apiKey: process.env.OPENAI_API_KEY,
  }),
  google: google({
    apiKey: process.env.GOOGLE_API_KEY,
  }),
};

// Model configurations for different use cases
export const modelConfigs = {
  // For general chat and complex reasoning
  chat: {
    model: aiProviders.openai('gpt-4o'),
    temperature: 0.7,
    maxTokens: 2000,
  },
  
  // For creative content generation
  creative: {
    model: aiProviders.openai('gpt-4o'),
    temperature: 0.9,
    maxTokens: 1500,
  },
  
  // For structured data and analysis
  analytical: {
    model: aiProviders.openai('gpt-4o'),
    temperature: 0.3,
    maxTokens: 1000,
  },
  
  // For fast responses
  fast: {
    model: aiProviders.google('gemini-1.5-flash'),
    temperature: 0.5,
    maxTokens: 1000,
  },
};