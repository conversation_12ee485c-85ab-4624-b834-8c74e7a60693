// ElevenLabs Constants
// API constants and configuration

export const E<PERSON>VENLABS_API_BASE_URL = 'https://api.elevenlabs.io/v1';

export const ELEVENLABS_MODELS = {
  ELEVEN_MULTILINGUAL_V2: 'eleven_multilingual_v2',
  ELEVEN_TURBO_V2_5: 'eleven_turbo_v2_5',
  ELEVEN_FLASH_V2_5: 'eleven_flash_v2_5',
  ELEVEN_V3_ALPHA: 'eleven_v3_alpha',
} as const;

export const ELEVENLABS_OUTPUT_FORMATS = {
  MP3_22050_32: 'mp3_22050_32',
  MP3_44100_32: 'mp3_44100_32',
  MP3_44100_64: 'mp3_44100_64',
  MP3_44100_96: 'mp3_44100_96',
  MP3_44100_128: 'mp3_44100_128',
  MP3_44100_192: 'mp3_44100_192',
  PCM_16000: 'pcm_16000',
  PCM_22050: 'pcm_22050',
  PCM_24000: 'pcm_24000',
  PCM_44100: 'pcm_44100',
  ULAW_8000: 'ulaw_8000',
} as const;

export const VOICE_CATEGORIES = {
  PREMADE: 'premade',
  CLONED: 'cloned',
  GENERATED: 'generated',
  PROFESSIONAL: 'professional',
} as const;

export const VOICE_SETTINGS_DEFAULTS = {
  STABILITY: 0.5,
  SIMILARITY_BOOST: 0.5,
  STYLE: 0.0,
  USE_SPEAKER_BOOST: true,
} as const;

export const SUPPORTED_LANGUAGES = [
  'en', 'ja', 'zh', 'de', 'hi', 'fr', 'ko', 'pt', 'it', 'es', 'id', 'nl', 'tr', 'ar', 'sv', 'cs', 'ru', 'da', 'el', 'fi', 'hr', 'ms', 'sk', 'ta', 'th', 'uk', 'vi', 'no', 'he', 'hu', 'bg', 'ca', 'sl'
] as const;

export const TEXT_NORMALIZATION_OPTIONS = {
  AUTO: 'auto',
  ON: 'on',
  OFF: 'off',
} as const;

export const SUBSCRIPTION_TIERS = {
  FREE: 'free',
  STARTER: 'starter',
  CREATOR: 'creator',
  PRO: 'pro',
  SCALE: 'scale',
  BUSINESS: 'business',
} as const;

export const VOICE_VERIFICATION_STATES = {
  VERIFIED: 'verified',
  REQUIRES_VERIFICATION: 'requires_verification',
  VERIFICATION_FAILED: 'verification_failed',
} as const;

export const GENERATION_STATES = {
  IDLE: 'idle',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
} as const;

export const STREAMING_CHUNK_SCHEDULES = {
  REAL_TIME: [50],
  BALANCED: [120, 160, 250, 290],
  QUALITY: [500],
} as const;

export const ERROR_CODES = {
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  QUOTA_EXCEEDED: 429,
  VALIDATION_ERROR: 422,
  SERVER_ERROR: 500,
} as const;

export const MAX_TEXT_LENGTH = {
  [ELEVENLABS_MODELS.ELEVEN_MULTILINGUAL_V2]: 2500,
  [ELEVENLABS_MODELS.ELEVEN_TURBO_V2_5]: 2500,
  [ELEVENLABS_MODELS.ELEVEN_FLASH_V2_5]: 2500,
  [ELEVENLABS_MODELS.ELEVEN_V3_ALPHA]: 2500,
} as const;

export const VOICE_CLONE_REQUIREMENTS = {
  MIN_AUDIO_DURATION: 60, // seconds
  MAX_AUDIO_DURATION: 300, // seconds
  MIN_FILES: 1,
  MAX_FILES: 25,
  SUPPORTED_FORMATS: ['mp3', 'wav', 'flac', 'm4a', 'ogg'],
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB per file
} as const;

export const VOICE_DESIGN_LIMITS = {
  MIN_DESCRIPTION_LENGTH: 10,
  MAX_DESCRIPTION_LENGTH: 500,
  MIN_TEXT_LENGTH: 50,
  MAX_TEXT_LENGTH: 1000,
} as const;

// Conversational AI Constants
export const CONVERSATIONAL_AI_MODELS = {
  ELEVEN_MULTILINGUAL_V2: 'eleven_multilingual_v2',
  ELEVEN_TURBO_V2_5: 'eleven_turbo_v2_5',
  ELEVEN_FLASH_V2_5: 'eleven_flash_v2_5',
  ELEVEN_V3_ALPHA: 'eleven_v3_alpha',
} as const;

export const TURN_TAKING_MODELS = {
  DEFAULT: 'default',
  ADVANCED: 'advanced',
  CUSTOM: 'custom',
} as const;

export const TURN_DETECTION_METHODS = {
  VOICE_ACTIVITY: 'voice_activity',
  SILENCE_DETECTION: 'silence_detection',
  HYBRID: 'hybrid',
} as const;

export const AUDIO_FORMATS = {
  PCM_16: 'pcm_16',
  PCM_24: 'pcm_24', 
  PCM_32: 'pcm_32',
  MP3: 'mp3',
  WAV: 'wav',
  WEBM: 'webm',
  OPUS: 'opus',
} as const;

export const SAMPLE_RATES = {
  SR_16000: 16000,
  SR_24000: 24000,
  SR_44100: 44100,
  SR_48000: 48000,
} as const;

export const CONVERSATIONAL_AI_DEFAULTS = {
  VOICE_SETTINGS: {
    STABILITY: 0.5,
    SIMILARITY_BOOST: 0.8,
    STYLE: 0.0,
    USE_SPEAKER_BOOST: true,
    OPTIMIZE_STREAMING_LATENCY: 0,
    OUTPUT_FORMAT: 'mp3_44100_128',
  },
  TURN_TAKING: {
    ENABLED: true,
    MODEL_TYPE: 'default',
    SENSITIVITY: 0.5,
    MAX_SILENCE_MS: 1000,
    MIN_SPEECH_DURATION_MS: 100,
    INTERRUPTION_THRESHOLD: 0.5,
    TURN_DETECTION_METHOD: 'hybrid',
  },
  CONVERSATION_CONFIG: {
    CONVERSATION_TIMEOUT_MS: 30000,
    MAX_CONVERSATION_LENGTH_MS: 1800000, // 30 minutes
    ENABLE_INTERRUPTIONS: true,
    ENABLE_BACKCHANNEL: true,
    ENABLE_TURN_TAKING: true,
    SILENCE_THRESHOLD_MS: 1000,
  },
  AUDIO_CONFIG: {
    SAMPLE_RATE: 44100,
    CHANNELS: 1,
    BIT_DEPTH: 16,
    ENABLE_NOISE_SUPPRESSION: true,
    ENABLE_ECHO_CANCELLATION: true,
    ENABLE_AUTO_GAIN_CONTROL: true,
  },
  SYSTEM_PROMPTS: {
    GENERAL_ASSISTANT: 'You are a helpful and friendly AI assistant. Respond naturally and conversationally.',
    CUSTOMER_SERVICE: 'You are a professional customer service representative. Be helpful, polite, and solution-oriented.',
    EDUCATIONAL: 'You are an educational assistant. Explain concepts clearly and encourage learning.',
    CASUAL_CHAT: 'You are a casual conversation partner. Be friendly, engaging, and personable.',
    TECHNICAL_SUPPORT: 'You are a technical support specialist. Provide clear, step-by-step guidance.',
  },
} as const;

export const CONVERSATIONAL_AI_LIMITS = {
  MIN_SYSTEM_PROMPT_LENGTH: 10,
  MAX_SYSTEM_PROMPT_LENGTH: 5000,
  MIN_AGENT_NAME_LENGTH: 1,
  MAX_AGENT_NAME_LENGTH: 100,
  MIN_DESCRIPTION_LENGTH: 0,
  MAX_DESCRIPTION_LENGTH: 500,
  MIN_CONTEXT_WINDOW: 1000,
  MAX_CONTEXT_WINDOW: 32000,
  MIN_MAX_TOKENS: 1,
  MAX_MAX_TOKENS: 4000,
  MIN_TEMPERATURE: 0,
  MAX_TEMPERATURE: 2,
  MIN_TOP_P: 0,
  MAX_TOP_P: 1,
  MIN_FREQUENCY_PENALTY: -2,
  MAX_FREQUENCY_PENALTY: 2,
  MIN_PRESENCE_PENALTY: -2,
  MAX_PRESENCE_PENALTY: 2,
} as const;