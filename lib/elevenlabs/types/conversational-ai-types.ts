// ElevenLabs Conversational AI Types
// Comprehensive type definitions for real-time voice conversations

export interface ConversationalAIAgent {
  agent_id: string;
  name: string;
  description?: string;
  voice_id: string;
  model_id: string;
  system_prompt: string;
  conversation_config: ConversationConfig;
  voice_settings: VoiceSettings;
  turn_taking_settings: TurnTakingSettings;
  language: string;
  tools?: ConversationalTool[];
  context_window: number;
  max_tokens: number;
  temperature: number;
  top_p: number;
  frequency_penalty: number;
  presence_penalty: number;
  created_at: string;
  updated_at: string;
  owner_id: string;
  is_public: boolean;
  tags: string[];
  metadata: Record<string, any>;
}

export interface ConversationConfig {
  conversation_timeout_ms: number;
  max_conversation_length_ms: number;
  enable_interruptions: boolean;
  enable_backchannel: boolean;
  enable_turn_taking: boolean;
  silence_threshold_ms: number;
  voice_activity_detection: VoiceActivityDetectionConfig;
  custom_llm_settings?: CustomLLMSettings;
  audio_processing: AudioProcessingConfig;
}

export interface VoiceActivityDetectionConfig {
  enabled: boolean;
  threshold: number;
  min_speech_duration_ms: number;
  max_silence_duration_ms: number;
  speech_pad_ms: number;
  silence_pad_ms: number;
  energy_threshold: number;
  spectral_centroid_threshold: number;
}

export interface CustomLLMSettings {
  endpoint: string;
  model_name: string;
  api_key?: string;
  headers?: Record<string, string>;
  timeout_ms: number;
  max_retries: number;
  temperature: number;
  top_p: number;
  max_tokens: number;
  streaming: boolean;
}

export interface AudioProcessingConfig {
  input_audio_format: AudioFormat;
  output_audio_format: AudioFormat;
  noise_suppression: boolean;
  echo_cancellation: boolean;
  auto_gain_control: boolean;
  sample_rate: number;
  bit_depth: number;
  channels: number;
}

export interface AudioFormat {
  encoding: 'pcm_16' | 'pcm_24' | 'pcm_32' | 'mp3' | 'wav' | 'webm' | 'opus';
  sample_rate: 16000 | 24000 | 44100 | 48000;
  bit_depth: 16 | 24 | 32;
  channels: 1 | 2;
}

export interface TurnTakingSettings {
  enabled: boolean;
  model_type: 'default' | 'advanced' | 'custom';
  sensitivity: number;
  max_silence_ms: number;
  min_speech_duration_ms: number;
  interruption_threshold: number;
  turn_detection_method: 'voice_activity' | 'silence_detection' | 'hybrid';
  custom_model_endpoint?: string;
}

export interface VoiceSettings {
  stability: number;
  similarity_boost: number;
  style: number;
  use_speaker_boost: boolean;
  optimize_streaming_latency: number;
  output_format: string;
}

export interface ConversationalTool {
  name: string;
  description: string;
  parameters: ToolParameters;
  function: string;
  enabled: boolean;
  timeout_ms: number;
  retry_count: number;
}

export interface ToolParameters {
  type: 'object';
  properties: Record<string, {
    type: string;
    description?: string;
    enum?: string[];
    default?: any;
  }>;
  required: string[];
}

export interface ConversationalAIConfig {
  agentId: string;
  signedUrl?: string;
  systemPrompt?: string;
  context?: string;
  voiceSettings?: VoiceSettings;
  turnTaking?: TurnTakingSettings;
  audioConfig?: AudioConfig;
  customLLM?: CustomLLMSettings;
  callbacks?: ConversationalCallbacks;
  metadata?: Record<string, any>;
}

export interface AudioConfig {
  enableRecording?: boolean;
  sampleRate?: number;
  channels?: number;
  bitDepth?: number;
  format?: AudioFormat;
  enableNoiseSuppression?: boolean;
  enableEchoCancellation?: boolean;
  enableAutoGainControl?: boolean;
  inputDeviceId?: string;
  outputDeviceId?: string;
}

export interface ConversationalCallbacks {
  onConnect?: () => void;
  onDisconnect?: (reason?: string) => void;
  onMessage?: (message: ConversationalMessage) => void;
  onAudioReceived?: (audioBlob: Blob, metadata?: AudioMetadata) => void;
  onTextReceived?: (text: string, metadata?: TextMetadata) => void;
  onError?: (error: ConversationalAIError) => void;
  onStatusChange?: (status: ConversationalAIStatus) => void;
  onTurnDetected?: (turn: TurnEvent) => void;
  onInterruption?: (interruption: InterruptionEvent) => void;
  onToolCall?: (toolCall: ToolCallEvent) => void;
}

export interface ConversationalAISession {
  id: string;
  agentId: string;
  status: ConversationalAIStatus;
  config: ConversationalAIConfig;
  startTime: number;
  endTime?: number;
  duration?: number;
  messages: ConversationalMessage[];
  metadata: Record<string, any>;
  metrics?: SessionMetrics;
}

export interface ConversationalMessage {
  id: string;
  type: 'text' | 'audio' | 'tool_call' | 'tool_response' | 'system';
  content: string | Blob | ArrayBuffer;
  timestamp: number;
  sender: 'user' | 'assistant' | 'system';
  metadata?: MessageMetadata;
}

export interface MessageMetadata {
  responseTime?: number;
  audioFormat?: AudioFormat;
  duration?: number;
  confidence?: number;
  turnType?: 'start' | 'continue' | 'end';
  toolCall?: ToolCallEvent;
  emotions?: EmotionData;
  voiceActivity?: VoiceActivityData;
}

export interface AudioMetadata {
  format: AudioFormat;
  duration: number;
  size: number;
  channels: number;
  sampleRate: number;
  bitDepth: number;
  encoding: string;
}

export interface TextMetadata {
  confidence: number;
  language: string;
  responseTime: number;
  tokenCount: number;
  modelUsed: string;
  emotions?: EmotionData;
}

export interface EmotionData {
  primary: string;
  secondary?: string;
  intensity: number;
  confidence: number;
  valence: number;
  arousal: number;
}

export interface VoiceActivityData {
  isActive: boolean;
  energy: number;
  spectralCentroid: number;
  zeroCrossingRate: number;
  confidence: number;
}

export interface ConversationalAIError {
  type: 'connection' | 'audio' | 'text' | 'tool' | 'session' | 'configuration' | 'api';
  message: string;
  code?: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

export type ConversationalAIStatus = 
  | 'initializing'
  | 'connecting'
  | 'connected'
  | 'active'
  | 'listening'
  | 'speaking'
  | 'processing'
  | 'waiting'
  | 'interrupted'
  | 'error'
  | 'disconnected'
  | 'ended';

export interface TurnEvent {
  type: 'start' | 'end' | 'continue';
  speaker: 'user' | 'assistant';
  confidence: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface InterruptionEvent {
  type: 'user_interrupt' | 'system_interrupt' | 'timeout_interrupt';
  timestamp: number;
  interruptedMessageId?: string;
  reason?: string;
  metadata?: Record<string, any>;
}

export interface ToolCallEvent {
  toolName: string;
  parameters: Record<string, any>;
  callId: string;
  timestamp: number;
  status: 'pending' | 'success' | 'error';
  response?: any;
  error?: string;
  duration?: number;
}

export interface VoiceActivityDetection {
  enabled: boolean;
  threshold: number;
  silenceTimeoutMs: number;
  isActive: boolean;
  lastActivity: number;
  minSpeechDuration?: number;
  maxSilenceDuration?: number;
  energyThreshold?: number;
  spectralCentroidThreshold?: number;
}

export interface SessionMetrics {
  startTime: number;
  endTime?: number;
  currentDuration?: number;
  messagesCount: number;
  userMessagesCount?: number;
  assistantMessagesCount?: number;
  totalAudioDuration: number;
  averageResponseTime: number;
  responseTimeBuffer: number[];
  interruptionCount?: number;
  turnCount?: number;
  toolCallCount?: number;
  errorCount?: number;
  reconnectionCount?: number;
  audioQualityMetrics?: AudioQualityMetrics;
  isActive?: boolean;
}

export interface AudioQualityMetrics {
  averageLatency: number;
  jitter: number;
  packetLoss: number;
  audioDropouts: number;
  noiseLevel: number;
  signalToNoiseRatio: number;
  echoCancellationEffectiveness: number;
}

export interface ConversationalAIResponse {
  success: boolean;
  sessionId?: string;
  messageId?: string;
  type: 'text' | 'audio' | 'tool_call' | 'status_update' | 'error';
  content?: any;
  metadata?: Record<string, any>;
  timestamp: number;
  error?: ConversationalAIError;
}

export interface StreamingResponse {
  type: 'audio_chunk' | 'text_chunk' | 'audio_complete' | 'text_complete';
  data: Uint8Array | string;
  chunkId: string;
  isLast: boolean;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface ConversationalAnalytics {
  totalSessions: number;
  totalDuration: number;
  averageSessionDuration: number;
  totalMessages: number;
  averageMessagesPerSession: number;
  mostUsedAgents: Array<{
    agentId: string;
    name: string;
    usageCount: number;
    averageRating: number;
  }>;
  performanceMetrics: {
    averageResponseTime: number;
    averageLatency: number;
    errorRate: number;
    interruptionRate: number;
  };
  userSatisfactionMetrics: {
    averageRating: number;
    npsScore: number;
    completionRate: number;
  };
  timeRange: {
    start: string;
    end: string;
  };
}

export interface AgentPerformanceMetrics {
  agentId: string;
  totalSessions: number;
  totalDuration: number;
  averageResponseTime: number;
  errorRate: number;
  interruptionRate: number;
  userSatisfactionScore: number;
  mostCommonErrors: Array<{
    error: string;
    count: number;
  }>;
  performanceTrends: Array<{
    date: string;
    metrics: {
      responseTime: number;
      errorRate: number;
      satisfaction: number;
    };
  }>;
}

export interface ConversationalAISettings {
  defaultLanguage: string;
  defaultVoiceSettings: VoiceSettings;
  defaultTurnTakingSettings: TurnTakingSettings;
  defaultAudioConfig: AudioConfig;
  enableAnalytics: boolean;
  enableLogging: boolean;
  enableErrorReporting: boolean;
  maxSessionDuration: number;
  maxConcurrentSessions: number;
  allowedDomains: string[];
  rateLimiting: {
    enabled: boolean;
    maxRequestsPerMinute: number;
    maxRequestsPerHour: number;
  };
  security: {
    enableEncryption: boolean;
    requireAuthentication: boolean;
    allowedApiKeys: string[];
  };
}