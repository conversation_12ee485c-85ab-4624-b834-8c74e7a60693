// ElevenLabs Types
// TypeScript interfaces and types for ElevenLabs API

export interface ElevenLabsVoice {
  voice_id: string;
  name: string;
  samples: any[];
  category: string;
  fine_tuning: {
    is_allowed: boolean;
    finetuning_state: string;
    verification_failures: any[];
    verification_attempts_count: number;
    manual_verification_requested: boolean;
  };
  labels: Record<string, string>;
  description: string;
  preview_url: string;
  available_for_tiers: string[];
  settings: {
    stability: number;
    similarity_boost: number;
    style: number;
    use_speaker_boost: boolean;
  };
  sharing: {
    status: string;
    history_item_sample_id: string;
    original_voice_id: string;
  };
  high_quality_base_model_ids: string[];
  safety_control: string;
  voice_verification: {
    requires_verification: boolean;
    is_verified: boolean;
    verification_failures: any[];
    verification_attempts_count: number;
    language: string;
  };
  permission_on_resource: string;
  is_owner: boolean;
  is_mixed: boolean;
}

export interface ElevenLabsModel {
  model_id: string;
  name: string;
  can_be_finetuned: boolean;
  can_do_text_to_speech: boolean;
  can_do_voice_conversion: boolean;
  can_use_style: boolean;
  can_use_speaker_boost: boolean;
  serves_pro_voices: boolean;
  language: {
    language_id: string;
    name: string;
  };
  description: string;
  requires_alpha_access: boolean;
  max_characters_request_free_tier: number;
  max_characters_request_subscribed_tier: number;
  maximum_text_length_per_request: number;
  tokenizer: string;
}

export interface TTSRequest {
  text: string;
  voice_id: string;
  model_id?: string;
  voice_settings?: {
    stability?: number;
    similarity_boost?: number;
    style?: number;
    use_speaker_boost?: boolean;
  };
  pronunciation_dictionary_locators?: Array<{
    pronunciation_dictionary_id: string;
    version_id: string;
  }>;
  seed?: number;
  previous_text?: string;
  next_text?: string;
  previous_request_ids?: string[];
  next_request_ids?: string[];
  output_format?: 'mp3_22050_32' | 'mp3_44100_32' | 'mp3_44100_64' | 'mp3_44100_96' | 'mp3_44100_128' | 'mp3_44100_192' | 'pcm_16000' | 'pcm_22050' | 'pcm_24000' | 'pcm_44100' | 'ulaw_8000';
  apply_text_normalization?: 'auto' | 'on' | 'off';
}

export interface TTSResponse {
  audio: ArrayBuffer;
  alignment?: {
    characters: string[];
    character_start_times_seconds: number[];
    character_end_times_seconds: number[];
  };
  normalized_alignment?: {
    characters: string[];
    character_start_times_seconds: number[];
    character_end_times_seconds: number[];
  };
}

export interface VoiceCloneRequest {
  name: string;
  files: File[];
  description?: string;
  labels?: Record<string, string>;
  remove_background_noise?: boolean;
}

export interface VoiceDesignRequest {
  name: string;
  text: string;
  voice_description: string;
  generated_voice_id?: string;
}

export interface HistoryItem {
  history_item_id: string;
  request_id: string;
  voice_id: string;
  voice_name: string;
  voice_category: string;
  model_id: string;
  text: string;
  date_unix: number;
  character_count_change_from: number;
  character_count_change_to: number;
  content_type: string;
  state: string;
  settings: {
    stability: number;
    similarity_boost: number;
    style: number;
    use_speaker_boost: boolean;
  };
  feedback: {
    thumbs_up: boolean;
    feedback: string;
    emotions: boolean;
    inaccurate_clone: boolean;
    glitches: boolean;
    audio_quality: boolean;
    other: boolean;
  };
  share_link_id: string;
  source: string;
}

export interface UserSubscription {
  tier: string;
  character_count: number;
  character_limit: number;
  can_extend_character_limit: boolean;
  allowed_to_extend_character_limit: boolean;
  next_character_count_reset_unix: number;
  voice_limit: number;
  max_voice_add_edits: number;
  voice_add_edit_counter: number;
  professional_voice_limit: number;
  can_extend_voice_limit: boolean;
  can_use_instant_voice_cloning: boolean;
  can_use_professional_voice_cloning: boolean;
  currency: string;
  status: string;
  billing_period: string;
  character_refresh_period: string;
  next_invoice_date: string;
  has_open_invoices: boolean;
}

export interface ElevenLabsError {
  detail: {
    loc: string[];
    msg: string;
    type: string;
  }[];
}

export interface StreamingOptions {
  chunk_length_schedule?: number[];
  enable_ssml_parsing?: boolean;
  output_format?: 'mp3_22050_32' | 'mp3_44100_32' | 'mp3_44100_64' | 'mp3_44100_96' | 'mp3_44100_128' | 'mp3_44100_192' | 'pcm_16000' | 'pcm_22050' | 'pcm_24000' | 'pcm_44100' | 'ulaw_8000';
  try_trigger_generation?: boolean;
}

export interface VoiceLibraryResponse {
  voices: ElevenLabsVoice[];
}

export interface GenerationState {
  isGenerating: boolean;
  progress: number;
  currentText: string;
  error: string | null;
  generatedAudio: ArrayBuffer | null;
  audioUrl: string | null;
  alignment: TTSResponse['alignment'] | null;
}

export interface VoiceCloneState {
  isCloning: boolean;
  progress: number;
  clonedVoices: ElevenLabsVoice[];
  error: string | null;
}

export interface VoiceDesignState {
  isDesigning: boolean;
  progress: number;
  designedVoices: ElevenLabsVoice[];
  error: string | null;
}

export interface HistoryState {
  items: HistoryItem[];
  isLoading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
}

export interface UserState {
  subscription: UserSubscription | null;
  isLoading: boolean;
  error: string | null;
}

export interface ElevenLabsStore {
  // API Configuration
  apiKey: string | null;
  baseUrl: string;
  
  // Voices
  voices: ElevenLabsVoice[];
  selectedVoice: ElevenLabsVoice | null;
  isLoadingVoices: boolean;
  
  // Models
  models: ElevenLabsModel[];
  selectedModel: ElevenLabsModel | null;
  isLoadingModels: boolean;
  
  // Generation
  generation: GenerationState;
  
  // Voice Cloning
  voiceClone: VoiceCloneState;
  
  // Voice Design
  voiceDesign: VoiceDesignState;
  
  // History
  history: HistoryState;
  
  // User
  user: UserState;
  
  // Actions
  setApiKey: (key: string) => void;
  loadVoices: () => Promise<void>;
  loadModels: () => Promise<void>;
  selectVoice: (voice: ElevenLabsVoice) => void;
  selectModel: (model: ElevenLabsModel) => void;
  generateSpeech: (request: TTSRequest) => Promise<void>;
  streamSpeech: (request: TTSRequest, options?: StreamingOptions) => Promise<ReadableStream>;
  cloneVoice: (request: VoiceCloneRequest) => Promise<void>;
  designVoice: (request: VoiceDesignRequest) => Promise<void>;
  loadHistory: (page?: number, pageSize?: number) => Promise<void>;
  loadUser: () => Promise<void>;
  clearError: (section: keyof Pick<ElevenLabsStore, 'generation' | 'voiceClone' | 'voiceDesign' | 'history' | 'user'>) => void;
  reset: () => void;
}

// Re-export conversational AI types
export * from './conversational-ai-types';