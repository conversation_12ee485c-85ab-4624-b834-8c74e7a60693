// ElevenLabs Voice Service
// Voice management and operations

import { ElevenLabsService } from './elevenlabs-service';
import { validateVoiceCloneFiles, getAudioDuration } from '../utils';
import type { ElevenLabsVoice, VoiceCloneRequest, VoiceDesignRequest } from '../types';

export class ElevenLabsVoiceService extends ElevenLabsService {
  /**
   * Get voices with caching
   */
  private voiceCache: Map<string, { voices: ElevenLabsVoice[]; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  async getVoicesWithCache(useCache: boolean = true): Promise<ElevenLabsVoice[]> {
    const cacheKey = 'voices';
    const cached = this.voiceCache.get(cacheKey);
    
    if (useCache && cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.voices;
    }

    const voices = await super.getVoices();
    this.voiceCache.set(cacheKey, { voices, timestamp: Date.now() });
    
    return voices;
  }

  /**
   * Search voices by criteria
   */
  async searchVoices(criteria: {
    name?: string;
    gender?: string;
    age?: string;
    accent?: string;
    category?: string;
    language?: string;
  }): Promise<ElevenLabsVoice[]> {
    const voices = await this.getVoicesWithCache();
    
    return voices.filter(voice => {
      if (criteria.name && !voice.name.toLowerCase().includes(criteria.name.toLowerCase())) {
        return false;
      }
      
      if (criteria.gender && voice.labels?.gender !== criteria.gender) {
        return false;
      }
      
      if (criteria.age && voice.labels?.age !== criteria.age) {
        return false;
      }
      
      if (criteria.accent && voice.labels?.accent !== criteria.accent) {
        return false;
      }
      
      if (criteria.category && voice.category !== criteria.category) {
        return false;
      }
      
      if (criteria.language && voice.labels?.language !== criteria.language) {
        return false;
      }
      
      return true;
    });
  }

  /**
   * Get voice recommendations
   */
  async getVoiceRecommendations(
    criteria: {
      useCase?: 'audiobook' | 'podcast' | 'commercial' | 'storytelling' | 'conversation';
      gender?: 'male' | 'female';
      age?: 'young' | 'middle_aged' | 'old';
      accent?: string;
      language?: string;
    }
  ): Promise<ElevenLabsVoice[]> {
    const voices = await this.getVoicesWithCache();
    
    // Score voices based on criteria
    const scoredVoices = voices.map(voice => {
      let score = 0;
      
      // Gender match
      if (criteria.gender && voice.labels?.gender === criteria.gender) {
        score += 3;
      }
      
      // Age match
      if (criteria.age && voice.labels?.age === criteria.age) {
        score += 2;
      }
      
      // Accent match
      if (criteria.accent && voice.labels?.accent === criteria.accent) {
        score += 2;
      }
      
      // Language match
      if (criteria.language && voice.labels?.language === criteria.language) {
        score += 3;
      }
      
      // Use case scoring
      if (criteria.useCase && voice.labels?.['use case']) {
        const useCases = voice.labels['use case'].split(',').map(uc => uc.trim().toLowerCase());
        if (useCases.includes(criteria.useCase)) {
          score += 5;
        }
      }
      
      // Prefer high-quality voices
      if (voice.category === 'professional') {
        score += 2;
      }
      
      return { voice, score };
    });
    
    // Sort by score and return top recommendations
    return scoredVoices
      .sort((a, b) => b.score - a.score)
      .slice(0, 10)
      .map(item => item.voice);
  }

  /**
   * Validate voice clone request
   */
  async validateVoiceCloneRequest(request: VoiceCloneRequest): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate name
    if (!request.name || request.name.trim().length < 1) {
      errors.push('Voice name is required');
    }

    if (request.name && request.name.length > 100) {
      errors.push('Voice name is too long (max 100 characters)');
    }

    // Validate files
    const fileErrors = validateVoiceCloneFiles(request.files);
    errors.push(...fileErrors);

    // Check audio duration
    try {
      const durations = await Promise.all(
        request.files.map(file => getAudioDuration(file))
      );
      
      const totalDuration = durations.reduce((sum, duration) => sum + duration, 0);
      
      if (totalDuration < 60) {
        warnings.push('Audio duration is less than 1 minute. Consider adding more audio for better quality.');
      }
      
      if (totalDuration > 300) {
        warnings.push('Audio duration is more than 5 minutes. This may increase processing time.');
      }
    } catch (error) {
      warnings.push('Could not validate audio duration');
    }

    // Check for existing voice with same name
    try {
      const existingVoices = await this.getVoicesWithCache();
      const nameExists = existingVoices.some(
        voice => voice.name.toLowerCase() === request.name.toLowerCase()
      );
      
      if (nameExists) {
        warnings.push('A voice with this name already exists');
      }
    } catch (error) {
      warnings.push('Could not check for existing voice names');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Clone voice with validation
   */
  async cloneVoiceWithValidation(request: VoiceCloneRequest): Promise<{
    voice?: ElevenLabsVoice;
    errors?: string[];
    warnings?: string[];
  }> {
    const validation = await this.validateVoiceCloneRequest(request);
    
    if (!validation.isValid) {
      return {
        errors: validation.errors,
        warnings: validation.warnings,
      };
    }

    try {
      const voice = await this.cloneVoice(request);
      
      // Clear cache to include new voice
      this.voiceCache.clear();
      
      return {
        voice,
        warnings: validation.warnings,
      };
    } catch (error) {
      return {
        errors: [error instanceof Error ? error.message : 'Voice cloning failed'],
        warnings: validation.warnings,
      };
    }
  }

  /**
   * Design voice with validation
   */
  async designVoiceWithValidation(request: VoiceDesignRequest): Promise<{
    voice?: ElevenLabsVoice;
    errors?: string[];
    warnings?: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate name
    if (!request.name || request.name.trim().length < 1) {
      errors.push('Voice name is required');
    }

    // Validate text
    if (!request.text || request.text.trim().length < 50) {
      errors.push('Text must be at least 50 characters long');
    }

    if (request.text && request.text.length > 1000) {
      errors.push('Text is too long (max 1000 characters)');
    }

    // Validate description
    if (!request.voice_description || request.voice_description.trim().length < 10) {
      errors.push('Voice description must be at least 10 characters long');
    }

    if (request.voice_description && request.voice_description.length > 500) {
      errors.push('Voice description is too long (max 500 characters)');
    }

    if (errors.length > 0) {
      return { errors, warnings };
    }

    try {
      const voice = await this.designVoice(request);
      
      // Clear cache to include new voice
      this.voiceCache.clear();
      
      return {
        voice,
        warnings,
      };
    } catch (error) {
      return {
        errors: [error instanceof Error ? error.message : 'Voice design failed'],
        warnings,
      };
    }
  }

  /**
   * Get voice usage statistics
   */
  async getVoiceUsage(voiceId: string): Promise<{
    totalGenerations: number;
    totalCharacters: number;
    averageRating: number;
    lastUsed: Date | null;
  }> {
    // This would typically come from the API, but we'll simulate it
    return {
      totalGenerations: 0,
      totalCharacters: 0,
      averageRating: 0,
      lastUsed: null,
    };
  }

  /**
   * Compare voices
   */
  async compareVoices(voiceIds: string[]): Promise<{
    voices: ElevenLabsVoice[];
    comparison: {
      categories: string[];
      languages: string[];
      genders: string[];
      ages: string[];
      accents: string[];
    };
  }> {
    const voices = await Promise.all(
      voiceIds.map(id => this.getVoice(id))
    );

    const comparison = {
      categories: [...new Set(voices.map(v => v.category))],
      languages: [...new Set(voices.map(v => v.labels?.language).filter(Boolean))],
      genders: [...new Set(voices.map(v => v.labels?.gender).filter(Boolean))],
      ages: [...new Set(voices.map(v => v.labels?.age).filter(Boolean))],
      accents: [...new Set(voices.map(v => v.labels?.accent).filter(Boolean))],
    };

    return {
      voices,
      comparison,
    };
  }

  /**
   * Export voice settings
   */
  async exportVoiceSettings(voiceId: string): Promise<{
    voice: ElevenLabsVoice;
    settings: ElevenLabsVoice['settings'];
    metadata: {
      exportDate: string;
      version: string;
    };
  }> {
    const [voice, settings] = await Promise.all([
      this.getVoice(voiceId),
      this.getVoiceSettings(voiceId),
    ]);

    return {
      voice,
      settings,
      metadata: {
        exportDate: new Date().toISOString(),
        version: '1.0',
      },
    };
  }

  /**
   * Batch update voice settings
   */
  async batchUpdateVoiceSettings(
    updates: Array<{
      voiceId: string;
      settings: Partial<ElevenLabsVoice['settings']>;
    }>
  ): Promise<Array<{
    voiceId: string;
    success: boolean;
    settings?: ElevenLabsVoice['settings'];
    error?: string;
  }>> {
    const results = await Promise.allSettled(
      updates.map(async update => {
        const settings = await this.updateVoiceSettings(update.voiceId, update.settings);
        return {
          voiceId: update.voiceId,
          success: true,
          settings,
        };
      })
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          voiceId: updates[index].voiceId,
          success: false,
          error: result.reason instanceof Error ? result.reason.message : 'Unknown error',
        };
      }
    });
  }

  /**
   * Clear voice cache
   */
  clearCache(): void {
    this.voiceCache.clear();
  }
}