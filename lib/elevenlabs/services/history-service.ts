// ElevenLabs History Service
// Manage generation history and analytics

import { ElevenLabsService } from './elevenlabs-service';
import type { HistoryItem } from '../types';

export class ElevenLabsHistoryService extends ElevenLabsService {
  /**
   * Get generation history
   */
  async getHistory(
    page: number = 1,
    pageSize: number = 100,
    startAfterHistoryItemId?: string
  ): Promise<{
    history: HistoryItem[];
    has_more: boolean;
    last_history_item_id?: string;
  }> {
    const params = new URLSearchParams({
      page_size: pageSize.toString(),
      page: page.toString(),
    });

    if (startAfterHistoryItemId) {
      params.append('start_after_history_item_id', startAfterHistoryItemId);
    }

    const response = await fetch(`${this.baseUrl}/history?${params}`, {
      headers: this.createHeaders(),
    });

    return this.handleResponse<{
      history: HistoryItem[];
      has_more: boolean;
      last_history_item_id?: string;
    }>(response);
  }

  /**
   * Get history item
   */
  async getHistoryItem(historyItemId: string): Promise<HistoryItem> {
    const response = await fetch(`${this.baseUrl}/history/${historyItemId}`, {
      headers: this.createHeaders(),
    });

    return this.handleResponse<HistoryItem>(response);
  }

  /**
   * Get history item audio
   */
  async getHistoryItemAudio(historyItemId: string): Promise<ArrayBuffer> {
    const response = await fetch(`${this.baseUrl}/history/${historyItemId}/audio`, {
      headers: this.createHeaders(),
    });

    return this.handleResponse<ArrayBuffer>(response);
  }

  /**
   * Delete history item
   */
  async deleteHistoryItem(historyItemId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/history/${historyItemId}`, {
      method: 'DELETE',
      headers: this.createHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to delete history item: ${response.statusText}`);
    }
  }

  /**
   * Download history item
   */
  async downloadHistoryItem(historyItemId: string, filename?: string): Promise<void> {
    const [historyItem, audioBuffer] = await Promise.all([
      this.getHistoryItem(historyItemId),
      this.getHistoryItemAudio(historyItemId),
    ]);

    const blob = new Blob([audioBuffer], { type: 'audio/mpeg' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename || `${historyItem.voice_name}_${historyItem.date_unix}.mp3`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * Get history analytics
   */
  async getHistoryAnalytics(
    timeRange: 'last_7_days' | 'last_30_days' | 'last_90_days' | 'all_time' = 'last_30_days'
  ): Promise<{
    totalGenerations: number;
    totalCharacters: number;
    averageCharactersPerGeneration: number;
    mostUsedVoices: Array<{
      voice_id: string;
      voice_name: string;
      count: number;
    }>;
    mostUsedModels: Array<{
      model_id: string;
      count: number;
    }>;
    generationsByDay: Array<{
      date: string;
      count: number;
      characters: number;
    }>;
    topCategories: Array<{
      category: string;
      count: number;
    }>;
  }> {
    // Get all history items for the specified time range
    const cutoffDate = this.getCutoffDate(timeRange);
    const allItems = await this.getAllHistoryItems(cutoffDate);

    const analytics = {
      totalGenerations: allItems.length,
      totalCharacters: allItems.reduce((sum, item) => sum + item.text.length, 0),
      averageCharactersPerGeneration: 0,
      mostUsedVoices: this.getMostUsedVoices(allItems),
      mostUsedModels: this.getMostUsedModels(allItems),
      generationsByDay: this.getGenerationsByDay(allItems),
      topCategories: this.getTopCategories(allItems),
    };

    analytics.averageCharactersPerGeneration = 
      analytics.totalGenerations > 0 
        ? Math.round(analytics.totalCharacters / analytics.totalGenerations)
        : 0;

    return analytics;
  }

  /**
   * Search history
   */
  async searchHistory(query: {
    text?: string;
    voice_id?: string;
    model_id?: string;
    start_date?: Date;
    end_date?: Date;
    page?: number;
    pageSize?: number;
  }): Promise<{
    history: HistoryItem[];
    has_more: boolean;
    total_count: number;
  }> {
    const allItems = await this.getAllHistoryItems();
    
    let filtered = allItems.filter(item => {
      if (query.text && !item.text.toLowerCase().includes(query.text.toLowerCase())) {
        return false;
      }
      
      if (query.voice_id && item.voice_id !== query.voice_id) {
        return false;
      }
      
      if (query.model_id && item.model_id !== query.model_id) {
        return false;
      }
      
      if (query.start_date && new Date(item.date_unix * 1000) < query.start_date) {
        return false;
      }
      
      if (query.end_date && new Date(item.date_unix * 1000) > query.end_date) {
        return false;
      }
      
      return true;
    });

    const page = query.page || 1;
    const pageSize = query.pageSize || 100;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;

    return {
      history: filtered.slice(start, end),
      has_more: end < filtered.length,
      total_count: filtered.length,
    };
  }

  /**
   * Export history
   */
  async exportHistory(
    format: 'json' | 'csv' = 'json',
    filters?: {
      start_date?: Date;
      end_date?: Date;
      voice_id?: string;
      model_id?: string;
    }
  ): Promise<string> {
    const allItems = await this.getAllHistoryItems();
    
    let filtered = allItems;
    if (filters) {
      filtered = allItems.filter(item => {
        if (filters.start_date && new Date(item.date_unix * 1000) < filters.start_date) {
          return false;
        }
        
        if (filters.end_date && new Date(item.date_unix * 1000) > filters.end_date) {
          return false;
        }
        
        if (filters.voice_id && item.voice_id !== filters.voice_id) {
          return false;
        }
        
        if (filters.model_id && item.model_id !== filters.model_id) {
          return false;
        }
        
        return true;
      });
    }

    if (format === 'json') {
      return JSON.stringify(filtered, null, 2);
    } else {
      return this.convertToCSV(filtered);
    }
  }

  /**
   * Get usage statistics
   */
  async getUsageStatistics(): Promise<{
    current_period: {
      characters_used: number;
      characters_limit: number;
      generations_count: number;
    };
    previous_period: {
      characters_used: number;
      generations_count: number;
    };
    trend: {
      characters_change: number;
      generations_change: number;
    };
  }> {
    const now = new Date();
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const previousMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const previousMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    const [currentItems, previousItems] = await Promise.all([
      this.getAllHistoryItems(currentMonthStart),
      this.getAllHistoryItems(previousMonthStart, previousMonthEnd),
    ]);

    const currentCharacters = currentItems.reduce((sum, item) => sum + item.text.length, 0);
    const previousCharacters = previousItems.reduce((sum, item) => sum + item.text.length, 0);

    const charactersChange = currentCharacters - previousCharacters;
    const generationsChange = currentItems.length - previousItems.length;

    return {
      current_period: {
        characters_used: currentCharacters,
        characters_limit: 10000, // This should come from user subscription
        generations_count: currentItems.length,
      },
      previous_period: {
        characters_used: previousCharacters,
        generations_count: previousItems.length,
      },
      trend: {
        characters_change: charactersChange,
        generations_change: generationsChange,
      },
    };
  }

  /**
   * Private helper methods
   */
  private getCutoffDate(timeRange: string): Date {
    const now = new Date();
    switch (timeRange) {
      case 'last_7_days':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case 'last_30_days':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      case 'last_90_days':
        return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      default:
        return new Date(0);
    }
  }

  private async getAllHistoryItems(startDate?: Date, endDate?: Date): Promise<HistoryItem[]> {
    const allItems: HistoryItem[] = [];
    let page = 1;
    let hasMore = true;

    while (hasMore) {
      const response = await this.getHistory(page, 100);
      const filteredItems = response.history.filter(item => {
        const itemDate = new Date(item.date_unix * 1000);
        
        if (startDate && itemDate < startDate) {
          return false;
        }
        
        if (endDate && itemDate > endDate) {
          return false;
        }
        
        return true;
      });

      allItems.push(...filteredItems);
      hasMore = response.has_more;
      page++;
    }

    return allItems;
  }

  private getMostUsedVoices(items: HistoryItem[]): Array<{
    voice_id: string;
    voice_name: string;
    count: number;
  }> {
    const voiceCount = new Map<string, { name: string; count: number }>();
    
    items.forEach(item => {
      const existing = voiceCount.get(item.voice_id);
      if (existing) {
        existing.count++;
      } else {
        voiceCount.set(item.voice_id, {
          name: item.voice_name,
          count: 1,
        });
      }
    });

    return Array.from(voiceCount.entries())
      .map(([voice_id, data]) => ({
        voice_id,
        voice_name: data.name,
        count: data.count,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  private getMostUsedModels(items: HistoryItem[]): Array<{
    model_id: string;
    count: number;
  }> {
    const modelCount = new Map<string, number>();
    
    items.forEach(item => {
      const existing = modelCount.get(item.model_id);
      modelCount.set(item.model_id, (existing || 0) + 1);
    });

    return Array.from(modelCount.entries())
      .map(([model_id, count]) => ({ model_id, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  private getGenerationsByDay(items: HistoryItem[]): Array<{
    date: string;
    count: number;
    characters: number;
  }> {
    const dayCount = new Map<string, { count: number; characters: number }>();
    
    items.forEach(item => {
      const date = new Date(item.date_unix * 1000).toISOString().split('T')[0];
      const existing = dayCount.get(date);
      
      if (existing) {
        existing.count++;
        existing.characters += item.text.length;
      } else {
        dayCount.set(date, {
          count: 1,
          characters: item.text.length,
        });
      }
    });

    return Array.from(dayCount.entries())
      .map(([date, data]) => ({
        date,
        count: data.count,
        characters: data.characters,
      }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }

  private getTopCategories(items: HistoryItem[]): Array<{
    category: string;
    count: number;
  }> {
    const categoryCount = new Map<string, number>();
    
    items.forEach(item => {
      const existing = categoryCount.get(item.voice_category);
      categoryCount.set(item.voice_category, (existing || 0) + 1);
    });

    return Array.from(categoryCount.entries())
      .map(([category, count]) => ({ category, count }))
      .sort((a, b) => b.count - a.count);
  }

  private convertToCSV(items: HistoryItem[]): string {
    const headers = [
      'ID',
      'Date',
      'Voice Name',
      'Voice Category',
      'Model ID',
      'Text',
      'Character Count',
      'Settings',
    ];

    const rows = items.map(item => [
      item.history_item_id,
      new Date(item.date_unix * 1000).toISOString(),
      item.voice_name,
      item.voice_category,
      item.model_id,
      `"${item.text.replace(/"/g, '""')}"`,
      item.text.length.toString(),
      JSON.stringify(item.settings),
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }
}