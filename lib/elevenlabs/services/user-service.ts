// ElevenLabs User Service
// User account and subscription management

import { ElevenLabsService } from './elevenlabs-service';
import type { UserSubscription } from '../types';

export class ElevenLabsUserService extends ElevenLabsService {
  /**
   * Get user information
   */
  async getUser(): Promise<{
    subscription: UserSubscription;
    is_new_user: boolean;
    xi_api_key: string;
    can_extend_character_limit: boolean;
    allowed_to_extend_character_limit: boolean;
    professional_voice_limit: number;
    can_extend_voice_limit: boolean;
    can_use_instant_voice_cloning: boolean;
    can_use_professional_voice_cloning: boolean;
    can_use_delayed_payment_methods: boolean;
    is_onboarded: boolean;
    first_name: string;
    is_api_user: boolean;
    avatar_url: string;
  }> {
    const response = await fetch(`${this.baseUrl}/user`, {
      headers: this.createHeaders(),
    });

    return this.handleResponse<{
      subscription: UserSubscription;
      is_new_user: boolean;
      xi_api_key: string;
      can_extend_character_limit: boolean;
      allowed_to_extend_character_limit: boolean;
      professional_voice_limit: number;
      can_extend_voice_limit: boolean;
      can_use_instant_voice_cloning: boolean;
      can_use_professional_voice_cloning: boolean;
      can_use_delayed_payment_methods: boolean;
      is_onboarded: boolean;
      first_name: string;
      is_api_user: boolean;
      avatar_url: string;
    }>(response);
  }

  /**
   * Get subscription information
   */
  async getSubscription(): Promise<UserSubscription> {
    const response = await fetch(`${this.baseUrl}/user/subscription`, {
      headers: this.createHeaders(),
    });

    return this.handleResponse<UserSubscription>(response);
  }

  /**
   * Get usage statistics
   */
  async getUsageStatistics(): Promise<{
    character_count: number;
    character_limit: number;
    next_character_count_reset_unix: number;
    voice_count: number;
    voice_limit: number;
    professional_voice_count: number;
    professional_voice_limit: number;
    instant_voice_clone_count: number;
    instant_voice_clone_limit: number;
    usage_percentage: number;
    days_until_reset: number;
  }> {
    const subscription = await this.getSubscription();
    const now = new Date();
    const resetDate = new Date(subscription.next_character_count_reset_unix * 1000);
    const daysUntilReset = Math.ceil((resetDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    return {
      character_count: subscription.character_count,
      character_limit: subscription.character_limit,
      next_character_count_reset_unix: subscription.next_character_count_reset_unix,
      voice_count: 0, // This would come from voices count
      voice_limit: subscription.voice_limit,
      professional_voice_count: 0, // This would come from professional voices count
      professional_voice_limit: subscription.professional_voice_limit,
      instant_voice_clone_count: 0, // This would come from IVC count
      instant_voice_clone_limit: subscription.can_use_instant_voice_cloning ? 10 : 0,
      usage_percentage: Math.round((subscription.character_count / subscription.character_limit) * 100),
      days_until_reset: daysUntilReset,
    };
  }

  /**
   * Check if user has sufficient quota
   */
  async checkQuota(charactersNeeded: number): Promise<{
    has_quota: boolean;
    current_count: number;
    limit: number;
    available: number;
    percentage_used: number;
    reset_date: Date;
  }> {
    const subscription = await this.getSubscription();
    const available = subscription.character_limit - subscription.character_count;
    
    return {
      has_quota: available >= charactersNeeded,
      current_count: subscription.character_count,
      limit: subscription.character_limit,
      available,
      percentage_used: Math.round((subscription.character_count / subscription.character_limit) * 100),
      reset_date: new Date(subscription.next_character_count_reset_unix * 1000),
    };
  }

  /**
   * Get billing information
   */
  async getBillingInfo(): Promise<{
    subscription: UserSubscription;
    next_invoice_date: Date;
    billing_period: string;
    currency: string;
    has_open_invoices: boolean;
    payment_method: string;
    auto_renew: boolean;
  }> {
    const subscription = await this.getSubscription();
    
    return {
      subscription,
      next_invoice_date: new Date(subscription.next_invoice_date),
      billing_period: subscription.billing_period,
      currency: subscription.currency,
      has_open_invoices: subscription.has_open_invoices,
      payment_method: 'Card', // This would come from billing API
      auto_renew: true, // This would come from billing API
    };
  }

  /**
   * Get available plans
   */
  async getAvailablePlans(): Promise<Array<{
    id: string;
    name: string;
    price: number;
    currency: string;
    billing_period: string;
    character_limit: number;
    voice_limit: number;
    professional_voice_limit: number;
    features: string[];
    is_current: boolean;
    is_recommended: boolean;
  }>> {
    // This would typically come from the API
    const currentSubscription = await this.getSubscription();
    
    const plans = [
      {
        id: 'free',
        name: 'Free',
        price: 0,
        currency: 'USD',
        billing_period: 'monthly',
        character_limit: 10000,
        voice_limit: 3,
        professional_voice_limit: 0,
        features: [
          '10,000 characters/month',
          '3 custom voices',
          'Instant voice cloning',
          'Basic voice design',
        ],
        is_current: currentSubscription.tier === 'free',
        is_recommended: false,
      },
      {
        id: 'starter',
        name: 'Starter',
        price: 5,
        currency: 'USD',
        billing_period: 'monthly',
        character_limit: 30000,
        voice_limit: 10,
        professional_voice_limit: 0,
        features: [
          '30,000 characters/month',
          '10 custom voices',
          'Instant voice cloning',
          'Voice design',
          'API access',
        ],
        is_current: currentSubscription.tier === 'starter',
        is_recommended: false,
      },
      {
        id: 'creator',
        name: 'Creator',
        price: 22,
        currency: 'USD',
        billing_period: 'monthly',
        character_limit: 100000,
        voice_limit: 30,
        professional_voice_limit: 3,
        features: [
          '100,000 characters/month',
          '30 custom voices',
          '3 professional voice clones',
          'Instant voice cloning',
          'Voice design',
          'API access',
          'Commercial license',
        ],
        is_current: currentSubscription.tier === 'creator',
        is_recommended: true,
      },
      {
        id: 'pro',
        name: 'Pro',
        price: 99,
        currency: 'USD',
        billing_period: 'monthly',
        character_limit: 500000,
        voice_limit: 160,
        professional_voice_limit: 10,
        features: [
          '500,000 characters/month',
          '160 custom voices',
          '10 professional voice clones',
          'Instant voice cloning',
          'Voice design',
          'API access',
          'Commercial license',
          'Priority support',
        ],
        is_current: currentSubscription.tier === 'pro',
        is_recommended: false,
      },
      {
        id: 'scale',
        name: 'Scale',
        price: 330,
        currency: 'USD',
        billing_period: 'monthly',
        character_limit: 2000000,
        voice_limit: 660,
        professional_voice_limit: 25,
        features: [
          '2,000,000 characters/month',
          '660 custom voices',
          '25 professional voice clones',
          'Instant voice cloning',
          'Voice design',
          'API access',
          'Commercial license',
          'Priority support',
          'Dedicated account manager',
        ],
        is_current: currentSubscription.tier === 'scale',
        is_recommended: false,
      },
    ];

    return plans;
  }

  /**
   * Get feature availability
   */
  async getFeatureAvailability(): Promise<{
    instant_voice_cloning: boolean;
    professional_voice_cloning: boolean;
    voice_design: boolean;
    api_access: boolean;
    commercial_license: boolean;
    priority_support: boolean;
    custom_models: boolean;
    bulk_generation: boolean;
    pronunciation_dictionaries: boolean;
    voice_library_access: boolean;
    streaming: boolean;
    timestamps: boolean;
    ssml: boolean;
    voice_mixing: boolean;
    voice_effects: boolean;
  }> {
    const subscription = await this.getSubscription();
    
    return {
      instant_voice_cloning: subscription.can_use_instant_voice_cloning,
      professional_voice_cloning: subscription.can_use_professional_voice_cloning,
      voice_design: ['creator', 'pro', 'scale'].includes(subscription.tier),
      api_access: subscription.tier !== 'free',
      commercial_license: ['creator', 'pro', 'scale'].includes(subscription.tier),
      priority_support: ['pro', 'scale'].includes(subscription.tier),
      custom_models: subscription.tier === 'scale',
      bulk_generation: ['pro', 'scale'].includes(subscription.tier),
      pronunciation_dictionaries: ['creator', 'pro', 'scale'].includes(subscription.tier),
      voice_library_access: true,
      streaming: true,
      timestamps: true,
      ssml: ['creator', 'pro', 'scale'].includes(subscription.tier),
      voice_mixing: subscription.tier === 'scale',
      voice_effects: ['pro', 'scale'].includes(subscription.tier),
    };
  }

  /**
   * Get usage history
   */
  async getUsageHistory(
    _period: 'last_7_days' | 'last_30_days' | 'last_90_days' | 'all_time' = 'last_30_days'
  ): Promise<Array<{
    date: string;
    characters_used: number;
    generations_count: number;
    voices_used: string[];
    models_used: string[];
  }>> {
    // This would typically integrate with the history service
    // For now, return empty array
    return [];
  }

  /**
   * Estimate cost for text
   */
  async estimateCost(text: string): Promise<{
    character_count: number;
    estimated_cost: number;
    currency: string;
    will_exceed_quota: boolean;
    characters_remaining: number;
    reset_date: Date;
  }> {
    const subscription = await this.getSubscription();
    const characterCount = text.length;
    const charactersRemaining = subscription.character_limit - subscription.character_count;
    
    // Cost estimation (this would come from actual pricing)
    const costPerCharacter = 0.0002; // Example cost
    const estimatedCost = characterCount * costPerCharacter;
    
    return {
      character_count: characterCount,
      estimated_cost: estimatedCost,
      currency: subscription.currency,
      will_exceed_quota: charactersRemaining < characterCount,
      characters_remaining: charactersRemaining,
      reset_date: new Date(subscription.next_character_count_reset_unix * 1000),
    };
  }

  /**
   * Get API keys
   */
  async getApiKeys(): Promise<Array<{
    key_id: string;
    key_name: string;
    key_preview: string;
    created_at: Date;
    last_used: Date | null;
    is_active: boolean;
    permissions: string[];
  }>> {
    // This would typically come from the API
    return [];
  }

  /**
   * Generate new API key
   */
  async generateApiKey(_name: string, _permissions: string[] = []): Promise<{
    key_id: string;
    key_name: string;
    api_key: string;
    created_at: Date;
    permissions: string[];
  }> {
    // This would typically call the API
    throw new Error('API key generation not implemented');
  }

  /**
   * Revoke API key
   */
  async revokeApiKey(_keyId: string): Promise<void> {
    // This would typically call the API
    throw new Error('API key revocation not implemented');
  }

  /**
   * Update user profile
   */
  async updateProfile(_updates: {
    first_name?: string;
    avatar_url?: string;
    preferences?: {
      language?: string;
      timezone?: string;
      notifications?: {
        email: boolean;
        push: boolean;
      };
    };
  }): Promise<void> {
    // This would typically call the API
    throw new Error('Profile update not implemented');
  }

  /**
   * Get notification preferences
   */
  async getNotificationPreferences(): Promise<{
    email_notifications: boolean;
    push_notifications: boolean;
    quota_warnings: boolean;
    new_features: boolean;
    marketing: boolean;
  }> {
    // This would typically come from the API
    return {
      email_notifications: true,
      push_notifications: true,
      quota_warnings: true,
      new_features: true,
      marketing: false,
    };
  }

  /**
   * Update notification preferences
   */
  async updateNotificationPreferences(_preferences: {
    email_notifications?: boolean;
    push_notifications?: boolean;
    quota_warnings?: boolean;
    new_features?: boolean;
    marketing?: boolean;
  }): Promise<void> {
    // This would typically call the API
    throw new Error('Notification preferences update not implemented');
  }
}