// ElevenLabs Streaming Service
// WebSocket and streaming functionality for real-time TTS

import { ELEVENLABS_API_BASE_URL, ELEVENLABS_MODELS, ELEVENLABS_OUTPUT_FORMATS } from '../constants';
import { validateApiKey, validateTTSRequest } from '../utils';
import type { TTSRequest, StreamingOptions } from '../types';

export class ElevenLabsStreamingService {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string, baseUrl: string = ELEVENLABS_API_BASE_URL) {
    if (!validateApiKey(apiKey)) {
      throw new Error('Invalid API key format');
    }
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  /**
   * Create headers for streaming requests
   */
  private createHeaders(): Record<string, string> {
    return {
      'xi-api-key': this.apiKey,
      'Content-Type': 'application/json',
      'User-Agent': 'TuneBase/1.0',
    };
  }

  /**
   * Stream speech synthesis
   */
  async streamTextToSpeech(
    request: TTSRequest,
    options: StreamingOptions = {}
  ): Promise<ReadableStream<Uint8Array>> {
    const validationErrors = validateTTSRequest(request);
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
    }

    const body = {
      text: request.text,
      model_id: request.model_id || ELEVENLABS_MODELS.ELEVEN_MULTILINGUAL_V2,
      voice_settings: request.voice_settings,
      pronunciation_dictionary_locators: request.pronunciation_dictionary_locators,
      seed: request.seed,
      previous_text: request.previous_text,
      next_text: request.next_text,
      previous_request_ids: request.previous_request_ids,
      next_request_ids: request.next_request_ids,
      output_format: request.output_format || ELEVENLABS_OUTPUT_FORMATS.MP3_44100_128,
      apply_text_normalization: request.apply_text_normalization || 'auto',
      ...options,
    };

    const response = await fetch(`${this.baseUrl}/text-to-speech/${request.voice_id}/stream`, {
      method: 'POST',
      headers: this.createHeaders(),
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Streaming failed: ${response.status} ${errorText}`);
    }

    if (!response.body) {
      throw new Error('No response body received');
    }

    return response.body;
  }

  /**
   * Stream speech synthesis with optimizations
   */
  async streamTextToSpeechOptimized(
    request: TTSRequest,
    options: StreamingOptions = {}
  ): Promise<ReadableStream<Uint8Array>> {
    const validationErrors = validateTTSRequest(request);
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
    }

    const body = {
      text: request.text,
      model_id: request.model_id || ELEVENLABS_MODELS.ELEVEN_FLASH_V2_5, // Use Flash model for streaming
      voice_settings: request.voice_settings,
      pronunciation_dictionary_locators: request.pronunciation_dictionary_locators,
      seed: request.seed,
      previous_text: request.previous_text,
      next_text: request.next_text,
      previous_request_ids: request.previous_request_ids,
      next_request_ids: request.next_request_ids,
      output_format: request.output_format || ELEVENLABS_OUTPUT_FORMATS.MP3_44100_128,
      apply_text_normalization: request.apply_text_normalization || 'auto',
      optimize_streaming_latency: 4, // Optimize for streaming
      ...options,
    };

    const response = await fetch(`${this.baseUrl}/text-to-speech/${request.voice_id}/stream`, {
      method: 'POST',
      headers: this.createHeaders(),
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Streaming failed: ${response.status} ${errorText}`);
    }

    if (!response.body) {
      throw new Error('No response body received');
    }

    return response.body;
  }

  /**
   * Create WebSocket connection for real-time streaming
   */
  createWebSocketConnection(
    voiceId: string,
    options: {
      model_id?: string;
      voice_settings?: TTSRequest['voice_settings'];
      output_format?: string;
      enable_ssml_parsing?: boolean;
      chunk_length_schedule?: number[];
      try_trigger_generation?: boolean;
    } = {}
  ): WebSocket {
    const wsUrl = this.baseUrl.replace('https://', 'wss://').replace('http://', 'ws://');
    const params = new URLSearchParams({
      'xi-api-key': this.apiKey,
      model_id: options.model_id || ELEVENLABS_MODELS.ELEVEN_FLASH_V2_5,
      output_format: options.output_format || ELEVENLABS_OUTPUT_FORMATS.MP3_44100_128,
      voice_settings: options.voice_settings ? JSON.stringify(options.voice_settings) : '',
      enable_ssml_parsing: options.enable_ssml_parsing?.toString() || 'false',
      chunk_length_schedule: options.chunk_length_schedule ? JSON.stringify(options.chunk_length_schedule) : '',
      try_trigger_generation: options.try_trigger_generation?.toString() || 'false',
    });

    const ws = new WebSocket(`${wsUrl}/text-to-speech/${voiceId}/stream-input?${params}`);
    
    return ws;
  }

  /**
   * Stream audio with custom processing
   */
  async streamWithProcessor(
    request: TTSRequest,
    processor: (chunk: Uint8Array) => void,
    options: StreamingOptions = {}
  ): Promise<void> {
    const stream = await this.streamTextToSpeech(request, options);
    const reader = stream.getReader();

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        if (value) {
          processor(value);
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * Stream to audio element
   */
  async streamToAudioElement(
    request: TTSRequest,
    audioElement: HTMLAudioElement,
    options: StreamingOptions = {}
  ): Promise<void> {
    const stream = await this.streamTextToSpeech(request, options);
    const mediaSource = new MediaSource();
    
    return new Promise((resolve, reject) => {
      audioElement.src = URL.createObjectURL(mediaSource);
      
      mediaSource.addEventListener('sourceopen', async () => {
        try {
          const mimeType = 'audio/mpeg';
          const sourceBuffer = mediaSource.addSourceBuffer(mimeType);
          const reader = stream.getReader();
          
          let isFirstChunk = true;
          
          const processChunk = async () => {
            try {
              const { done, value } = await reader.read();
              
              if (done) {
                if (mediaSource.readyState === 'open') {
                  mediaSource.endOfStream();
                }
                resolve();
                return;
              }
              
              if (value && !sourceBuffer.updating) {
                sourceBuffer.appendBuffer(value);
                
                if (isFirstChunk) {
                  isFirstChunk = false;
                  audioElement.play().catch(reject);
                }
              }
              
              sourceBuffer.addEventListener('updateend', processChunk, { once: true });
            } catch (error) {
              reject(error);
            }
          };
          
          processChunk();
        } catch (error) {
          reject(error);
        }
      });
      
      mediaSource.addEventListener('error', reject);
      audioElement.addEventListener('error', reject);
    });
  }

  /**
   * Stream to buffer
   */
  async streamToBuffer(
    request: TTSRequest,
    options: StreamingOptions = {}
  ): Promise<ArrayBuffer> {
    const stream = await this.streamTextToSpeech(request, options);
    const reader = stream.getReader();
    const chunks: Uint8Array[] = [];

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        if (value) {
          chunks.push(value);
        }
      }
    } finally {
      reader.releaseLock();
    }

    // Combine all chunks into a single ArrayBuffer
    const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
    const result = new Uint8Array(totalLength);
    let offset = 0;
    
    for (const chunk of chunks) {
      result.set(chunk, offset);
      offset += chunk.length;
    }

    return result.buffer;
  }

  /**
   * Cancel streaming
   */
  cancelStream(reader: ReadableStreamDefaultReader<Uint8Array>): void {
    reader.cancel();
  }
}