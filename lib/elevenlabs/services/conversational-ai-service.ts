// ElevenLabs Conversational AI Service
// Real-time voice conversation capabilities with WebSocket integration

import { EventEmitter } from 'events';
import {
  ConversationalAIAgent,
  ConversationalAISession,
  ConversationalMessage,
  ConversationalAIConfig,
  ConversationalAIError,
  TurnTakingSettings,
  AudioConfig,
  SessionMetrics
} from '../types/conversational-ai-types';
import { extractErrorMessage } from '../utils';

export class ElevenLabsConversationalAIService extends EventEmitter {
  private apiKey: string;
  private baseUrl: string;
  private ws: WebSocket | null = null;
  private isConnected = false;
  private isListening = false;
  private mediaRecorder: MediaRecorder | null = null;
  private audioContext: AudioContext | null = null;
  private audioChunks: Blob[] = [];
  private sessionId: string | null = null;
  private agentId: string | null = null;
  private currentSession: ConversationalAISession | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectDelay = 1000;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  // private activityDetection: VoiceActivityDetection | null = null;
  private sessionMetrics: SessionMetrics = {
    startTime: Date.now(),
    messagesCount: 0,
    totalAudioDuration: 0,
    averageResponseTime: 0,
    responseTimeBuffer: []
  };

  constructor(apiKey: string, baseUrl = 'wss://api.elevenlabs.io/v1/convai') {
    super();
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    this.on('connection:established', () => {
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.startHeartbeat();
    });

    this.on('connection:closed', () => {
      this.isConnected = false;
      this.stopHeartbeat();
      this.attemptReconnection();
    });

    this.on('message:received', (message: ConversationalMessage) => {
      this.updateSessionMetrics(message);
    });
  }

  /**
   * Start a new conversational AI session
   */
  async startSession(config: ConversationalAIConfig): Promise<ConversationalAISession> {
    try {
      // Validate configuration
      this.validateConfig(config);

      // Initialize session
      this.sessionId = this.generateSessionId();
      this.agentId = config.agentId;
      this.currentSession = {
        id: this.sessionId,
        agentId: this.agentId,
        status: 'initializing',
        config,
        startTime: Date.now(),
        messages: [],
        metadata: {}
      };

      // Setup WebSocket connection
      await this.connectWebSocket(config);

      // Initialize audio recording
      if (config.audioConfig?.enableRecording !== false) {
        await this.initializeAudioRecording(config.audioConfig);
      }

      // Setup voice activity detection
      if (config.turnTaking?.enabled !== false) {
        await this.setupVoiceActivityDetection(config.turnTaking);
      }

      this.currentSession.status = 'connected';
      this.emit('session:started', this.currentSession);

      return this.currentSession;
    } catch (error) {
      const errorMessage = extractErrorMessage(error);
      this.emit('session:error', { type: 'session_start_failed', message: errorMessage });
      throw new Error(`Failed to start session: ${errorMessage}`);
    }
  }

  /**
   * Connect to WebSocket for real-time communication
   */
  private async connectWebSocket(config: ConversationalAIConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const wsUrl = this.buildWebSocketUrl(config);
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          this.emit('connection:established');
          
          // Send initial configuration
          this.sendMessage({
            type: 'session_config',
            config: {
              voice_settings: config.voiceSettings,
              turn_taking: config.turnTaking,
              audio_config: config.audioConfig,
              system_prompt: config.systemPrompt,
              context: config.context
            }
          });

          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleWebSocketMessage(event);
        };

        this.ws.onerror = (error) => {
          this.emit('connection:error', error);
          reject(error);
        };

        this.ws.onclose = (event) => {
          this.emit('connection:closed', event);
        };

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleWebSocketMessage(event: MessageEvent) {
    try {
      const data = JSON.parse(event.data);
      
      switch (data.type) {
        case 'session_created':
          this.handleSessionCreated(data);
          break;
        case 'audio_response':
          this.handleAudioResponse(data);
          break;
        case 'text_response':
          this.handleTextResponse(data);
          break;
        case 'turn_detection':
          this.handleTurnDetection(data);
          break;
        case 'session_update':
          this.handleSessionUpdate(data);
          break;
        case 'error':
          this.handleError(data);
          break;
        case 'heartbeat':
          this.handleHeartbeat(data);
          break;
        default:
          this.emit('message:unknown', data);
      }
    } catch (error) {
      this.emit('message:parse_error', error);
    }
  }

  /**
   * Send audio data to the conversational AI
   */
  async sendAudio(audioData: Blob | ArrayBuffer): Promise<void> {
    if (!this.isConnected || !this.ws) {
      throw new Error('WebSocket not connected');
    }

    try {
      const message = {
        type: 'audio_input',
        session_id: this.sessionId,
        audio_data: audioData,
        timestamp: Date.now()
      };

      if (audioData instanceof Blob) {
        // Convert blob to array buffer
        const arrayBuffer = await audioData.arrayBuffer();
        this.ws.send(JSON.stringify({
          ...message,
          audio_data: Array.from(new Uint8Array(arrayBuffer))
        }));
      } else {
        this.ws.send(JSON.stringify({
          ...message,
          audio_data: Array.from(new Uint8Array(audioData))
        }));
      }

      this.emit('audio:sent', {
        size: audioData instanceof ArrayBuffer ? audioData.byteLength : (audioData as Blob).size
      });
    } catch (error) {
      this.emit('audio:send_error', error);
      throw error;
    }
  }

  /**
   * Send text message to the conversational AI
   */
  async sendText(text: string): Promise<void> {
    if (!this.isConnected || !this.ws) {
      throw new Error('WebSocket not connected');
    }

    try {
      const message = {
        type: 'text_input',
        session_id: this.sessionId,
        text,
        timestamp: Date.now()
      };

      this.ws.send(JSON.stringify(message));
      this.emit('text:sent', { text });
    } catch (error) {
      this.emit('text:send_error', error);
      throw error;
    }
  }

  /**
   * Start recording audio from microphone
   */
  async startRecording(): Promise<void> {
    if (this.isListening) {
      return;
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        } 
      });

      this.mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
          this.sendAudio(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        this.processAudioChunks();
      };

      this.mediaRecorder.start(100); // Record in 100ms chunks
      this.isListening = true;
      this.emit('recording:started');
    } catch (error) {
      this.emit('recording:error', error);
      throw error;
    }
  }

  /**
   * Stop recording audio
   */
  stopRecording(): void {
    if (!this.isListening || !this.mediaRecorder) {
      return;
    }

    this.mediaRecorder.stop();
    this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
    this.isListening = false;
    this.emit('recording:stopped');
  }

  /**
   * Interrupt the current AI response
   */
  async interrupt(): Promise<void> {
    if (!this.isConnected || !this.ws) {
      throw new Error('WebSocket not connected');
    }

    try {
      const message = {
        type: 'interrupt',
        session_id: this.sessionId,
        timestamp: Date.now()
      };

      this.ws.send(JSON.stringify(message));
      this.emit('conversation:interrupted');
    } catch (error) {
      this.emit('interrupt:error', error);
      throw error;
    }
  }

  /**
   * Update session configuration
   */
  async updateConfig(updates: Partial<ConversationalAIConfig>): Promise<void> {
    if (!this.isConnected || !this.ws || !this.currentSession) {
      throw new Error('No active session');
    }

    try {
      const message = {
        type: 'config_update',
        session_id: this.sessionId,
        updates,
        timestamp: Date.now()
      };

      this.ws.send(JSON.stringify(message));
      
      // Update local session config
      this.currentSession.config = { ...this.currentSession.config, ...updates };
      this.emit('config:updated', updates);
    } catch (error) {
      this.emit('config:update_error', error);
      throw error;
    }
  }

  /**
   * Get session metrics and statistics
   */
  getSessionMetrics(): SessionMetrics {
    return {
      ...this.sessionMetrics,
      currentDuration: Date.now() - this.sessionMetrics.startTime,
      isActive: this.isConnected
    };
  }

  /**
   * End the current session
   */
  async endSession(): Promise<void> {
    try {
      if (this.isListening) {
        this.stopRecording();
      }

      if (this.ws && this.isConnected) {
        const message = {
          type: 'session_end',
          session_id: this.sessionId,
          timestamp: Date.now()
        };

        this.ws.send(JSON.stringify(message));
        this.ws.close();
      }

      this.cleanup();
      this.emit('session:ended', this.getSessionMetrics());
    } catch (error) {
      this.emit('session:end_error', error);
      throw error;
    }
  }

  /**
   * Get available agents
   */
  async getAgents(): Promise<ConversationalAIAgent[]> {
    try {
      const response = await fetch(`https://api.elevenlabs.io/v1/convai/agents`, {
        headers: {
          'xi-api-key': this.apiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.agents || [];
    } catch (error) {
      throw new Error(`Failed to get agents: ${extractErrorMessage(error)}`);
    }
  }

  /**
   * Create a new agent
   */
  async createAgent(config: Partial<ConversationalAIAgent>): Promise<ConversationalAIAgent> {
    try {
      const response = await fetch(`https://api.elevenlabs.io/v1/convai/agents`, {
        method: 'POST',
        headers: {
          'xi-api-key': this.apiKey,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const agent = await response.json();
      this.emit('agent:created', agent);
      return agent;
    } catch (error) {
      throw new Error(`Failed to create agent: ${extractErrorMessage(error)}`);
    }
  }

  /**
   * Update an existing agent
   */
  async updateAgent(agentId: string, updates: Partial<ConversationalAIAgent>): Promise<ConversationalAIAgent> {
    try {
      const response = await fetch(`https://api.elevenlabs.io/v1/convai/agents/${agentId}`, {
        method: 'PUT',
        headers: {
          'xi-api-key': this.apiKey,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const agent = await response.json();
      this.emit('agent:updated', agent);
      return agent;
    } catch (error) {
      throw new Error(`Failed to update agent: ${extractErrorMessage(error)}`);
    }
  }

  /**
   * Delete an agent
   */
  async deleteAgent(agentId: string): Promise<void> {
    try {
      const response = await fetch(`https://api.elevenlabs.io/v1/convai/agents/${agentId}`, {
        method: 'DELETE',
        headers: {
          'xi-api-key': this.apiKey
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      this.emit('agent:deleted', { agentId });
    } catch (error) {
      throw new Error(`Failed to delete agent: ${extractErrorMessage(error)}`);
    }
  }

  // Private helper methods

  private validateConfig(config: ConversationalAIConfig): void {
    if (!config.agentId) {
      throw new Error('Agent ID is required');
    }

    if (config.turnTaking?.max_silence_ms && config.turnTaking.max_silence_ms < 100) {
      throw new Error('Max silence duration must be at least 100ms');
    }

    if (config.audioConfig?.sampleRate && ![16000, 24000, 44100, 48000].includes(config.audioConfig.sampleRate)) {
      throw new Error('Sample rate must be 16000, 24000, 44100, or 48000 Hz');
    }
  }

  private buildWebSocketUrl(config: ConversationalAIConfig): string {
    const params = new URLSearchParams();
    params.append('agent_id', config.agentId);
    
    if (config.signedUrl) {
      return config.signedUrl;
    }
    
    return `${this.baseUrl}/conversation?${params.toString()}`;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private async initializeAudioRecording(audioConfig?: AudioConfig): Promise<void> {
    if (typeof window === 'undefined' || !navigator.mediaDevices) {
      throw new Error('Audio recording not supported in this environment');
    }

    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: audioConfig?.sampleRate || 44100
      });
    } catch (error) {
      throw new Error(`Failed to initialize audio context: ${extractErrorMessage(error)}`);
    }
  }

  private async setupVoiceActivityDetection(_settings?: TurnTakingSettings): Promise<void> {
    if (!this.audioContext) {
      return;
    }

    // TODO: Implement voice activity detection
    // this.activityDetection = {
    //   enabled: settings?.enabled !== false,
    //   threshold: settings?.sensitivity || 0.01,
    //   silenceTimeoutMs: settings?.max_silence_ms || 1000,
    //   isActive: false,
    //   lastActivity: Date.now()
    // };
  }

  private sendMessage(message: any): void {
    if (this.ws && this.isConnected) {
      this.ws.send(JSON.stringify(message));
    }
  }

  private handleSessionCreated(data: any): void {
    if (this.currentSession) {
      this.currentSession.status = 'active';
      this.currentSession.metadata = { ...this.currentSession.metadata, ...data.metadata };
    }
    this.emit('session:created', data);
  }

  private handleAudioResponse(data: any): void {
    const audioData = new Uint8Array(data.audio_data);
    const audioBlob = new Blob([audioData], { type: 'audio/wav' });
    
    this.emit('audio:received', { 
      audioBlob, 
      duration: data.duration,
      messageId: data.message_id 
    });
  }

  private handleTextResponse(data: any): void {
    const message: ConversationalMessage = {
      id: data.message_id,
      type: 'text',
      content: data.text,
      timestamp: data.timestamp,
      sender: 'assistant',
      metadata: data.metadata
    };

    if (this.currentSession) {
      this.currentSession.messages.push(message);
    }

    this.emit('text:received', message);
  }

  private handleTurnDetection(data: any): void {
    this.emit('turn:detected', {
      type: data.turn_type,
      confidence: data.confidence,
      timestamp: data.timestamp
    });
  }

  private handleSessionUpdate(data: any): void {
    if (this.currentSession) {
      this.currentSession.metadata = { ...this.currentSession.metadata, ...data.updates };
    }
    this.emit('session:updated', data);
  }

  private handleError(data: any): void {
    const error: ConversationalAIError = {
      type: data.error_type,
      message: data.message,
      code: data.code,
      timestamp: data.timestamp
    };
    this.emit('error', error);
  }

  private handleHeartbeat(data: any): void {
    this.emit('heartbeat', data);
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.sendMessage({ type: 'heartbeat', timestamp: Date.now() });
    }, 30000); // 30 second heartbeat
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  private attemptReconnection(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.emit('connection:failed');
      return;
    }

    setTimeout(() => {
      this.reconnectAttempts++;
      this.emit('connection:reconnecting', this.reconnectAttempts);
      
      if (this.currentSession) {
        this.connectWebSocket(this.currentSession.config);
      }
    }, this.reconnectDelay * Math.pow(2, this.reconnectAttempts));
  }

  private processAudioChunks(): void {
    if (this.audioChunks.length === 0) return;

    const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
    this.audioChunks = [];
    
    this.emit('audio:processed', { audioBlob, size: audioBlob.size });
  }

  private updateSessionMetrics(message: ConversationalMessage): void {
    this.sessionMetrics.messagesCount++;
    
    if (message.metadata?.responseTime) {
      this.sessionMetrics.responseTimeBuffer.push(message.metadata.responseTime);
      if (this.sessionMetrics.responseTimeBuffer.length > 10) {
        this.sessionMetrics.responseTimeBuffer.shift();
      }
      
      this.sessionMetrics.averageResponseTime = 
        this.sessionMetrics.responseTimeBuffer.reduce((a, b) => a + b, 0) / 
        this.sessionMetrics.responseTimeBuffer.length;
    }
  }

  private cleanup(): void {
    this.isConnected = false;
    this.isListening = false;
    this.ws = null;
    this.mediaRecorder = null;
    this.audioContext = null;
    this.audioChunks = [];
    this.sessionId = null;
    this.currentSession = null;
    this.stopHeartbeat();
    this.removeAllListeners();
  }
}