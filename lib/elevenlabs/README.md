# ElevenLabs Integration Library

A comprehensive TypeScript library for integrating ElevenLabs AI voice generation into React/Next.js applications with full-featured UI components, state management, and API services.

## Features

### 🎯 Core Features
- **Text-to-Speech Generation** - Convert text to natural-sounding speech
- **Voice Management** - Browse, search, and manage voice libraries
- **Voice Cloning** - Create custom voices from audio samples
- **Voice Design** - Generate voices from text descriptions
- **Generation History** - Track and manage speech generation history
- **User Management** - Handle subscriptions, usage, and billing
- **Real-time Streaming** - Stream audio as it's generated

### 🛠 Technical Features
- **Type-Safe** - Full TypeScript support with comprehensive type definitions
- **State Management** - Zustand stores for efficient state management
- **React Hooks** - Custom hooks for easy integration
- **Error Handling** - Comprehensive error boundaries and handling
- **Caching** - Intelligent caching for voices and user data
- **Modular Architecture** - Clean, maintainable, and extensible code

## Installation

```bash
npm install react-dropzone zustand lucide-react sonner
```

## Quick Start

### 1. Setup API Key

```tsx
import { ApiKeySetup } from '@/components/elevenlabs/api-key-setup';

function App() {
  return <ApiKeySetup />;
}
```

### 2. Basic Text-to-Speech

```tsx
import { TextToSpeech } from '@/components/elevenlabs/text-to-speech';

function App() {
  return (
    <div>
      <TextToSpeech 
        defaultText="Hello, world!"
        onAudioGenerated={(audioUrl) => console.log('Generated:', audioUrl)}
      />
    </div>
  );
}
```

### 3. Voice Selection

```tsx
import { VoiceSelector } from '@/components/elevenlabs/voice-selector';

function App() {
  return (
    <VoiceSelector 
      onVoiceSelect={(voice) => console.log('Selected:', voice)}
      compact={false}
    />
  );
}
```

### 4. Complete Dashboard

```tsx
import { ElevenLabsDashboard } from '@/components/elevenlabs/elevenlabs-dashboard';
import { ElevenLabsProvider } from '@/components/elevenlabs/elevenlabs-provider';

function App() {
  return (
    <ElevenLabsProvider>
      <ElevenLabsDashboard />
    </ElevenLabsProvider>
  );
}
```

## API Documentation

### Services

#### ElevenLabsService
Core service for API interactions:

```typescript
const service = new ElevenLabsService(apiKey);

// Generate speech
const audio = await service.textToSpeech({
  text: "Hello, world!",
  voice_id: "voice-id",
  model_id: "eleven_multilingual_v2"
});

// Get voices
const voices = await service.getVoices();

// Get models
const models = await service.getModels();
```

#### ElevenLabsVoiceService
Extended voice management:

```typescript
const voiceService = new ElevenLabsVoiceService(apiKey);

// Search voices
const voices = await voiceService.searchVoices({
  name: "sarah",
  gender: "female",
  accent: "american"
});

// Clone voice
const clonedVoice = await voiceService.cloneVoiceWithValidation({
  name: "My Voice",
  files: audioFiles,
  description: "Custom voice description"
});

// Design voice
const designedVoice = await voiceService.designVoiceWithValidation({
  name: "AI Voice",
  text: "Sample text",
  voice_description: "Warm, friendly female voice"
});
```

#### ElevenLabsHistoryService
Manage generation history:

```typescript
const historyService = new ElevenLabsHistoryService(apiKey);

// Get history
const history = await historyService.getHistory(1, 100);

// Search history
const results = await historyService.searchHistory({
  text: "hello",
  voice_id: "voice-id"
});

// Get analytics
const analytics = await historyService.getHistoryAnalytics('last_30_days');
```

### Hooks

#### useElevenLabs
Main hook for ElevenLabs functionality:

```typescript
const {
  apiKey,
  setApiKey,
  voices,
  selectedVoice,
  generation,
  generateSpeech,
  loadVoices,
  isConfigured
} = useElevenLabs();
```

#### useTextToSpeech
Specialized hook for speech synthesis:

```typescript
const {
  generation,
  synthesize,
  play,
  pause,
  download,
  clearError
} = useTextToSpeech();
```

#### useVoiceManagement
Voice library management:

```typescript
const {
  library,
  search,
  filter,
  sort,
  toggleFavorite,
  startPreview
} = useVoiceManagement();
```

### Stores

#### Main Store (useElevenLabsStore)
Central state management:

```typescript
const store = useElevenLabsStore();

// Configuration
store.setApiKey(apiKey);
store.loadVoices();
store.loadModels();

// Generation
store.generateSpeech({
  text: "Hello",
  voice_id: "voice-id"
});

// Voice operations
store.selectVoice(voice);
store.cloneVoice(request);
```

#### Voice Store (useVoiceStore)
Specialized voice management:

```typescript
const voiceStore = useVoiceStore();

// Library management
voiceStore.loadVoiceLibrary(apiKey);
voiceStore.searchVoices("query");
voiceStore.filterVoices({ gender: "female" });

// Favorites
voiceStore.addToFavorites(voiceId);
voiceStore.removeFromFavorites(voiceId);

// Comparison
voiceStore.addToComparison(voice);
voiceStore.compareVoices(apiKey);
```

## Components

### Core Components

#### TextToSpeech
Complete text-to-speech interface:

```tsx
<TextToSpeech 
  defaultText="Enter your text here"
  onAudioGenerated={(url) => console.log(url)}
  className="w-full"
/>
```

#### VoiceSelector
Voice browsing and selection:

```tsx
<VoiceSelector 
  onVoiceSelect={(voice) => setSelectedVoice(voice)}
  selectedVoice={selectedVoice}
  compact={false}
/>
```

#### VoiceCloning
Voice cloning interface:

```tsx
<VoiceCloning 
  onVoiceCloned={(voice) => console.log('Cloned:', voice)}
/>
```

#### VoiceDesign
AI voice design from descriptions:

```tsx
<VoiceDesign 
  onVoiceDesigned={(voice) => console.log('Designed:', voice)}
/>
```

### Utility Components

#### QuotaIndicator
Display usage and quota information:

```tsx
<QuotaIndicator compact={true} />
```

#### AudioPlayer
Enhanced audio playback:

```tsx
<AudioPlayer 
  src={audioUrl}
  showDownload={true}
  filename="speech.mp3"
/>
```

#### VoiceSettings
Voice parameter configuration:

```tsx
<VoiceSettings 
  settings={settings}
  onChange={setSettings}
  voice={selectedVoice}
/>
```

## Configuration

### Environment Variables

```env
# Optional: Default API base URL
NEXT_PUBLIC_ELEVENLABS_BASE_URL=https://api.elevenlabs.io/v1
```

### Constants

The library includes comprehensive constants:

```typescript
// Models
ELEVENLABS_MODELS.ELEVEN_MULTILINGUAL_V2
ELEVENLABS_MODELS.ELEVEN_TURBO_V2_5
ELEVENLABS_MODELS.ELEVEN_FLASH_V2_5

// Output formats
ELEVENLABS_OUTPUT_FORMATS.MP3_44100_128
ELEVENLABS_OUTPUT_FORMATS.WAV_44100

// Voice settings defaults
VOICE_SETTINGS_DEFAULTS.STABILITY
VOICE_SETTINGS_DEFAULTS.SIMILARITY_BOOST
```

## Advanced Usage

### Custom Service Implementation

```typescript
class CustomElevenLabsService extends ElevenLabsService {
  async customTextToSpeech(text: string, options: CustomOptions) {
    // Custom implementation
    const response = await this.textToSpeech({
      text,
      voice_id: options.voiceId,
      model_id: options.modelId,
      voice_settings: options.settings
    });
    
    // Custom post-processing
    return this.processAudio(response.audio);
  }
}
```

### Stream Processing

```typescript
const { startStream, stopStream, isStreaming } = useStreaming();

// Start streaming
await startStream(
  { text: "Long text to stream...", voice_id: "voice-id" },
  (chunk) => {
    // Process audio chunk
    audioBuffer.append(chunk);
  },
  { format: "mp3_44100_128" }
);
```

### Batch Operations

```typescript
// Batch voice settings update
const updates = voices.map(voice => ({
  voiceId: voice.voice_id,
  settings: { stability: 0.7, similarity_boost: 0.5 }
}));

const results = await voiceService.batchUpdateVoiceSettings(updates);
```

## Error Handling

The library provides comprehensive error handling:

```typescript
// Global error boundary
<ElevenLabsErrorBoundary>
  <YourComponent />
</ElevenLabsErrorBoundary>

// Hook-level error handling
const { generation, clearError } = useTextToSpeech();

if (generation.error) {
  return <ErrorDisplay error={generation.error} onRetry={clearError} />;
}
```

## Performance Optimization

### Caching

```typescript
// Voice caching (automatic)
const voices = await voiceService.getVoicesWithCache(true);

// Clear cache when needed
voiceService.clearCache();
```

### Lazy Loading

```typescript
// Lazy load components
const VoiceCloning = lazy(() => import('./voice-cloning'));

<Suspense fallback={<LoadingSpinner />}>
  <VoiceCloning />
</Suspense>
```

## Type Definitions

The library includes comprehensive TypeScript definitions:

```typescript
interface ElevenLabsVoice {
  voice_id: string;
  name: string;
  category: 'premade' | 'cloned' | 'generated' | 'professional';
  description?: string;
  labels?: Record<string, string>;
  settings?: VoiceSettings;
}

interface TTSRequest {
  text: string;
  voice_id: string;
  model_id?: string;
  voice_settings?: VoiceSettings;
  pronunciation_dictionary_locators?: PronunciationDictionary[];
  seed?: number;
  previous_text?: string;
  next_text?: string;
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For support and questions:
- Check the [ElevenLabs API Documentation](https://elevenlabs.io/docs)
- Open an issue on GitHub
- Check existing issues and discussions

## Changelog

### v1.0.0
- Initial release
- Complete ElevenLabs API integration
- Full TypeScript support
- React hooks and components
- Zustand state management
- Comprehensive error handling