// ElevenLabs Conversational AI Store
// State management for real-time voice conversations

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ElevenLabsConversationalAIService } from '../services/conversational-ai-service';
import { extractErrorMessage } from '../utils';
import type { 
  ConversationalAIAgent,
  ConversationalAISession,
  ConversationalMessage,
  ConversationalAIConfig,
  ConversationalAIStatus,
  ConversationalAIError,
  SessionMetrics,
  AudioConfig,
  TurnTakingSettings,
  VoiceSettings,
  ConversationalAnalytics,
  AgentPerformanceMetrics
} from '../types/conversational-ai-types';

interface ConversationalAIStoreState {
  // Service instance
  service: ElevenLabsConversationalAIService | null;
  
  // Agents management
  agents: {
    list: ConversationalAIAgent[];
    selectedAgent: ConversationalAIAgent | null;
    isLoading: boolean;
    error: string | null;
    lastUpdated: number;
  };
  
  // Active session
  session: {
    current: ConversationalAISession | null;
    status: ConversationalAIStatus;
    isConnected: boolean;
    isListening: boolean;
    isSpeaking: boolean;
    isProcessing: boolean;
    reconnectAttempts: number;
    lastError: ConversationalAIError | null;
    metrics: SessionMetrics | null;
  };
  
  // Messages
  messages: {
    list: ConversationalMessage[];
    unreadCount: number;
    isTyping: boolean;
    lastMessageId: string | null;
  };
  
  // Audio
  audio: {
    isRecording: boolean;
    isPlaying: boolean;
    currentAudioUrl: string | null;
    audioDevices: MediaDeviceInfo[];
    selectedInputDevice: string | null;
    selectedOutputDevice: string | null;
    audioLevel: number;
    isAudioEnabled: boolean;
    isMuted: boolean;
    volume: number;
  };
  
  // Configuration
  config: {
    defaultVoiceSettings: VoiceSettings;
    defaultTurnTakingSettings: TurnTakingSettings;
    defaultAudioConfig: AudioConfig;
    enableAnalytics: boolean;
    enableLogging: boolean;
    maxSessionDuration: number;
  };
  
  // Analytics
  analytics: {
    data: ConversationalAnalytics | null;
    agentMetrics: Record<string, AgentPerformanceMetrics>;
    isLoading: boolean;
    error: string | null;
    lastUpdated: number;
  };
  
  // UI state
  ui: {
    isAgentSelectorOpen: boolean;
    isSettingsOpen: boolean;
    isAnalyticsOpen: boolean;
    activeTab: 'chat' | 'settings' | 'analytics' | 'agents';
    chatPanelCollapsed: boolean;
    settingsPanelCollapsed: boolean;
  };
}

interface ConversationalAIStoreActions {
  // Service management
  initializeService: (apiKey: string) => void;
  destroyService: () => void;
  
  // Agent management
  loadAgents: () => Promise<void>;
  selectAgent: (agent: ConversationalAIAgent) => void;
  createAgent: (config: Partial<ConversationalAIAgent>) => Promise<ConversationalAIAgent>;
  updateAgent: (agentId: string, updates: Partial<ConversationalAIAgent>) => Promise<void>;
  deleteAgent: (agentId: string) => Promise<void>;
  
  // Session management
  startSession: (config?: Partial<ConversationalAIConfig>) => Promise<void>;
  endSession: () => Promise<void>;
  pauseSession: () => Promise<void>;
  resumeSession: () => Promise<void>;
  updateSessionConfig: (updates: Partial<ConversationalAIConfig>) => Promise<void>;
  
  // Message management
  sendTextMessage: (text: string) => Promise<void>;
  clearMessages: () => void;
  markMessagesAsRead: () => void;
  
  // Audio management
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<void>;
  playAudio: (audioUrl: string) => Promise<void>;
  stopAudio: () => void;
  setVolume: (volume: number) => void;
  toggleMute: () => void;
  loadAudioDevices: () => Promise<void>;
  selectInputDevice: (deviceId: string) => void;
  selectOutputDevice: (deviceId: string) => void;
  
  // Conversation controls
  interrupt: () => Promise<void>;
  
  // Analytics
  loadAnalytics: (timeRange?: string) => Promise<void>;
  loadAgentMetrics: (agentId: string) => Promise<void>;
  exportAnalytics: (format: 'json' | 'csv') => Promise<string>;
  
  // Configuration
  updateConfig: (updates: Partial<ConversationalAIStoreState['config']>) => void;
  resetConfig: () => void;
  
  // UI actions
  setActiveTab: (tab: ConversationalAIStoreState['ui']['activeTab']) => void;
  toggleAgentSelector: () => void;
  toggleSettings: () => void;
  toggleAnalytics: () => void;
  toggleChatPanel: () => void;
  toggleSettingsPanel: () => void;
  
  // Error handling
  clearError: (section?: string) => void;
  
  // Utility
  reset: () => void;
}

type ConversationalAIStore = ConversationalAIStoreState & ConversationalAIStoreActions;

const defaultVoiceSettings: VoiceSettings = {
  stability: 0.5,
  similarity_boost: 0.5,
  style: 0.0,
  use_speaker_boost: true,
  optimize_streaming_latency: 3,
  output_format: 'pcm_44100'
};

const defaultTurnTakingSettings: TurnTakingSettings = {
  enabled: true,
  model_type: 'advanced',
  sensitivity: 0.7,
  max_silence_ms: 1000,
  min_speech_duration_ms: 300,
  interruption_threshold: 0.5,
  turn_detection_method: 'hybrid'
};

const defaultAudioConfig: AudioConfig = {
  enableRecording: true,
  sampleRate: 44100,
  channels: 1,
  bitDepth: 16,
  enableNoiseSuppression: true,
  enableEchoCancellation: true,
  enableAutoGainControl: true
};

export const useConversationalAIStore = create<ConversationalAIStore>()(
  persist(
    (set, get) => ({
      // Initial state
      service: null,
      
      agents: {
        list: [],
        selectedAgent: null,
        isLoading: false,
        error: null,
        lastUpdated: 0
      },
      
      session: {
        current: null,
        status: 'disconnected',
        isConnected: false,
        isListening: false,
        isSpeaking: false,
        isProcessing: false,
        reconnectAttempts: 0,
        lastError: null,
        metrics: null
      },
      
      messages: {
        list: [],
        unreadCount: 0,
        isTyping: false,
        lastMessageId: null
      },
      
      audio: {
        isRecording: false,
        isPlaying: false,
        currentAudioUrl: null,
        audioDevices: [],
        selectedInputDevice: null,
        selectedOutputDevice: null,
        audioLevel: 0,
        isAudioEnabled: true,
        isMuted: false,
        volume: 1.0
      },
      
      config: {
        defaultVoiceSettings,
        defaultTurnTakingSettings,
        defaultAudioConfig,
        enableAnalytics: true,
        enableLogging: true,
        maxSessionDuration: 30 * 60 * 1000 // 30 minutes
      },
      
      analytics: {
        data: null,
        agentMetrics: {},
        isLoading: false,
        error: null,
        lastUpdated: 0
      },
      
      ui: {
        isAgentSelectorOpen: false,
        isSettingsOpen: false,
        isAnalyticsOpen: false,
        activeTab: 'chat',
        chatPanelCollapsed: false,
        settingsPanelCollapsed: false
      },
      
      // Actions
      initializeService: (apiKey: string) => {
        const service = new ElevenLabsConversationalAIService(apiKey);
        
        // Setup event listeners
        service.on('session:started', (session) => {
          set(state => ({
            session: {
              ...state.session,
              current: session,
              status: 'connected',
              isConnected: true
            }
          }));
        });
        
        service.on('session:ended', (metrics) => {
          set(state => ({
            session: {
              ...state.session,
              current: null,
              status: 'disconnected',
              isConnected: false,
              metrics
            }
          }));
        });
        
        service.on('text:received', (message) => {
          set(state => ({
            messages: {
              ...state.messages,
              list: [...state.messages.list, message],
              unreadCount: state.messages.unreadCount + 1,
              lastMessageId: message.id
            }
          }));
        });
        
        service.on('audio:received', ({ audioBlob }) => {
          const audioUrl = URL.createObjectURL(audioBlob);
          set(state => ({
            audio: {
              ...state.audio,
              currentAudioUrl: audioUrl
            }
          }));
        });
        
        service.on('recording:started', () => {
          set(state => ({
            audio: {
              ...state.audio,
              isRecording: true
            },
            session: {
              ...state.session,
              isListening: true
            }
          }));
        });
        
        service.on('recording:stopped', () => {
          set(state => ({
            audio: {
              ...state.audio,
              isRecording: false
            },
            session: {
              ...state.session,
              isListening: false
            }
          }));
        });
        
        service.on('error', (error) => {
          set(state => ({
            session: {
              ...state.session,
              lastError: error
            }
          }));
        });
        
        service.on('connection:reconnecting', (attempts) => {
          set(state => ({
            session: {
              ...state.session,
              reconnectAttempts: attempts
            }
          }));
        });
        
        set({ service });
      },
      
      destroyService: () => {
        const { service } = get();
        if (service) {
          service.removeAllListeners();
          service.endSession();
        }
        set({ service: null });
      },
      
      loadAgents: async () => {
        const { service } = get();
        if (!service) return;
        
        set(state => ({
          agents: {
            ...state.agents,
            isLoading: true,
            error: null
          }
        }));
        
        try {
          const agents = await service.getAgents();
          set(state => ({
            agents: {
              ...state.agents,
              list: agents,
              isLoading: false,
              lastUpdated: Date.now()
            }
          }));
        } catch (error) {
          set(state => ({
            agents: {
              ...state.agents,
              isLoading: false,
              error: extractErrorMessage(error)
            }
          }));
        }
      },
      
      selectAgent: (agent: ConversationalAIAgent) => {
        set(state => ({
          agents: {
            ...state.agents,
            selectedAgent: agent
          }
        }));
      },
      
      createAgent: async (config: Partial<ConversationalAIAgent>) => {
        const { service } = get();
        if (!service) throw new Error('Service not initialized');
        
        try {
          const agent = await service.createAgent(config);
          set(state => ({
            agents: {
              ...state.agents,
              list: [...state.agents.list, agent]
            }
          }));
          return agent;
        } catch (error) {
          set(state => ({
            agents: {
              ...state.agents,
              error: extractErrorMessage(error)
            }
          }));
          throw error;
        }
      },
      
      updateAgent: async (agentId: string, updates: Partial<ConversationalAIAgent>) => {
        const { service } = get();
        if (!service) throw new Error('Service not initialized');
        
        try {
          const updatedAgent = await service.updateAgent(agentId, updates);
          set(state => ({
            agents: {
              ...state.agents,
              list: state.agents.list.map(agent => 
                agent.agent_id === agentId ? updatedAgent : agent
              )
            }
          }));
        } catch (error) {
          set(state => ({
            agents: {
              ...state.agents,
              error: extractErrorMessage(error)
            }
          }));
          throw error;
        }
      },
      
      deleteAgent: async (agentId: string) => {
        const { service } = get();
        if (!service) throw new Error('Service not initialized');
        
        try {
          await service.deleteAgent(agentId);
          set(state => ({
            agents: {
              ...state.agents,
              list: state.agents.list.filter(agent => agent.agent_id !== agentId)
            }
          }));
        } catch (error) {
          set(state => ({
            agents: {
              ...state.agents,
              error: extractErrorMessage(error)
            }
          }));
          throw error;
        }
      },
      
      startSession: async (config?: Partial<ConversationalAIConfig>) => {
        const { service, agents, config: defaultConfig } = get();
        if (!service) throw new Error('Service not initialized');
        
        const selectedAgent = agents.selectedAgent;
        if (!selectedAgent) throw new Error('No agent selected');
        
        const sessionConfig: ConversationalAIConfig = {
          agentId: selectedAgent.agent_id,
          voiceSettings: defaultConfig.defaultVoiceSettings,
          turnTaking: defaultConfig.defaultTurnTakingSettings,
          audioConfig: defaultConfig.defaultAudioConfig,
          ...config
        };
        
        try {
          const session = await service.startSession(sessionConfig);
          set(state => ({
            session: {
              ...state.session,
              current: session,
              status: 'connected',
              isConnected: true
            }
          }));
        } catch (error) {
          set(state => ({
            session: {
              ...state.session,
              lastError: {
                type: 'session',
                message: extractErrorMessage(error),
                timestamp: Date.now()
              }
            }
          }));
          throw error;
        }
      },
      
      endSession: async () => {
        const { service } = get();
        if (!service) return;
        
        try {
          await service.endSession();
          set(state => ({
            session: {
              ...state.session,
              current: null,
              status: 'disconnected',
              isConnected: false
            },
            messages: {
              ...state.messages,
              list: [],
              unreadCount: 0
            }
          }));
        } catch (error) {
          console.error('Error ending session:', error);
        }
      },
      
      pauseSession: async () => {
        const { service } = get();
        if (!service) return;
        
        try {
          service.stopRecording();
          set(state => ({
            session: {
              ...state.session,
              status: 'waiting'
            }
          }));
        } catch (error) {
          console.error('Error pausing session:', error);
        }
      },
      
      resumeSession: async () => {
        const { service } = get();
        if (!service) return;
        
        try {
          await service.startRecording();
          set(state => ({
            session: {
              ...state.session,
              status: 'active'
            }
          }));
        } catch (error) {
          console.error('Error resuming session:', error);
        }
      },
      
      updateSessionConfig: async (updates: Partial<ConversationalAIConfig>) => {
        const { service } = get();
        if (!service) return;
        
        try {
          await service.updateConfig(updates);
        } catch (error) {
          console.error('Error updating session config:', error);
        }
      },
      
      sendTextMessage: async (text: string) => {
        const { service } = get();
        if (!service) return;
        
        try {
          await service.sendText(text);
          
          // Add user message to local state
          const userMessage: ConversationalMessage = {
            id: `user_${Date.now()}`,
            type: 'text',
            content: text,
            timestamp: Date.now(),
            sender: 'user'
          };
          
          set(state => ({
            messages: {
              ...state.messages,
              list: [...state.messages.list, userMessage]
            }
          }));
        } catch (error) {
          console.error('Error sending text message:', error);
        }
      },
      
      clearMessages: () => {
        set(state => ({
          messages: {
            ...state.messages,
            list: [],
            unreadCount: 0
          }
        }));
      },
      
      markMessagesAsRead: () => {
        set(state => ({
          messages: {
            ...state.messages,
            unreadCount: 0
          }
        }));
      },
      
      startRecording: async () => {
        const { service } = get();
        if (!service) return;
        
        try {
          await service.startRecording();
        } catch (error) {
          console.error('Error starting recording:', error);
        }
      },
      
      stopRecording: async () => {
        const { service } = get();
        if (!service) return;
        
        try {
          service.stopRecording();
        } catch (error) {
          console.error('Error stopping recording:', error);
        }
      },
      
      playAudio: async (audioUrl: string) => {
        try {
          const audio = new Audio(audioUrl);
          audio.volume = get().audio.volume;
          
          set(state => ({
            audio: {
              ...state.audio,
              isPlaying: true
            }
          }));
          
          audio.onended = () => {
            set(state => ({
              audio: {
                ...state.audio,
                isPlaying: false
              }
            }));
          };
          
          await audio.play();
        } catch (error) {
          console.error('Error playing audio:', error);
        }
      },
      
      stopAudio: () => {
        set(state => ({
          audio: {
            ...state.audio,
            isPlaying: false
          }
        }));
      },
      
      setVolume: (volume: number) => {
        set(state => ({
          audio: {
            ...state.audio,
            volume: Math.max(0, Math.min(1, volume))
          }
        }));
      },
      
      toggleMute: () => {
        set(state => ({
          audio: {
            ...state.audio,
            isMuted: !state.audio.isMuted
          }
        }));
      },
      
      loadAudioDevices: async () => {
        try {
          const devices = await navigator.mediaDevices.enumerateDevices();
          const audioDevices = devices.filter(device => 
            device.kind === 'audioinput' || device.kind === 'audiooutput'
          );
          
          set(state => ({
            audio: {
              ...state.audio,
              audioDevices
            }
          }));
        } catch (error) {
          console.error('Error loading audio devices:', error);
        }
      },
      
      selectInputDevice: (deviceId: string) => {
        set(state => ({
          audio: {
            ...state.audio,
            selectedInputDevice: deviceId
          }
        }));
      },
      
      selectOutputDevice: (deviceId: string) => {
        set(state => ({
          audio: {
            ...state.audio,
            selectedOutputDevice: deviceId
          }
        }));
      },
      
      interrupt: async () => {
        const { service } = get();
        if (!service) return;
        
        try {
          await service.interrupt();
        } catch (error) {
          console.error('Error interrupting:', error);
        }
      },
      
      loadAnalytics: async (_timeRange = 'last_30_days') => {
        set(state => ({
          analytics: {
            ...state.analytics,
            isLoading: true,
            error: null
          }
        }));
        
        try {
          // This would be implemented with actual analytics API
          // For now, we'll use placeholder data
          const data: ConversationalAnalytics = {
            totalSessions: 0,
            totalDuration: 0,
            averageSessionDuration: 0,
            totalMessages: 0,
            averageMessagesPerSession: 0,
            mostUsedAgents: [],
            performanceMetrics: {
              averageResponseTime: 0,
              averageLatency: 0,
              errorRate: 0,
              interruptionRate: 0
            },
            userSatisfactionMetrics: {
              averageRating: 0,
              npsScore: 0,
              completionRate: 0
            },
            timeRange: {
              start: '',
              end: ''
            }
          };
          
          set(state => ({
            analytics: {
              ...state.analytics,
              data,
              isLoading: false,
              lastUpdated: Date.now()
            }
          }));
        } catch (error) {
          set(state => ({
            analytics: {
              ...state.analytics,
              isLoading: false,
              error: extractErrorMessage(error)
            }
          }));
        }
      },
      
      loadAgentMetrics: async (agentId: string) => {
        // Implementation would fetch agent-specific metrics
        console.log('Loading metrics for agent:', agentId);
      },
      
      exportAnalytics: async (format: 'json' | 'csv') => {
        const { analytics } = get();
        if (!analytics.data) return '';
        
        if (format === 'json') {
          return JSON.stringify(analytics.data, null, 2);
        } else {
          // Convert to CSV format
          return 'CSV export not implemented yet';
        }
      },
      
      updateConfig: (updates: Partial<ConversationalAIStoreState['config']>) => {
        set(state => ({
          config: {
            ...state.config,
            ...updates
          }
        }));
      },
      
      resetConfig: () => {
        set(() => ({
          config: {
            defaultVoiceSettings,
            defaultTurnTakingSettings,
            defaultAudioConfig,
            enableAnalytics: true,
            enableLogging: true,
            maxSessionDuration: 30 * 60 * 1000
          }
        }));
      },
      
      setActiveTab: (tab: ConversationalAIStoreState['ui']['activeTab']) => {
        set(state => ({
          ui: {
            ...state.ui,
            activeTab: tab
          }
        }));
      },
      
      toggleAgentSelector: () => {
        set(state => ({
          ui: {
            ...state.ui,
            isAgentSelectorOpen: !state.ui.isAgentSelectorOpen
          }
        }));
      },
      
      toggleSettings: () => {
        set(state => ({
          ui: {
            ...state.ui,
            isSettingsOpen: !state.ui.isSettingsOpen
          }
        }));
      },
      
      toggleAnalytics: () => {
        set(state => ({
          ui: {
            ...state.ui,
            isAnalyticsOpen: !state.ui.isAnalyticsOpen
          }
        }));
      },
      
      toggleChatPanel: () => {
        set(state => ({
          ui: {
            ...state.ui,
            chatPanelCollapsed: !state.ui.chatPanelCollapsed
          }
        }));
      },
      
      toggleSettingsPanel: () => {
        set(state => ({
          ui: {
            ...state.ui,
            settingsPanelCollapsed: !state.ui.settingsPanelCollapsed
          }
        }));
      },
      
      clearError: (section?: string) => {
        if (section) {
          set(state => ({
            [section]: {
              ...state[section as keyof ConversationalAIStoreState],
              error: null
            }
          }));
        } else {
          set(state => ({
            agents: { ...state.agents, error: null },
            session: { ...state.session, lastError: null },
            analytics: { ...state.analytics, error: null }
          }));
        }
      },
      
      reset: () => {
        const { service } = get();
        if (service) {
          service.endSession();
        }
        
        set({
          service: null,
          agents: {
            list: [],
            selectedAgent: null,
            isLoading: false,
            error: null,
            lastUpdated: 0
          },
          session: {
            current: null,
            status: 'disconnected',
            isConnected: false,
            isListening: false,
            isSpeaking: false,
            isProcessing: false,
            reconnectAttempts: 0,
            lastError: null,
            metrics: null
          },
          messages: {
            list: [],
            unreadCount: 0,
            isTyping: false,
            lastMessageId: null
          },
          audio: {
            isRecording: false,
            isPlaying: false,
            currentAudioUrl: null,
            audioDevices: [],
            selectedInputDevice: null,
            selectedOutputDevice: null,
            audioLevel: 0,
            isAudioEnabled: true,
            isMuted: false,
            volume: 1.0
          },
          analytics: {
            data: null,
            agentMetrics: {},
            isLoading: false,
            error: null,
            lastUpdated: 0
          },
          ui: {
            isAgentSelectorOpen: false,
            isSettingsOpen: false,
            isAnalyticsOpen: false,
            activeTab: 'chat',
            chatPanelCollapsed: false,
            settingsPanelCollapsed: false
          }
        });
      }
    }),
    {
      name: 'conversational-ai-store',
      partialize: (state) => ({
        config: state.config,
        ui: {
          activeTab: state.ui.activeTab,
          chatPanelCollapsed: state.ui.chatPanelCollapsed,
          settingsPanelCollapsed: state.ui.settingsPanelCollapsed
        },
        audio: {
          selectedInputDevice: state.audio.selectedInputDevice,
          selectedOutputDevice: state.audio.selectedOutputDevice,
          volume: state.audio.volume,
          isMuted: state.audio.isMuted
        }
      })
    }
  )
);