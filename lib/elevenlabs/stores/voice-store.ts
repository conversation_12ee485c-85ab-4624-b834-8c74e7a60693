// ElevenLabs Voice Store
// Specialized store for voice management

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ElevenLabsVoiceService } from '../services/voice-service';
import { extractErrorMessage } from '../utils';
import type { ElevenLabsVoice } from '../types';

interface VoiceStoreState {
  // Voice Library
  library: {
    voices: ElevenLabsVoice[];
    filteredVoices: ElevenLabsVoice[];
    isLoading: boolean;
    error: string | null;
    filters: {
      search: string;
      category: string;
      gender: string;
      age: string;
      accent: string;
      language: string;
    };
    sort: {
      field: 'name' | 'category' | 'date_created';
      direction: 'asc' | 'desc';
    };
  };
  
  // Voice Comparison
  comparison: {
    selectedVoices: ElevenLabsVoice[];
    compareData: any;
    isLoading: boolean;
    error: string | null;
  };
  
  // Voice Favorites
  favorites: {
    voiceIds: string[];
    voices: ElevenLabsVoice[];
  };
  
  // Voice Settings
  settings: {
    defaultSettings: {
      stability: number;
      similarity_boost: number;
      style: number;
      use_speaker_boost: boolean;
    };
    voiceSettings: Record<string, any>;
  };
  
  // Voice Preview
  preview: {
    currentVoice: ElevenLabsVoice | null;
    isPlaying: boolean;
    audioUrl: string | null;
    error: string | null;
  };
}

interface VoiceStoreActions {
  // Library actions
  loadVoiceLibrary: (apiKey: string) => Promise<void>;
  searchVoices: (query: string) => void;
  filterVoices: (filters: Partial<VoiceStoreState['library']['filters']>) => void;
  sortVoices: (field: VoiceStoreState['library']['sort']['field'], direction?: VoiceStoreState['library']['sort']['direction']) => void;
  clearFilters: () => void;
  
  // Comparison actions
  addToComparison: (voice: ElevenLabsVoice) => void;
  removeFromComparison: (voiceId: string) => void;
  compareVoices: (apiKey: string) => Promise<void>;
  clearComparison: () => void;
  
  // Favorites actions
  addToFavorites: (voiceId: string) => void;
  removeFromFavorites: (voiceId: string) => void;
  loadFavorites: (apiKey: string) => Promise<void>;
  
  // Settings actions
  updateDefaultSettings: (settings: Partial<VoiceStoreState['settings']['defaultSettings']>) => void;
  updateVoiceSettings: (voiceId: string, settings: any) => void;
  resetVoiceSettings: (voiceId: string) => void;
  
  // Preview actions
  previewVoice: (apiKey: string, voice: ElevenLabsVoice, text?: string) => Promise<void>;
  stopPreview: () => void;
  
  // Utility actions
  clearError: (section: string) => void;
  reset: () => void;
}

type VoiceStore = VoiceStoreState & VoiceStoreActions;

export const useVoiceStore = create<VoiceStore>()(
  persist(
    (set, get) => ({
      // Initial state
      library: {
        voices: [],
        filteredVoices: [],
        isLoading: false,
        error: null,
        filters: {
          search: '',
          category: '',
          gender: '',
          age: '',
          accent: '',
          language: '',
        },
        sort: {
          field: 'name',
          direction: 'asc',
        },
      },
      
      comparison: {
        selectedVoices: [],
        compareData: null,
        isLoading: false,
        error: null,
      },
      
      favorites: {
        voiceIds: [],
        voices: [],
      },
      
      settings: {
        defaultSettings: {
          stability: 0.5,
          similarity_boost: 0.5,
          style: 0.0,
          use_speaker_boost: true,
        },
        voiceSettings: {},
      },
      
      preview: {
        currentVoice: null,
        isPlaying: false,
        audioUrl: null,
        error: null,
      },
      
      // Actions
      loadVoiceLibrary: async (apiKey: string) => {
        set(state => ({
          library: {
            ...state.library,
            isLoading: true,
            error: null,
          },
        }));
        
        try {
          const service = new ElevenLabsVoiceService(apiKey);
          const voices = await service.getVoicesWithCache();
          
          set(state => ({
            library: {
              ...state.library,
              voices,
              filteredVoices: voices,
              isLoading: false,
            },
          }));
          
          // Apply current filters
          get().filterVoices({});
        } catch (error) {
          set(state => ({
            library: {
              ...state.library,
              isLoading: false,
              error: extractErrorMessage(error),
            },
          }));
        }
      },
      
      searchVoices: (query: string) => {
        set(state => ({
          library: {
            ...state.library,
            filters: {
              ...state.library.filters,
              search: query,
            },
          },
        }));
        
        get().filterVoices({});
      },
      
      filterVoices: (newFilters: Partial<VoiceStoreState['library']['filters']>) => {
        set(state => ({
          library: {
            ...state.library,
            filters: {
              ...state.library.filters,
              ...newFilters,
            },
          },
        }));
        
        const { library } = get();
        const { voices, filters } = library;
        
        let filtered = voices.filter(voice => {
          if (filters.search && !voice.name.toLowerCase().includes(filters.search.toLowerCase())) {
            return false;
          }
          
          if (filters.category && voice.category !== filters.category) {
            return false;
          }
          
          if (filters.gender && voice.labels?.gender !== filters.gender) {
            return false;
          }
          
          if (filters.age && voice.labels?.age !== filters.age) {
            return false;
          }
          
          if (filters.accent && voice.labels?.accent !== filters.accent) {
            return false;
          }
          
          if (filters.language && voice.labels?.language !== filters.language) {
            return false;
          }
          
          return true;
        });
        
        // Apply sorting
        const { sort } = library;
        filtered.sort((a, b) => {
          let aValue: any = a[sort.field as keyof ElevenLabsVoice];
          let bValue: any = b[sort.field as keyof ElevenLabsVoice];
          
          if (typeof aValue === 'string') {
            aValue = aValue.toLowerCase();
          }
          if (typeof bValue === 'string') {
            bValue = bValue.toLowerCase();
          }
          
          if (sort.direction === 'asc') {
            return aValue > bValue ? 1 : -1;
          } else {
            return aValue < bValue ? 1 : -1;
          }
        });
        
        set(state => ({
          library: {
            ...state.library,
            filteredVoices: filtered,
          },
        }));
      },
      
      sortVoices: (field, direction) => {
        set(state => ({
          library: {
            ...state.library,
            sort: {
              field,
              direction: direction || (state.library.sort.field === field && state.library.sort.direction === 'asc' ? 'desc' : 'asc'),
            },
          },
        }));
        
        get().filterVoices({});
      },
      
      clearFilters: () => {
        set(state => ({
          library: {
            ...state.library,
            filters: {
              search: '',
              category: '',
              gender: '',
              age: '',
              accent: '',
              language: '',
            },
          },
        }));
        
        get().filterVoices({});
      },
      
      addToComparison: (voice: ElevenLabsVoice) => {
        set(state => ({
          comparison: {
            ...state.comparison,
            selectedVoices: [...state.comparison.selectedVoices, voice],
          },
        }));
      },
      
      removeFromComparison: (voiceId: string) => {
        set(state => ({
          comparison: {
            ...state.comparison,
            selectedVoices: state.comparison.selectedVoices.filter(v => v.voice_id !== voiceId),
          },
        }));
      },
      
      compareVoices: async (apiKey: string) => {
        const { comparison } = get();
        if (comparison.selectedVoices.length < 2) return;
        
        set(state => ({
          comparison: {
            ...state.comparison,
            isLoading: true,
            error: null,
          },
        }));
        
        try {
          const service = new ElevenLabsVoiceService(apiKey);
          const voiceIds = comparison.selectedVoices.map(v => v.voice_id);
          const compareData = await service.compareVoices(voiceIds);
          
          set(state => ({
            comparison: {
              ...state.comparison,
              compareData,
              isLoading: false,
            },
          }));
        } catch (error) {
          set(state => ({
            comparison: {
              ...state.comparison,
              isLoading: false,
              error: extractErrorMessage(error),
            },
          }));
        }
      },
      
      clearComparison: () => {
        set(state => ({
          comparison: {
            ...state.comparison,
            selectedVoices: [],
            compareData: null,
          },
        }));
      },
      
      addToFavorites: (voiceId: string) => {
        set(state => ({
          favorites: {
            ...state.favorites,
            voiceIds: [...state.favorites.voiceIds, voiceId],
          },
        }));
      },
      
      removeFromFavorites: (voiceId: string) => {
        set(state => ({
          favorites: {
            ...state.favorites,
            voiceIds: state.favorites.voiceIds.filter(id => id !== voiceId),
          },
        }));
      },
      
      loadFavorites: async (apiKey: string) => {
        const { favorites } = get();
        if (favorites.voiceIds.length === 0) return;
        
        try {
          const service = new ElevenLabsVoiceService(apiKey);
          const voices = await Promise.all(
            favorites.voiceIds.map(id => service.getVoice(id))
          );
          
          set(state => ({
            favorites: {
              ...state.favorites,
              voices,
            },
          }));
        } catch (error) {
          // Handle error silently for favorites
        }
      },
      
      updateDefaultSettings: (settings) => {
        set(state => ({
          settings: {
            ...state.settings,
            defaultSettings: {
              ...state.settings.defaultSettings,
              ...settings,
            },
          },
        }));
      },
      
      updateVoiceSettings: (voiceId: string, settings: any) => {
        set(state => ({
          settings: {
            ...state.settings,
            voiceSettings: {
              ...state.settings.voiceSettings,
              [voiceId]: settings,
            },
          },
        }));
      },
      
      resetVoiceSettings: (voiceId: string) => {
        set(state => {
          const { [voiceId]: removed, ...rest } = state.settings.voiceSettings;
          return {
            settings: {
              ...state.settings,
              voiceSettings: rest,
            },
          };
        });
      },
      
      previewVoice: async (apiKey: string, voice: ElevenLabsVoice, text = 'Hello, this is a voice preview.') => {
        set(state => ({
          preview: {
            ...state.preview,
            currentVoice: voice,
            isPlaying: true,
            error: null,
          },
        }));
        
        try {
          const service = new ElevenLabsVoiceService(apiKey);
          const response = await service.textToSpeech({
            text,
            voice_id: voice.voice_id,
            voice_settings: voice.settings,
          });
          
          const audioUrl = URL.createObjectURL(new Blob([response.audio], { type: 'audio/mpeg' }));
          
          set(state => ({
            preview: {
              ...state.preview,
              audioUrl,
              isPlaying: false,
            },
          }));
          
          // Play the audio
          const audio = new Audio(audioUrl);
          audio.play();
          
          audio.addEventListener('ended', () => {
            set(state => ({
              preview: {
                ...state.preview,
                isPlaying: false,
              },
            }));
          });
        } catch (error) {
          set(state => ({
            preview: {
              ...state.preview,
              isPlaying: false,
              error: extractErrorMessage(error),
            },
          }));
        }
      },
      
      stopPreview: () => {
        set(state => ({
          preview: {
            ...state.preview,
            isPlaying: false,
          },
        }));
      },
      
      clearError: (section: string) => {
        set(state => ({
          [section]: {
            ...state[section as keyof VoiceStoreState],
            error: null,
          },
        }));
      },
      
      reset: () => {
        set({
          library: {
            voices: [],
            filteredVoices: [],
            isLoading: false,
            error: null,
            filters: {
              search: '',
              category: '',
              gender: '',
              age: '',
              accent: '',
              language: '',
            },
            sort: {
              field: 'name',
              direction: 'asc',
            },
          },
          comparison: {
            selectedVoices: [],
            compareData: null,
            isLoading: false,
            error: null,
          },
          favorites: {
            voiceIds: [],
            voices: [],
          },
          settings: {
            defaultSettings: {
              stability: 0.5,
              similarity_boost: 0.5,
              style: 0.0,
              use_speaker_boost: true,
            },
            voiceSettings: {},
          },
          preview: {
            currentVoice: null,
            isPlaying: false,
            audioUrl: null,
            error: null,
          },
        });
      },
    }),
    {
      name: 'voice-store',
      partialize: (state) => ({
        favorites: state.favorites,
        settings: state.settings,
      }),
    }
  )
);