// ElevenLabs Main Store
// Primary Zustand store for ElevenLabs functionality

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ElevenLabsService } from '../services/elevenlabs-service';
import { ElevenLabsStreamingService } from '../services/streaming-service';
import { ElevenLabsVoiceService } from '../services/voice-service';
import { ElevenLabsHistoryService } from '../services/history-service';
import { ElevenLabsUserService } from '../services/user-service';
import { ELEVENLABS_MODELS, VOICE_SETTINGS_DEFAULTS } from '../constants';
import { createAudioBlobUrl, extractErrorMessage } from '../utils';
import type { 
  ElevenLabsStore, 
  ElevenLabsVoice, 
  ElevenLabsModel, 
  TTSRequest, 
  VoiceCloneRequest, 
  VoiceDesignRequest,
  StreamingOptions
} from '../types';

export const useElevenLabsStore = create<ElevenLabsStore>()(
  persist(
    (set, get) => ({
      // Configuration
      apiKey: null,
      baseUrl: 'https://api.elevenlabs.io/v1',
      
      // Voices
      voices: [],
      selectedVoice: null,
      isLoadingVoices: false,
      
      // Models
      models: [],
      selectedModel: null,
      isLoadingModels: false,
      
      // Generation
      generation: {
        isGenerating: false,
        progress: 0,
        currentText: '',
        error: null,
        generatedAudio: null,
        audioUrl: null,
        alignment: null,
      },
      
      // Voice Cloning
      voiceClone: {
        isCloning: false,
        progress: 0,
        clonedVoices: [],
        error: null,
      },
      
      // Voice Design
      voiceDesign: {
        isDesigning: false,
        progress: 0,
        designedVoices: [],
        error: null,
      },
      
      // History
      history: {
        items: [],
        isLoading: false,
        error: null,
        currentPage: 1,
        totalPages: 1,
      },
      
      // User
      user: {
        subscription: null,
        isLoading: false,
        error: null,
      },
      
      // Actions
      setApiKey: (key: string) => {
        set({ apiKey: key });
      },
      
      loadVoices: async () => {
        const { apiKey } = get();
        if (!apiKey) return;
        
        set({ isLoadingVoices: true });
        
        try {
          const service = new ElevenLabsVoiceService(apiKey);
          const voices = await service.getVoicesWithCache();
          
          set({ 
            voices, 
            isLoadingVoices: false,
            selectedVoice: get().selectedVoice || voices[0] || null,
          });
        } catch (error) {
          set({ 
            isLoadingVoices: false,
            generation: {
              ...get().generation,
              error: extractErrorMessage(error),
            },
          });
        }
      },
      
      loadModels: async () => {
        const { apiKey } = get();
        if (!apiKey) return;
        
        set({ isLoadingModels: true });
        
        try {
          const service = new ElevenLabsService(apiKey);
          const models = await service.getModels();
          
          set({ 
            models, 
            isLoadingModels: false,
            selectedModel: get().selectedModel || models.find(m => m.model_id === ELEVENLABS_MODELS.ELEVEN_MULTILINGUAL_V2) || models[0] || null,
          });
        } catch (error) {
          set({ 
            isLoadingModels: false,
            generation: {
              ...get().generation,
              error: extractErrorMessage(error),
            },
          });
        }
      },
      
      selectVoice: (voice: ElevenLabsVoice) => {
        set({ selectedVoice: voice });
      },
      
      selectModel: (model: ElevenLabsModel) => {
        set({ selectedModel: model });
      },
      
      generateSpeech: async (request: TTSRequest) => {
        const { apiKey } = get();
        if (!apiKey) return;
        
        set({
          generation: {
            isGenerating: true,
            progress: 0,
            currentText: request.text,
            error: null,
            generatedAudio: null,
            audioUrl: null,
            alignment: null,
          },
        });
        
        try {
          const service = new ElevenLabsService(apiKey);
          
          // Simulate progress
          const progressInterval = setInterval(() => {
            set(state => ({
              generation: {
                ...state.generation,
                progress: Math.min(state.generation.progress + 10, 90),
              },
            }));
          }, 200);
          
          const response = await service.textToSpeech(request);
          
          clearInterval(progressInterval);
          
          const audioUrl = createAudioBlobUrl(response.audio);
          
          set({
            generation: {
              isGenerating: false,
              progress: 100,
              currentText: request.text,
              error: null,
              generatedAudio: response.audio,
              audioUrl,
              alignment: response.alignment,
            },
          });
        } catch (error) {
          set({
            generation: {
              isGenerating: false,
              progress: 0,
              currentText: request.text,
              error: extractErrorMessage(error),
              generatedAudio: null,
              audioUrl: null,
              alignment: null,
            },
          });
        }
      },
      
      streamSpeech: async (request: TTSRequest, options?: StreamingOptions) => {
        const { apiKey } = get();
        if (!apiKey) throw new Error('API key not set');
        
        const service = new ElevenLabsStreamingService(apiKey);
        return service.streamTextToSpeech(request, options);
      },
      
      cloneVoice: async (request: VoiceCloneRequest) => {
        const { apiKey } = get();
        if (!apiKey) return;
        
        set({
          voiceClone: {
            isCloning: true,
            progress: 0,
            clonedVoices: get().voiceClone.clonedVoices,
            error: null,
          },
        });
        
        try {
          const service = new ElevenLabsVoiceService(apiKey);
          
          // Simulate progress
          const progressInterval = setInterval(() => {
            set(state => ({
              voiceClone: {
                ...state.voiceClone,
                progress: Math.min(state.voiceClone.progress + 5, 90),
              },
            }));
          }, 500);
          
          const result = await service.cloneVoiceWithValidation(request);
          
          clearInterval(progressInterval);
          
          if (result.errors) {
            set({
              voiceClone: {
                isCloning: false,
                progress: 0,
                clonedVoices: get().voiceClone.clonedVoices,
                error: result.errors.join(', '),
              },
            });
            return;
          }
          
          if (result.voice) {
            set({
              voiceClone: {
                isCloning: false,
                progress: 100,
                clonedVoices: [...get().voiceClone.clonedVoices, result.voice],
                error: null,
              },
              voices: [...get().voices, result.voice],
            });
          }
        } catch (error) {
          set({
            voiceClone: {
              isCloning: false,
              progress: 0,
              clonedVoices: get().voiceClone.clonedVoices,
              error: extractErrorMessage(error),
            },
          });
        }
      },
      
      designVoice: async (request: VoiceDesignRequest) => {
        const { apiKey } = get();
        if (!apiKey) return;
        
        set({
          voiceDesign: {
            isDesigning: true,
            progress: 0,
            designedVoices: get().voiceDesign.designedVoices,
            error: null,
          },
        });
        
        try {
          const service = new ElevenLabsVoiceService(apiKey);
          
          // Simulate progress
          const progressInterval = setInterval(() => {
            set(state => ({
              voiceDesign: {
                ...state.voiceDesign,
                progress: Math.min(state.voiceDesign.progress + 5, 90),
              },
            }));
          }, 500);
          
          const result = await service.designVoiceWithValidation(request);
          
          clearInterval(progressInterval);
          
          if (result.errors) {
            set({
              voiceDesign: {
                isDesigning: false,
                progress: 0,
                designedVoices: get().voiceDesign.designedVoices,
                error: result.errors.join(', '),
              },
            });
            return;
          }
          
          if (result.voice) {
            set({
              voiceDesign: {
                isDesigning: false,
                progress: 100,
                designedVoices: [...get().voiceDesign.designedVoices, result.voice],
                error: null,
              },
              voices: [...get().voices, result.voice],
            });
          }
        } catch (error) {
          set({
            voiceDesign: {
              isDesigning: false,
              progress: 0,
              designedVoices: get().voiceDesign.designedVoices,
              error: extractErrorMessage(error),
            },
          });
        }
      },
      
      loadHistory: async (page = 1, pageSize = 100) => {
        const { apiKey } = get();
        if (!apiKey) return;
        
        set({
          history: {
            ...get().history,
            isLoading: true,
            error: null,
          },
        });
        
        try {
          const service = new ElevenLabsHistoryService(apiKey);
          const response = await service.getHistory(page, pageSize);
          
          set({
            history: {
              items: response.history,
              isLoading: false,
              error: null,
              currentPage: page,
              totalPages: response.has_more ? page + 1 : page,
            },
          });
        } catch (error) {
          set({
            history: {
              ...get().history,
              isLoading: false,
              error: extractErrorMessage(error),
            },
          });
        }
      },
      
      loadUser: async () => {
        const { apiKey } = get();
        if (!apiKey) return;
        
        set({
          user: {
            subscription: null,
            isLoading: true,
            error: null,
          },
        });
        
        try {
          const service = new ElevenLabsUserService(apiKey);
          const subscription = await service.getSubscription();
          
          set({
            user: {
              subscription,
              isLoading: false,
              error: null,
            },
          });
        } catch (error) {
          set({
            user: {
              subscription: null,
              isLoading: false,
              error: extractErrorMessage(error),
            },
          });
        }
      },
      
      clearError: (section) => {
        set(state => ({
          [section]: {
            ...state[section],
            error: null,
          },
        }));
      },
      
      reset: () => {
        set({
          apiKey: null,
          voices: [],
          selectedVoice: null,
          isLoadingVoices: false,
          models: [],
          selectedModel: null,
          isLoadingModels: false,
          generation: {
            isGenerating: false,
            progress: 0,
            currentText: '',
            error: null,
            generatedAudio: null,
            audioUrl: null,
            alignment: null,
          },
          voiceClone: {
            isCloning: false,
            progress: 0,
            clonedVoices: [],
            error: null,
          },
          voiceDesign: {
            isDesigning: false,
            progress: 0,
            designedVoices: [],
            error: null,
          },
          history: {
            items: [],
            isLoading: false,
            error: null,
            currentPage: 1,
            totalPages: 1,
          },
          user: {
            subscription: null,
            isLoading: false,
            error: null,
          },
        });
      },
    }),
    {
      name: 'elevenlabs-store',
      partialize: (state) => ({
        apiKey: state.apiKey,
        selectedVoice: state.selectedVoice,
        selectedModel: state.selectedModel,
        voices: state.voices,
        models: state.models,
      }),
    }
  )
);

// Helper hooks
export const useElevenLabsVoices = () => {
  const { voices, selectedVoice, isLoadingVoices, loadVoices, selectVoice } = useElevenLabsStore();
  return { voices, selectedVoice, isLoadingVoices, loadVoices, selectVoice };
};

export const useElevenLabsModels = () => {
  const { models, selectedModel, isLoadingModels, loadModels, selectModel } = useElevenLabsStore();
  return { models, selectedModel, isLoadingModels, loadModels, selectModel };
};

export const useElevenLabsGeneration = () => {
  const { generation, generateSpeech, streamSpeech, clearError } = useElevenLabsStore();
  return { 
    generation, 
    generateSpeech, 
    streamSpeech, 
    clearError: () => clearError('generation'),
  };
};

export const useElevenLabsVoiceClone = () => {
  const { voiceClone, cloneVoice, clearError } = useElevenLabsStore();
  return { 
    voiceClone, 
    cloneVoice, 
    clearError: () => clearError('voiceClone'),
  };
};

export const useElevenLabsVoiceDesign = () => {
  const { voiceDesign, designVoice, clearError } = useElevenLabsStore();
  return { 
    voiceDesign, 
    designVoice, 
    clearError: () => clearError('voiceDesign'),
  };
};

export const useElevenLabsHistory = () => {
  const { history, loadHistory, clearError } = useElevenLabsStore();
  return { 
    history, 
    loadHistory, 
    clearError: () => clearError('history'),
  };
};

export const useElevenLabsUser = () => {
  const { user, loadUser, clearError } = useElevenLabsStore();
  return { 
    user, 
    loadUser, 
    clearError: () => clearError('user'),
  };
};

export const useElevenLabsConfig = () => {
  const { apiKey, setApiKey, reset } = useElevenLabsStore();
  return { apiKey, setApiKey, reset };
};