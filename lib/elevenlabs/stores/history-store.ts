// ElevenLabs History Store
// Specialized store for history management

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ElevenLabsHistoryService } from '../services/history-service';
import { extractErrorMessage } from '../utils';
import type { HistoryItem } from '../types';

interface HistoryStoreState {
  // History Items
  items: HistoryItem[];
  filteredItems: HistoryItem[];
  selectedItems: string[];
  
  // Pagination
  currentPage: number;
  pageSize: number;
  totalPages: number;
  hasMore: boolean;
  
  // Filters
  filters: {
    search: string;
    voice_id: string;
    model_id: string;
    start_date: Date | null;
    end_date: Date | null;
    category: string;
  };
  
  // Sort
  sort: {
    field: 'date_unix' | 'voice_name' | 'text' | 'character_count';
    direction: 'asc' | 'desc';
  };
  
  // Analytics
  analytics: {
    data: any;
    timeRange: 'last_7_days' | 'last_30_days' | 'last_90_days' | 'all_time';
    isLoading: boolean;
    error: string | null;
  };
  
  // Loading States
  isLoading: boolean;
  isSearching: boolean;
  isDeleting: boolean;
  isExporting: boolean;
  
  // Error States
  error: string | null;
  
  // Cache
  cache: Map<string, HistoryItem[]>;
  cacheExpiry: number;
}

interface HistoryStoreActions {
  // History actions
  loadHistory: (apiKey: string, page?: number, pageSize?: number) => Promise<void>;
  loadMoreHistory: (apiKey: string) => Promise<void>;
  refreshHistory: (apiKey: string) => Promise<void>;
  searchHistory: (apiKey: string, query: any) => Promise<void>;
  
  // Item actions
  getHistoryItem: (apiKey: string, itemId: string) => Promise<HistoryItem | null>;
  deleteHistoryItem: (apiKey: string, itemId: string) => Promise<void>;
  downloadHistoryItem: (apiKey: string, itemId: string, filename?: string) => Promise<void>;
  
  // Selection actions
  selectItem: (itemId: string) => void;
  unselectItem: (itemId: string) => void;
  selectAll: () => void;
  unselectAll: () => void;
  bulkDelete: (apiKey: string, itemIds: string[]) => Promise<void>;
  
  // Filter actions
  setFilters: (filters: Partial<HistoryStoreState['filters']>) => void;
  clearFilters: () => void;
  applyFilters: () => void;
  
  // Sort actions
  setSortField: (field: HistoryStoreState['sort']['field']) => void;
  setSortDirection: (direction: HistoryStoreState['sort']['direction']) => void;
  toggleSort: (field: HistoryStoreState['sort']['field']) => void;
  
  // Analytics actions
  loadAnalytics: (apiKey: string, timeRange?: HistoryStoreState['analytics']['timeRange']) => Promise<void>;
  setAnalyticsTimeRange: (timeRange: HistoryStoreState['analytics']['timeRange']) => void;
  
  // Export actions
  exportHistory: (apiKey: string, format?: 'json' | 'csv', filters?: any) => Promise<string>;
  
  // Utility actions
  clearError: () => void;
  clearCache: () => void;
  reset: () => void;
}

type HistoryStore = HistoryStoreState & HistoryStoreActions;

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export const useHistoryStore = create<HistoryStore>()(
  persist(
    (set, get) => ({
      // Initial state
      items: [],
      filteredItems: [],
      selectedItems: [],
      currentPage: 1,
      pageSize: 100,
      totalPages: 1,
      hasMore: false,
      filters: {
        search: '',
        voice_id: '',
        model_id: '',
        start_date: null,
        end_date: null,
        category: '',
      },
      sort: {
        field: 'date_unix',
        direction: 'desc',
      },
      analytics: {
        data: null,
        timeRange: 'last_30_days',
        isLoading: false,
        error: null,
      },
      isLoading: false,
      isSearching: false,
      isDeleting: false,
      isExporting: false,
      error: null,
      cache: new Map(),
      cacheExpiry: Date.now() + CACHE_DURATION,
      
      // Actions
      loadHistory: async (apiKey: string, page = 1, pageSize = 100) => {
        set({ isLoading: true, error: null });
        
        try {
          const service = new ElevenLabsHistoryService(apiKey);
          const response = await service.getHistory(page, pageSize);
          
          set({
            items: response.history,
            filteredItems: response.history,
            currentPage: page,
            pageSize,
            hasMore: response.has_more,
            totalPages: response.has_more ? page + 1 : page,
            isLoading: false,
          });
          
          // Apply current filters
          get().applyFilters();
        } catch (error) {
          set({
            isLoading: false,
            error: extractErrorMessage(error),
          });
        }
      },
      
      loadMoreHistory: async (apiKey: string) => {
        const { currentPage, pageSize, hasMore, isLoading } = get();
        
        if (!hasMore || isLoading) return;
        
        set({ isLoading: true });
        
        try {
          const service = new ElevenLabsHistoryService(apiKey);
          const response = await service.getHistory(currentPage + 1, pageSize);
          
          set(state => ({
            items: [...state.items, ...response.history],
            filteredItems: [...state.filteredItems, ...response.history],
            currentPage: currentPage + 1,
            hasMore: response.has_more,
            totalPages: response.has_more ? currentPage + 2 : currentPage + 1,
            isLoading: false,
          }));
          
          // Apply current filters
          get().applyFilters();
        } catch (error) {
          set({
            isLoading: false,
            error: extractErrorMessage(error),
          });
        }
      },
      
      refreshHistory: async (apiKey: string) => {
        const { pageSize } = get();
        await get().loadHistory(apiKey, 1, pageSize);
      },
      
      searchHistory: async (apiKey: string, query: any) => {
        set({ isSearching: true, error: null });
        
        try {
          const service = new ElevenLabsHistoryService(apiKey);
          const response = await service.searchHistory(query);
          
          set({
            items: response.history,
            filteredItems: response.history,
            currentPage: 1,
            hasMore: response.has_more,
            totalPages: response.has_more ? 2 : 1,
            isSearching: false,
          });
        } catch (error) {
          set({
            isSearching: false,
            error: extractErrorMessage(error),
          });
        }
      },
      
      getHistoryItem: async (apiKey: string, itemId: string) => {
        try {
          const service = new ElevenLabsHistoryService(apiKey);
          return await service.getHistoryItem(itemId);
        } catch (error) {
          console.error('Failed to get history item:', error);
          return null;
        }
      },
      
      deleteHistoryItem: async (apiKey: string, itemId: string) => {
        set({ isDeleting: true, error: null });
        
        try {
          const service = new ElevenLabsHistoryService(apiKey);
          await service.deleteHistoryItem(itemId);
          
          set(state => ({
            items: state.items.filter(item => item.history_item_id !== itemId),
            filteredItems: state.filteredItems.filter(item => item.history_item_id !== itemId),
            selectedItems: state.selectedItems.filter(id => id !== itemId),
            isDeleting: false,
          }));
        } catch (error) {
          set({
            isDeleting: false,
            error: extractErrorMessage(error),
          });
        }
      },
      
      downloadHistoryItem: async (apiKey: string, itemId: string, filename?: string) => {
        try {
          const service = new ElevenLabsHistoryService(apiKey);
          await service.downloadHistoryItem(itemId, filename);
        } catch (error) {
          set({ error: extractErrorMessage(error) });
        }
      },
      
      selectItem: (itemId: string) => {
        set(state => ({
          selectedItems: [...state.selectedItems, itemId],
        }));
      },
      
      unselectItem: (itemId: string) => {
        set(state => ({
          selectedItems: state.selectedItems.filter(id => id !== itemId),
        }));
      },
      
      selectAll: () => {
        set(state => ({
          selectedItems: state.filteredItems.map(item => item.history_item_id),
        }));
      },
      
      unselectAll: () => {
        set({ selectedItems: [] });
      },
      
      bulkDelete: async (apiKey: string, itemIds: string[]) => {
        set({ isDeleting: true, error: null });
        
        try {
          const service = new ElevenLabsHistoryService(apiKey);
          
          // Delete items one by one
          for (const itemId of itemIds) {
            await service.deleteHistoryItem(itemId);
          }
          
          set(state => ({
            items: state.items.filter(item => !itemIds.includes(item.history_item_id)),
            filteredItems: state.filteredItems.filter(item => !itemIds.includes(item.history_item_id)),
            selectedItems: [],
            isDeleting: false,
          }));
        } catch (error) {
          set({
            isDeleting: false,
            error: extractErrorMessage(error),
          });
        }
      },
      
      setFilters: (newFilters: Partial<HistoryStoreState['filters']>) => {
        set(state => ({
          filters: {
            ...state.filters,
            ...newFilters,
          },
        }));
        get().applyFilters();
      },
      
      clearFilters: () => {
        set({
          filters: {
            search: '',
            voice_id: '',
            model_id: '',
            start_date: null,
            end_date: null,
            category: '',
          },
        });
        get().applyFilters();
      },
      
      applyFilters: () => {
        const { items, filters, sort } = get();
        
        let filtered = items.filter(item => {
          if (filters.search && !item.text.toLowerCase().includes(filters.search.toLowerCase())) {
            return false;
          }
          
          if (filters.voice_id && item.voice_id !== filters.voice_id) {
            return false;
          }
          
          if (filters.model_id && item.model_id !== filters.model_id) {
            return false;
          }
          
          if (filters.start_date && new Date(item.date_unix * 1000) < filters.start_date) {
            return false;
          }
          
          if (filters.end_date && new Date(item.date_unix * 1000) > filters.end_date) {
            return false;
          }
          
          if (filters.category && item.voice_category !== filters.category) {
            return false;
          }
          
          return true;
        });
        
        // Apply sorting
        filtered.sort((a, b) => {
          let aValue: any;
          let bValue: any;

          if (sort.field === 'character_count') {
            aValue = a.text.length;
            bValue = b.text.length;
          } else {
            aValue = (a as any)[sort.field];
            bValue = (b as any)[sort.field];
          }
          
          if (typeof aValue === 'string') {
            aValue = aValue.toLowerCase();
          }
          if (typeof bValue === 'string') {
            bValue = bValue.toLowerCase();
          }
          
          if (sort.direction === 'asc') {
            return aValue > bValue ? 1 : -1;
          } else {
            return aValue < bValue ? 1 : -1;
          }
        });
        
        set({ filteredItems: filtered });
      },
      
      setSortField: (field: HistoryStoreState['sort']['field']) => {
        set(state => ({
          sort: {
            ...state.sort,
            field,
          },
        }));
        get().applyFilters();
      },
      
      setSortDirection: (direction: HistoryStoreState['sort']['direction']) => {
        set(state => ({
          sort: {
            ...state.sort,
            direction,
          },
        }));
        get().applyFilters();
      },
      
      toggleSort: (field: HistoryStoreState['sort']['field']) => {
        set(state => ({
          sort: {
            field,
            direction: state.sort.field === field && state.sort.direction === 'asc' ? 'desc' : 'asc',
          },
        }));
        get().applyFilters();
      },
      
      loadAnalytics: async (apiKey: string, timeRange?: HistoryStoreState['analytics']['timeRange']) => {
        const range = timeRange || get().analytics.timeRange;
        
        set(state => ({
          analytics: {
            ...state.analytics,
            isLoading: true,
            error: null,
            timeRange: range,
          },
        }));
        
        try {
          const service = new ElevenLabsHistoryService(apiKey);
          const data = await service.getHistoryAnalytics(range);
          
          set(state => ({
            analytics: {
              ...state.analytics,
              data,
              isLoading: false,
            },
          }));
        } catch (error) {
          set(state => ({
            analytics: {
              ...state.analytics,
              isLoading: false,
              error: extractErrorMessage(error),
            },
          }));
        }
      },
      
      setAnalyticsTimeRange: (timeRange: HistoryStoreState['analytics']['timeRange']) => {
        set(state => ({
          analytics: {
            ...state.analytics,
            timeRange,
          },
        }));
      },
      
      exportHistory: async (apiKey: string, format = 'json', filters?: any) => {
        set({ isExporting: true, error: null });
        
        try {
          const service = new ElevenLabsHistoryService(apiKey);
          const data = await service.exportHistory(format, filters);
          
          set({ isExporting: false });
          return data;
        } catch (error) {
          set({
            isExporting: false,
            error: extractErrorMessage(error),
          });
          throw error;
        }
      },
      
      clearError: () => {
        set({ error: null });
      },
      
      clearCache: () => {
        set({ cache: new Map() });
      },
      
      reset: () => {
        set({
          items: [],
          filteredItems: [],
          selectedItems: [],
          currentPage: 1,
          pageSize: 100,
          totalPages: 1,
          hasMore: false,
          filters: {
            search: '',
            voice_id: '',
            model_id: '',
            start_date: null,
            end_date: null,
            category: '',
          },
          sort: {
            field: 'date_unix',
            direction: 'desc',
          },
          analytics: {
            data: null,
            timeRange: 'last_30_days',
            isLoading: false,
            error: null,
          },
          isLoading: false,
          isSearching: false,
          isDeleting: false,
          isExporting: false,
          error: null,
          cache: new Map(),
          cacheExpiry: Date.now() + CACHE_DURATION,
        });
      },
    }),
    {
      name: 'history-store',
      partialize: (state) => ({
        filters: state.filters,
        sort: state.sort,
        analytics: {
          timeRange: state.analytics.timeRange,
        },
      }),
    }
  )
);