// ElevenLabs User Store
// Specialized store for user account and subscription management

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ElevenLabsUserService } from '../services/user-service';
import { extractErrorMessage } from '../utils';
import type { UserSubscription } from '../types';

interface UserStoreState {
  // User Data
  user: {
    subscription: UserSubscription | null;
    profile: {
      first_name: string;
      avatar_url: string;
      is_onboarded: boolean;
      is_api_user: boolean;
    } | null;
    features: {
      instant_voice_cloning: boolean;
      professional_voice_cloning: boolean;
      voice_design: boolean;
      api_access: boolean;
      commercial_license: boolean;
      priority_support: boolean;
      custom_models: boolean;
      bulk_generation: boolean;
      pronunciation_dictionaries: boolean;
      voice_library_access: boolean;
      streaming: boolean;
      timestamps: boolean;
      ssml: boolean;
      voice_mixing: boolean;
      voice_effects: boolean;
    } | null;
    isLoading: boolean;
    error: string | null;
  };
  
  // Usage Statistics
  usage: {
    current: {
      character_count: number;
      character_limit: number;
      voice_count: number;
      voice_limit: number;
      professional_voice_count: number;
      professional_voice_limit: number;
      instant_voice_clone_count: number;
      instant_voice_clone_limit: number;
      usage_percentage: number;
      days_until_reset: number;
      next_reset_date: Date | null;
    };
    history: Array<{
      date: string;
      characters_used: number;
      generations_count: number;
      voices_used: string[];
      models_used: string[];
    }>;
    trends: {
      characters_change: number;
      generations_change: number;
    };
    isLoading: boolean;
    error: string | null;
  };
  
  // Billing Information
  billing: {
    subscription: UserSubscription | null;
    next_invoice_date: Date | null;
    billing_period: string;
    currency: string;
    has_open_invoices: boolean;
    payment_method: string;
    auto_renew: boolean;
    available_plans: Array<{
      id: string;
      name: string;
      price: number;
      currency: string;
      billing_period: string;
      character_limit: number;
      voice_limit: number;
      professional_voice_limit: number;
      features: string[];
      is_current: boolean;
      is_recommended: boolean;
    }>;
    isLoading: boolean;
    error: string | null;
  };
  
  // API Keys
  apiKeys: {
    keys: Array<{
      key_id: string;
      key_name: string;
      key_preview: string;
      created_at: Date;
      last_used: Date | null;
      is_active: boolean;
      permissions: string[];
    }>;
    isLoading: boolean;
    error: string | null;
  };
  
  // Preferences
  preferences: {
    notifications: {
      email_notifications: boolean;
      push_notifications: boolean;
      quota_warnings: boolean;
      new_features: boolean;
      marketing: boolean;
    };
    language: string;
    timezone: string;
    isLoading: boolean;
    error: string | null;
  };
}

interface UserStoreActions {
  // User actions
  loadUser: (apiKey: string) => Promise<void>;
  loadUserFeatures: (apiKey: string) => Promise<void>;
  updateProfile: (apiKey: string, updates: any) => Promise<void>;
  
  // Usage actions
  loadUsageStatistics: (apiKey: string) => Promise<void>;
  loadUsageHistory: (apiKey: string, period?: string) => Promise<void>;
  checkQuota: (apiKey: string, charactersNeeded: number) => Promise<any>;
  estimateCost: (apiKey: string, text: string) => Promise<any>;
  
  // Billing actions
  loadBillingInfo: (apiKey: string) => Promise<void>;
  loadAvailablePlans: (apiKey: string) => Promise<void>;
  
  // API Key actions
  loadApiKeys: (apiKey: string) => Promise<void>;
  generateApiKey: (apiKey: string, name: string, permissions?: string[]) => Promise<void>;
  revokeApiKey: (apiKey: string, keyId: string) => Promise<void>;
  
  // Preference actions
  loadPreferences: (apiKey: string) => Promise<void>;
  updatePreferences: (apiKey: string, preferences: any) => Promise<void>;
  
  // Utility actions
  clearError: (section: string) => void;
  reset: () => void;
}

type UserStore = UserStoreState & UserStoreActions;

export const useUserStore = create<UserStore>()(
  persist(
    (set) => ({
      // Initial state
      user: {
        subscription: null,
        profile: null,
        features: null,
        isLoading: false,
        error: null,
      },
      
      usage: {
        current: {
          character_count: 0,
          character_limit: 0,
          voice_count: 0,
          voice_limit: 0,
          professional_voice_count: 0,
          professional_voice_limit: 0,
          instant_voice_clone_count: 0,
          instant_voice_clone_limit: 0,
          usage_percentage: 0,
          days_until_reset: 0,
          next_reset_date: null,
        },
        history: [],
        trends: {
          characters_change: 0,
          generations_change: 0,
        },
        isLoading: false,
        error: null,
      },
      
      billing: {
        subscription: null,
        next_invoice_date: null,
        billing_period: '',
        currency: '',
        has_open_invoices: false,
        payment_method: '',
        auto_renew: false,
        available_plans: [],
        isLoading: false,
        error: null,
      },
      
      apiKeys: {
        keys: [],
        isLoading: false,
        error: null,
      },
      
      preferences: {
        notifications: {
          email_notifications: true,
          push_notifications: true,
          quota_warnings: true,
          new_features: true,
          marketing: false,
        },
        language: 'en',
        timezone: 'UTC',
        isLoading: false,
        error: null,
      },
      
      // Actions
      loadUser: async (apiKey: string) => {
        set(state => ({
          user: {
            ...state.user,
            isLoading: true,
            error: null,
          },
        }));
        
        try {
          const service = new ElevenLabsUserService(apiKey);
          const userData = await service.getUser();
          
          set(state => ({
            user: {
              ...state.user,
              subscription: userData.subscription,
              profile: {
                first_name: userData.first_name,
                avatar_url: userData.avatar_url,
                is_onboarded: userData.is_onboarded,
                is_api_user: userData.is_api_user,
              },
              isLoading: false,
            },
          }));
        } catch (error) {
          set(state => ({
            user: {
              ...state.user,
              isLoading: false,
              error: extractErrorMessage(error),
            },
          }));
        }
      },
      
      loadUserFeatures: async (apiKey: string) => {
        try {
          const service = new ElevenLabsUserService(apiKey);
          const features = await service.getFeatureAvailability();
          
          set(state => ({
            user: {
              ...state.user,
              features,
            },
          }));
        } catch (error) {
          console.error('Failed to load user features:', error);
        }
      },
      
      updateProfile: async (apiKey: string, updates: any) => {
        try {
          const service = new ElevenLabsUserService(apiKey);
          await service.updateProfile(updates);
          
          set(state => ({
            user: {
              ...state.user,
              profile: {
                ...state.user.profile!,
                ...updates,
              },
            },
          }));
        } catch (error) {
          set(state => ({
            user: {
              ...state.user,
              error: extractErrorMessage(error),
            },
          }));
        }
      },
      
      loadUsageStatistics: async (apiKey: string) => {
        set(state => ({
          usage: {
            ...state.usage,
            isLoading: true,
            error: null,
          },
        }));
        
        try {
          const service = new ElevenLabsUserService(apiKey);
          const stats = await service.getUsageStatistics();
          
          set(state => ({
            usage: {
              ...state.usage,
              current: {
                character_count: stats.character_count,
                character_limit: stats.character_limit,
                voice_count: stats.voice_count,
                voice_limit: stats.voice_limit,
                professional_voice_count: stats.professional_voice_count,
                professional_voice_limit: stats.professional_voice_limit,
                instant_voice_clone_count: stats.instant_voice_clone_count,
                instant_voice_clone_limit: stats.instant_voice_clone_limit,
                usage_percentage: stats.usage_percentage,
                days_until_reset: stats.days_until_reset,
                next_reset_date: new Date(stats.next_character_count_reset_unix * 1000),
              },
              isLoading: false,
            },
          }));
        } catch (error) {
          set(state => ({
            usage: {
              ...state.usage,
              isLoading: false,
              error: extractErrorMessage(error),
            },
          }));
        }
      },
      
      loadUsageHistory: async (apiKey: string, period = 'last_30_days') => {
        try {
          const service = new ElevenLabsUserService(apiKey);
          const history = await service.getUsageHistory(period as any);
          
          set(state => ({
            usage: {
              ...state.usage,
              history,
            },
          }));
        } catch (error) {
          console.error('Failed to load usage history:', error);
        }
      },
      
      checkQuota: async (apiKey: string, charactersNeeded: number) => {
        try {
          const service = new ElevenLabsUserService(apiKey);
          return await service.checkQuota(charactersNeeded);
        } catch (error) {
          console.error('Failed to check quota:', error);
          return null;
        }
      },
      
      estimateCost: async (apiKey: string, text: string) => {
        try {
          const service = new ElevenLabsUserService(apiKey);
          return await service.estimateCost(text);
        } catch (error) {
          console.error('Failed to estimate cost:', error);
          return null;
        }
      },
      
      loadBillingInfo: async (apiKey: string) => {
        set(state => ({
          billing: {
            ...state.billing,
            isLoading: true,
            error: null,
          },
        }));
        
        try {
          const service = new ElevenLabsUserService(apiKey);
          const billingInfo = await service.getBillingInfo();
          
          set(state => ({
            billing: {
              ...state.billing,
              subscription: billingInfo.subscription,
              next_invoice_date: billingInfo.next_invoice_date,
              billing_period: billingInfo.billing_period,
              currency: billingInfo.currency,
              has_open_invoices: billingInfo.has_open_invoices,
              payment_method: billingInfo.payment_method,
              auto_renew: billingInfo.auto_renew,
              isLoading: false,
            },
          }));
        } catch (error) {
          set(state => ({
            billing: {
              ...state.billing,
              isLoading: false,
              error: extractErrorMessage(error),
            },
          }));
        }
      },
      
      loadAvailablePlans: async (apiKey: string) => {
        try {
          const service = new ElevenLabsUserService(apiKey);
          const plans = await service.getAvailablePlans();
          
          set(state => ({
            billing: {
              ...state.billing,
              available_plans: plans,
            },
          }));
        } catch (error) {
          console.error('Failed to load available plans:', error);
        }
      },
      
      loadApiKeys: async (apiKey: string) => {
        set(state => ({
          apiKeys: {
            ...state.apiKeys,
            isLoading: true,
            error: null,
          },
        }));
        
        try {
          const service = new ElevenLabsUserService(apiKey);
          const keys = await service.getApiKeys();
          
          set(state => ({
            apiKeys: {
              ...state.apiKeys,
              keys,
              isLoading: false,
            },
          }));
        } catch (error) {
          set(state => ({
            apiKeys: {
              ...state.apiKeys,
              isLoading: false,
              error: extractErrorMessage(error),
            },
          }));
        }
      },
      
      generateApiKey: async (apiKey: string, name: string, permissions: string[] = []) => {
        try {
          const service = new ElevenLabsUserService(apiKey);
          const newKey = await service.generateApiKey(name, permissions);
          
          set(state => ({
            apiKeys: {
              ...state.apiKeys,
              keys: [...state.apiKeys.keys, {
                key_id: newKey.key_id,
                key_name: newKey.key_name,
                key_preview: newKey.api_key.substring(0, 8) + '...',
                created_at: newKey.created_at,
                last_used: null,
                is_active: true,
                permissions: newKey.permissions,
              }],
            },
          }));
        } catch (error) {
          set(state => ({
            apiKeys: {
              ...state.apiKeys,
              error: extractErrorMessage(error),
            },
          }));
        }
      },
      
      revokeApiKey: async (apiKey: string, keyId: string) => {
        try {
          const service = new ElevenLabsUserService(apiKey);
          await service.revokeApiKey(keyId);
          
          set(state => ({
            apiKeys: {
              ...state.apiKeys,
              keys: state.apiKeys.keys.filter(key => key.key_id !== keyId),
            },
          }));
        } catch (error) {
          set(state => ({
            apiKeys: {
              ...state.apiKeys,
              error: extractErrorMessage(error),
            },
          }));
        }
      },
      
      loadPreferences: async (apiKey: string) => {
        set(state => ({
          preferences: {
            ...state.preferences,
            isLoading: true,
            error: null,
          },
        }));
        
        try {
          const service = new ElevenLabsUserService(apiKey);
          const notifications = await service.getNotificationPreferences();
          
          set(state => ({
            preferences: {
              ...state.preferences,
              notifications,
              isLoading: false,
            },
          }));
        } catch (error) {
          set(state => ({
            preferences: {
              ...state.preferences,
              isLoading: false,
              error: extractErrorMessage(error),
            },
          }));
        }
      },
      
      updatePreferences: async (apiKey: string, preferences: any) => {
        try {
          const service = new ElevenLabsUserService(apiKey);
          await service.updateNotificationPreferences(preferences);
          
          set(state => ({
            preferences: {
              ...state.preferences,
              notifications: {
                ...state.preferences.notifications,
                ...preferences,
              },
            },
          }));
        } catch (error) {
          set(state => ({
            preferences: {
              ...state.preferences,
              error: extractErrorMessage(error),
            },
          }));
        }
      },
      
      clearError: (section: string) => {
        set(state => ({
          [section]: {
            ...state[section as keyof UserStoreState],
            error: null,
          },
        }));
      },
      
      reset: () => {
        set({
          user: {
            subscription: null,
            profile: null,
            features: null,
            isLoading: false,
            error: null,
          },
          usage: {
            current: {
              character_count: 0,
              character_limit: 0,
              voice_count: 0,
              voice_limit: 0,
              professional_voice_count: 0,
              professional_voice_limit: 0,
              instant_voice_clone_count: 0,
              instant_voice_clone_limit: 0,
              usage_percentage: 0,
              days_until_reset: 0,
              next_reset_date: null,
            },
            history: [],
            trends: {
              characters_change: 0,
              generations_change: 0,
            },
            isLoading: false,
            error: null,
          },
          billing: {
            subscription: null,
            next_invoice_date: null,
            billing_period: '',
            currency: '',
            has_open_invoices: false,
            payment_method: '',
            auto_renew: false,
            available_plans: [],
            isLoading: false,
            error: null,
          },
          apiKeys: {
            keys: [],
            isLoading: false,
            error: null,
          },
          preferences: {
            notifications: {
              email_notifications: true,
              push_notifications: true,
              quota_warnings: true,
              new_features: true,
              marketing: false,
            },
            language: 'en',
            timezone: 'UTC',
            isLoading: false,
            error: null,
          },
        });
      },
    }),
    {
      name: 'user-store',
      partialize: (state) => ({
        preferences: state.preferences,
      }),
    }
  )
);