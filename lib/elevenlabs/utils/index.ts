// ElevenLabs Utilities
// Helper functions and utilities for ElevenLabs operations

import { ELEVENLABS_OUTPUT_FORMATS, VOICE_CLONE_REQUIREMENTS } from '../constants';
import type { ElevenLabsVoice, TTSRequest } from '../types';

/**
 * Validate API key format
 */
export function validateApiKey(apiKey: string): boolean {
  // ElevenLabs API keys start with 'sk-' and are 32 characters long
  return /^sk-[a-zA-Z0-9]{32}$/.test(apiKey);
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Format duration in seconds to readable format
 */
export function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * Extract error message from ElevenLabs API error
 */
export function extractErrorMessage(error: any): string {
  if (error.response?.data?.detail) {
    const detail = error.response.data.detail;
    if (Array.isArray(detail)) {
      return detail.map((d: any) => d.msg).join(', ');
    }
    return detail.message || detail;
  }
  return error.message || 'An unexpected error occurred';
}

/**
 * Create blob URL from ArrayBuffer
 */
export function createAudioBlobUrl(audioBuffer: ArrayBuffer, mimeType: string = 'audio/mpeg'): string {
  const blob = new Blob([audioBuffer], { type: mimeType });
  return URL.createObjectURL(blob);
}

/**
 * Download audio file from ArrayBuffer
 */
export function downloadAudio(audioBuffer: ArrayBuffer, filename: string = 'speech.mp3'): void {
  const blob = new Blob([audioBuffer], { type: 'audio/mpeg' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

/**
 * Convert output format to MIME type
 */
export function outputFormatToMimeType(format: string): string {
  const formatMap: Record<string, string> = {
    [ELEVENLABS_OUTPUT_FORMATS.MP3_22050_32]: 'audio/mpeg',
    [ELEVENLABS_OUTPUT_FORMATS.MP3_44100_32]: 'audio/mpeg',
    [ELEVENLABS_OUTPUT_FORMATS.MP3_44100_64]: 'audio/mpeg',
    [ELEVENLABS_OUTPUT_FORMATS.MP3_44100_96]: 'audio/mpeg',
    [ELEVENLABS_OUTPUT_FORMATS.MP3_44100_128]: 'audio/mpeg',
    [ELEVENLABS_OUTPUT_FORMATS.MP3_44100_192]: 'audio/mpeg',
    [ELEVENLABS_OUTPUT_FORMATS.PCM_16000]: 'audio/wav',
    [ELEVENLABS_OUTPUT_FORMATS.PCM_22050]: 'audio/wav',
    [ELEVENLABS_OUTPUT_FORMATS.PCM_24000]: 'audio/wav',
    [ELEVENLABS_OUTPUT_FORMATS.PCM_44100]: 'audio/wav',
    [ELEVENLABS_OUTPUT_FORMATS.ULAW_8000]: 'audio/basic',
  };
  return formatMap[format] || 'audio/mpeg';
}

/**
 * Get file extension from output format
 */
export function outputFormatToExtension(format: string): string {
  const formatMap: Record<string, string> = {
    [ELEVENLABS_OUTPUT_FORMATS.MP3_22050_32]: 'mp3',
    [ELEVENLABS_OUTPUT_FORMATS.MP3_44100_32]: 'mp3',
    [ELEVENLABS_OUTPUT_FORMATS.MP3_44100_64]: 'mp3',
    [ELEVENLABS_OUTPUT_FORMATS.MP3_44100_96]: 'mp3',
    [ELEVENLABS_OUTPUT_FORMATS.MP3_44100_128]: 'mp3',
    [ELEVENLABS_OUTPUT_FORMATS.MP3_44100_192]: 'mp3',
    [ELEVENLABS_OUTPUT_FORMATS.PCM_16000]: 'wav',
    [ELEVENLABS_OUTPUT_FORMATS.PCM_22050]: 'wav',
    [ELEVENLABS_OUTPUT_FORMATS.PCM_24000]: 'wav',
    [ELEVENLABS_OUTPUT_FORMATS.PCM_44100]: 'wav',
    [ELEVENLABS_OUTPUT_FORMATS.ULAW_8000]: 'au',
  };
  return formatMap[format] || 'mp3';
}

/**
 * Validate TTS request
 */
export function validateTTSRequest(request: TTSRequest): string[] {
  const errors: string[] = [];
  
  if (!request.text || request.text.trim().length === 0) {
    errors.push('Text is required');
  }
  
  if (request.text && request.text.length > 5000) {
    errors.push('Text is too long (max 5000 characters)');
  }
  
  if (!request.voice_id) {
    errors.push('Voice ID is required');
  }
  
  if (request.voice_settings) {
    const { stability, similarity_boost, style } = request.voice_settings;
    
    if (stability !== undefined && (stability < 0 || stability > 1)) {
      errors.push('Stability must be between 0 and 1');
    }
    
    if (similarity_boost !== undefined && (similarity_boost < 0 || similarity_boost > 1)) {
      errors.push('Similarity boost must be between 0 and 1');
    }
    
    if (style !== undefined && (style < 0 || style > 1)) {
      errors.push('Style must be between 0 and 1');
    }
  }
  
  return errors;
}

/**
 * Validate voice clone files
 */
export function validateVoiceCloneFiles(files: File[]): string[] {
  const errors: string[] = [];
  
  if (files.length < VOICE_CLONE_REQUIREMENTS.MIN_FILES) {
    errors.push(`At least ${VOICE_CLONE_REQUIREMENTS.MIN_FILES} file is required`);
  }
  
  if (files.length > VOICE_CLONE_REQUIREMENTS.MAX_FILES) {
    errors.push(`Maximum ${VOICE_CLONE_REQUIREMENTS.MAX_FILES} files allowed`);
  }
  
  files.forEach((file, index) => {
    const extension = file.name.split('.').pop()?.toLowerCase();
    if (!extension || !VOICE_CLONE_REQUIREMENTS.SUPPORTED_FORMATS.includes(extension as any)) {
      errors.push(`File ${index + 1}: Unsupported format (${file.name})`);
    }
    
    if (file.size > VOICE_CLONE_REQUIREMENTS.MAX_FILE_SIZE) {
      errors.push(`File ${index + 1}: Too large (${formatFileSize(file.size)})`);
    }
  });
  
  return errors;
}

/**
 * Get audio duration from file
 */
export function getAudioDuration(file: File): Promise<number> {
  return new Promise((resolve, reject) => {
    const audio = new Audio();
    const url = URL.createObjectURL(file);
    
    audio.addEventListener('loadedmetadata', () => {
      URL.revokeObjectURL(url);
      resolve(audio.duration);
    });
    
    audio.addEventListener('error', () => {
      URL.revokeObjectURL(url);
      reject(new Error('Could not load audio file'));
    });
    
    audio.src = url;
  });
}

/**
 * Generate voice filename
 */
export function generateVoiceFilename(voice: ElevenLabsVoice, text: string, format: string): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const textPreview = text.substring(0, 50).replace(/[^a-zA-Z0-9]/g, '_');
  const extension = outputFormatToExtension(format);
  return `${voice.name}_${textPreview}_${timestamp}.${extension}`;
}

/**
 * Clean text for TTS
 */
export function cleanTextForTTS(text: string): string {
  return text
    .replace(/[\u200B-\u200D\uFEFF]/g, '') // Remove zero-width characters
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
}

/**
 * Split text into chunks for long texts
 */
export function splitTextIntoChunks(text: string, maxLength: number = 2500): string[] {
  if (text.length <= maxLength) {
    return [text];
  }
  
  const chunks: string[] = [];
  const sentences = text.split(/[.!?]+/);
  let currentChunk = '';
  
  for (const sentence of sentences) {
    const trimmedSentence = sentence.trim();
    if (!trimmedSentence) continue;
    
    if (currentChunk.length + trimmedSentence.length + 1 <= maxLength) {
      currentChunk += (currentChunk ? ' ' : '') + trimmedSentence + '.';
    } else {
      if (currentChunk) {
        chunks.push(currentChunk);
        currentChunk = '';
      }
      
      // If sentence is too long, split by words
      if (trimmedSentence.length > maxLength) {
        const words = trimmedSentence.split(' ');
        let wordChunk = '';
        
        for (const word of words) {
          if (wordChunk.length + word.length + 1 <= maxLength) {
            wordChunk += (wordChunk ? ' ' : '') + word;
          } else {
            if (wordChunk) {
              chunks.push(wordChunk + '.');
              wordChunk = '';
            }
            wordChunk = word;
          }
        }
        
        if (wordChunk) {
          currentChunk = wordChunk + '.';
        }
      } else {
        currentChunk = trimmedSentence + '.';
      }
    }
  }
  
  if (currentChunk) {
    chunks.push(currentChunk);
  }
  
  return chunks;
}

/**
 * Merge audio buffers
 */
export function mergeAudioBuffers(buffers: ArrayBuffer[]): ArrayBuffer {
  const totalLength = buffers.reduce((sum, buffer) => sum + buffer.byteLength, 0);
  const result = new Uint8Array(totalLength);
  let offset = 0;
  
  for (const buffer of buffers) {
    result.set(new Uint8Array(buffer), offset);
    offset += buffer.byteLength;
  }
  
  return result.buffer;
}

/**
 * Generate unique request ID
 */
export function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Check if browser supports audio playback
 */
export function supportsAudioPlayback(): boolean {
  return typeof Audio !== 'undefined';
}

/**
 * Check if browser supports Web Audio API
 */
export function supportsWebAudio(): boolean {
  return typeof AudioContext !== 'undefined' || typeof (window as any).webkitAudioContext !== 'undefined';
}

/**
 * Get voice category label
 */
export function getVoiceCategoryLabel(category: string): string {
  const categoryMap: Record<string, string> = {
    premade: 'Premade',
    cloned: 'Cloned',
    generated: 'Generated',
    professional: 'Professional',
  };
  return categoryMap[category] || category;
}

/**
 * Get voice gender from labels
 */
export function getVoiceGender(voice: ElevenLabsVoice): string {
  return voice.labels?.gender || 'Unknown';
}

/**
 * Get voice age from labels
 */
export function getVoiceAge(voice: ElevenLabsVoice): string {
  return voice.labels?.age || 'Unknown';
}

/**
 * Get voice accent from labels
 */
export function getVoiceAccent(voice: ElevenLabsVoice): string {
  return voice.labels?.accent || 'Unknown';
}

/**
 * Calculate estimated cost for text
 */
export function calculateEstimatedCost(text: string, characterCost: number = 0.0002): number {
  return text.length * characterCost;
}

/**
 * Format currency
 */
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 4,
  }).format(amount);
}

/**
 * Format number for display
 */
export function formatNumber(num: number, precision: number = 2): string {
  return new Intl.NumberFormat('en-US', {
    maximumFractionDigits: precision,
    minimumFractionDigits: precision,
  }).format(num);
}

/**
 * Format duration in milliseconds to readable format
 */
export function formatDurationMs(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * Generate session ID
 */
export function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Generate agent ID
 */
export function generateAgentId(): string {
  return `agent_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Convert base64 to blob
 */
export function base64ToBlob(base64: string, mimeType: string = 'audio/wav'): Blob {
  const byteCharacters = atob(base64);
  const byteNumbers = new Array(byteCharacters.length);
  
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mimeType });
}

/**
 * Convert blob to base64
 */
export function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      resolve(result.split(',')[1]); // Remove data URL prefix
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

/**
 * Check if WebSocket is supported
 */
export function supportsWebSocket(): boolean {
  return typeof WebSocket !== 'undefined';
}

/**
 * Check if MediaRecorder is supported
 */
export function supportsMediaRecorder(): boolean {
  return typeof MediaRecorder !== 'undefined';
}

/**
 * Check if getUserMedia is supported
 */
export function supportsGetUserMedia(): boolean {
  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
}

/**
 * Get optimal audio format based on browser support
 */
export function getOptimalAudioFormat(): string {
  const mimeTypes = [
    'audio/webm;codecs=opus',
    'audio/webm;codecs=vorbis',
    'audio/mp4;codecs=mp4a.40.2',
    'audio/mpeg',
    'audio/wav'
  ];
  
  for (const mimeType of mimeTypes) {
    if (MediaRecorder.isTypeSupported && MediaRecorder.isTypeSupported(mimeType)) {
      return mimeType;
    }
  }
  
  return 'audio/wav'; // fallback
}

/**
 * Validate conversational AI configuration
 */
export function validateConversationalAIConfig(config: any): string[] {
  const errors: string[] = [];
  
  if (!config.agentId) {
    errors.push('Agent ID is required');
  }
  
  if (config.voiceSettings) {
    const { stability, similarity_boost, style } = config.voiceSettings;
    
    if (stability !== undefined && (stability < 0 || stability > 1)) {
      errors.push('Voice stability must be between 0 and 1');
    }
    
    if (similarity_boost !== undefined && (similarity_boost < 0 || similarity_boost > 1)) {
      errors.push('Voice similarity boost must be between 0 and 1');
    }
    
    if (style !== undefined && (style < 0 || style > 1)) {
      errors.push('Voice style must be between 0 and 1');
    }
  }
  
  if (config.turnTaking) {
    const { sensitivity, max_silence_ms, min_speech_duration_ms } = config.turnTaking;
    
    if (sensitivity !== undefined && (sensitivity < 0 || sensitivity > 1)) {
      errors.push('Turn taking sensitivity must be between 0 and 1');
    }
    
    if (max_silence_ms !== undefined && max_silence_ms < 0) {
      errors.push('Max silence duration must be positive');
    }
    
    if (min_speech_duration_ms !== undefined && min_speech_duration_ms < 0) {
      errors.push('Min speech duration must be positive');
    }
  }
  
  return errors;
}

/**
 * Debounce function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return (...args: Parameters<T>) => {
    const later = () => {
      timeout = null;
      func(...args);
    };
    
    if (timeout) {
      clearTimeout(timeout);
    }
    
    timeout = setTimeout(later, wait);
  };
}

/**
 * Throttle function
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let lastTime = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    
    if (now - lastTime >= wait) {
      lastTime = now;
      func(...args);
    }
  };
}

/**
 * Calculate audio quality score
 */
export function calculateAudioQualityScore(metrics: {
  latency: number;
  jitter: number;
  packetLoss: number;
  signalToNoiseRatio: number;
}): number {
  const { latency, jitter, packetLoss, signalToNoiseRatio } = metrics;
  
  // Score based on different factors (0-100)
  const latencyScore = Math.max(0, 100 - (latency / 10)); // 10ms = 100 points
  const jitterScore = Math.max(0, 100 - (jitter * 20)); // 5ms jitter = 0 points
  const packetLossScore = Math.max(0, 100 - (packetLoss * 200)); // 0.5% loss = 0 points
  const snrScore = Math.min(100, signalToNoiseRatio * 2); // 50dB SNR = 100 points
  
  return Math.round((latencyScore + jitterScore + packetLossScore + snrScore) / 4);
}