"use client";

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

interface TopLoaderConfig {
  color: string;
  height: number;
  showSpinner: boolean;
  easing: string;
  speed: number;
  shadow: string;
  zIndex: number;
  showAtBottom: boolean;
  template: string;
  initialPosition: number;
  crawl: boolean;
  crawlSpeed: number;
}

interface TopLoaderState {
  // Configuration
  config: TopLoaderConfig;
  
  // Loading state
  isLoading: boolean;
  progress: number;
  
  // Theme-specific configurations
  themeConfigs: {
    dark: Partial<TopLoaderConfig>;
    light: Partial<TopLoaderConfig>;
  };
  
  // Preferences
  preferences: {
    enableAnimations: boolean;
    showProgressText: boolean;
    autoHideDelay: number;
    enableSounds: boolean;
  };
  
  // Analytics
  analytics: {
    loadingTimes: number[];
    averageLoadTime: number;
    totalLoads: number;
  };
  
  // Actions
  updateConfig: (newConfig: Partial<TopLoaderConfig>) => void;
  resetConfig: () => void;
  setLoading: (loading: boolean) => void;
  setProgress: (progress: number) => void;
  updateThemeConfig: (theme: 'dark' | 'light', config: Partial<TopLoaderConfig>) => void;
  updatePreferences: (preferences: Partial<TopLoaderState['preferences']>) => void;
  recordLoadTime: (time: number) => void;
  getThemeConfig: (theme: 'dark' | 'light') => Partial<TopLoaderConfig>;
  applyTheme: (theme: 'dark' | 'light') => void;
}

const defaultConfig: TopLoaderConfig = {
  color: "oklch(0.208 0.042 265.755)",
  height: 3,
  showSpinner: false,
  easing: "ease",
  speed: 200,
  shadow: "0 0 10px currentColor, 0 0 5px currentColor",
  zIndex: 1600,
  showAtBottom: false,
  template: '<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>',
  initialPosition: 0.08,
  crawl: true,
  crawlSpeed: 200,
};

export const useTopLoaderStore = create<TopLoaderState>()(
  persist(
    (set, get) => ({
      // Initial state
      config: defaultConfig,
      isLoading: false,
      progress: 0,
      
      themeConfigs: {
        dark: {
          color: "oklch(0.488 0.243 264.376)",
          shadow: "0 0 10px oklch(0.488 0.243 264.376), 0 0 5px oklch(0.488 0.243 264.376)",
        },
        light: {
          color: "oklch(0.208 0.042 265.755)",
          shadow: "0 0 10px oklch(0.208 0.042 265.755), 0 0 5px oklch(0.208 0.042 265.755)",
        },
      },
      
      preferences: {
        enableAnimations: true,
        showProgressText: false,
        autoHideDelay: 300,
        enableSounds: false,
      },
      
      analytics: {
        loadingTimes: [],
        averageLoadTime: 0,
        totalLoads: 0,
      },
      
      // Actions
      updateConfig: (newConfig) => {
        set((state) => ({
          config: {
            ...state.config,
            ...newConfig,
          },
        }));
      },
      
      resetConfig: () => {
        set({ config: defaultConfig });
      },
      
      setLoading: (loading) => {
        set({ isLoading: loading });
        
        // Record loading start time
        if (loading) {
          set((state) => ({
            analytics: {
              ...state.analytics,
              totalLoads: state.analytics.totalLoads + 1,
            },
          }));
        }
      },
      
      setProgress: (progress) => {
        set({ progress: Math.min(100, Math.max(0, progress)) });
      },
      
      updateThemeConfig: (theme, config) => {
        set((state) => ({
          themeConfigs: {
            ...state.themeConfigs,
            [theme]: {
              ...state.themeConfigs[theme],
              ...config,
            },
          },
        }));
      },
      
      updatePreferences: (preferences) => {
        set((state) => ({
          preferences: {
            ...state.preferences,
            ...preferences,
          },
        }));
      },
      
      recordLoadTime: (time) => {
        set((state) => {
          const newLoadingTimes = [...state.analytics.loadingTimes, time].slice(-100); // Keep last 100 records
          const averageLoadTime = newLoadingTimes.reduce((sum, time) => sum + time, 0) / newLoadingTimes.length;
          
          return {
            analytics: {
              ...state.analytics,
              loadingTimes: newLoadingTimes,
              averageLoadTime,
            },
          };
        });
      },
      
      getThemeConfig: (theme) => {
        const state = get();
        return state.themeConfigs[theme];
      },
      
      applyTheme: (theme) => {
        const state = get();
        const themeConfig = state.themeConfigs[theme];
        
        set({
          config: {
            ...state.config,
            ...themeConfig,
          },
        });
      },
    }),
    {
      name: 'top-loader-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        config: state.config,
        themeConfigs: state.themeConfigs,
        preferences: state.preferences,
        analytics: state.analytics,
      }),
    }
  )
);