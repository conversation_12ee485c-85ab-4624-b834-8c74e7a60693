import { create } from "zustand"
import { persist } from "zustand/middleware"

interface UIStore {
  // Theme
  theme: "light" | "dark" | "system"
  setTheme: (theme: "light" | "dark" | "system") => void

  // Sidebar
  activeItem: string
  setActiveItem: (item: string) => void
  isSidebarOpen: boolean
  toggleSidebar: () => void
  sidebarWidth: number
  setSidebarWidth: (width: number) => void
  isCollapsed: boolean
  toggleCollapse: () => void
  sidebarPinned: boolean
  toggleSidebarPin: () => void

  // Modal
  isModalOpen: boolean
  modalType: string | null
  modalData: any
  openModal: (type: string, data?: any) => void
  closeModal: () => void

  // Toast
  toasts: Array<{id: string, type: "success" | "error" | "info" | "warning", message: string}>
  addToast: (type: "success" | "error" | "info" | "warning", message: string) => void
  removeToast: (id: string) => void

  // Loading States
  isLoading: boolean
  loadingMessage: string
  setLoading: (loading: boolean, message?: string) => void

  // Viewport
  isMobile: boolean
  setIsMobile: (isMobile: boolean) => void
  
  // Search
  searchQuery: string
  setSearchQuery: (query: string) => void
  isSearchOpen: boolean
  toggleSearch: () => void

  // Navigation
  breadcrumbs: Array<{label: string, path: string}>
  setBreadcrumbs: (breadcrumbs: Array<{label: string, path: string}>) => void
}

export const useUIStore = create<UIStore>()(
  persist(
    (set) => ({
      // Theme
      theme: "system",
      setTheme: (theme) => set({ theme }),

      // Sidebar
      activeItem: "",
      setActiveItem: (item) => set({ activeItem: item }),
      isSidebarOpen: true,
      toggleSidebar: () => set((state) => ({ isSidebarOpen: !state.isSidebarOpen })),
      sidebarWidth: 240,
      setSidebarWidth: (width) => set({ sidebarWidth: width }),
      isCollapsed: false,
      toggleCollapse: () => set((state) => ({ isCollapsed: !state.isCollapsed })),
      sidebarPinned: false,
      toggleSidebarPin: () => set((state) => ({ sidebarPinned: !state.sidebarPinned })),

      // Modal
      isModalOpen: false,
      modalType: null,
      modalData: null,
      openModal: (type, data) => set({ isModalOpen: true, modalType: type, modalData: data }),
      closeModal: () => set({ isModalOpen: false, modalType: null, modalData: null }),

      // Toast
      toasts: [],
      addToast: (type, message) => set((state) => ({
        toasts: [...state.toasts, { id: Date.now().toString(), type, message }]
      })),
      removeToast: (id) => set((state) => ({
        toasts: state.toasts.filter((toast) => toast.id !== id)
      })),

      // Loading States
      isLoading: false,
      loadingMessage: "",
      setLoading: (loading, message = "") => set({ isLoading: loading, loadingMessage: message }),

      // Viewport
      isMobile: false,
      setIsMobile: (isMobile) => set({ isMobile }),

      // Search
      searchQuery: "",
      setSearchQuery: (query) => set({ searchQuery: query }),
      isSearchOpen: false,
      toggleSearch: () => set((state) => ({ isSearchOpen: !state.isSearchOpen })),

      // Navigation
      breadcrumbs: [],
      setBreadcrumbs: (breadcrumbs) => set({ breadcrumbs })
    }),
    {
      name: "ui-store",
      partialize: (state) => ({
        theme: state.theme,
        sidebarWidth: state.sidebarWidth,
        isCollapsed: state.isCollapsed,
        sidebarPinned: state.sidebarPinned
      })
    }
  )
)
