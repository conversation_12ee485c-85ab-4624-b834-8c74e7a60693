# NextJS TopLoader Setup with ShadCN Theme Integration

## Overview

This setup provides a comprehensive NextJS TopLoader implementation with advanced features including:

- **🎨 ShadCN Theme Integration**: Automatic color adaptation based on light/dark themes
- **🎛️ Advanced Configuration**: Extensive customization options
- **📊 Analytics**: Loading performance tracking
- **🎯 Zustand State Management**: Modular state management with persistence
- **⚡ Performance Optimizations**: Optimized animations and rendering
- **♿ Accessibility**: Reduced motion support and proper ARIA attributes

## Features

### 🎨 Theme Integration
- Automatic color adaptation for light/dark themes
- Custom CSS variables support
- ShadCN color palette integration
- Dynamic theme switching

### 🎛️ Advanced Configuration
- Height, speed, easing customization
- Spinner and animation controls
- Position settings (top/bottom)
- Shadow and visual effects
- Z-index management

### 📊 Analytics & Performance
- Loading time tracking
- Performance metrics
- Historical data storage
- Average load time calculation

### 🎯 State Management
- Zustand store with persistence
- Modular configuration
- Theme-specific settings
- User preferences

## Installation

The package is already installed in your project:

```bash
npm install nextjs-toploader # Already installed
```

## Usage

### Basic Implementation

The TopLoader is automatically included in your app through the providers:

```tsx
// app/providers.tsx
import TopLoader from "@/components/ui/top-loader";

export function Providers({ children }: ProvidersProps) {
  return (
    <SessionProvider>
      <UserProvider>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <TopLoaderProvider>
            <ToastProvider>
              <TopLoader
                height={3}
                showSpinner={false}
                easing="ease"
                speed={200}
                customTemplate={true}
              />
              {children}
            </ToastProvider>
          </TopLoaderProvider>
        </ThemeProvider>
      </UserProvider>
    </SessionProvider>
  );
}
```

### Advanced Usage with Zustand Store

```tsx
import { useTopLoaderStore } from "@/lib/stores/top-loader-store";

function MyComponent() {
  const {
    config,
    preferences,
    analytics,
    updateConfig,
    updatePreferences,
    setLoading,
    setProgress,
  } = useTopLoaderStore();

  // Update configuration
  const handleSpeedChange = (speed: number) => {
    updateConfig({ speed });
  };

  // Control loading state
  const simulateLoading = async () => {
    setLoading(true);
    for (let i = 0; i <= 100; i += 10) {
      setProgress(i);
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    setLoading(false);
  };

  return (
    <div>
      <button onClick={simulateLoading}>Test Loading</button>
      <p>Average Load Time: {analytics.averageLoadTime}ms</p>
    </div>
  );
}
```

### Custom Hook Usage

```tsx
import { useTopLoader } from "@/hooks/use-top-loader";

function MyComponent() {
  const {
    config,
    updateConfig,
    setLoading,
    setProgress,
    getThemeConfig,
  } = useTopLoader();

  const handleThemeChange = (theme: 'dark' | 'light') => {
    const themeConfig = getThemeConfig();
    updateConfig(themeConfig);
  };

  return (
    <div>
      <button onClick={() => handleThemeChange('dark')}>
        Apply Dark Theme
      </button>
    </div>
  );
}
```

## Configuration Options

### Core Configuration

```typescript
interface TopLoaderConfig {
  color: string;                    // Primary color
  height: number;                   // Height in pixels
  showSpinner: boolean;             // Show/hide spinner
  easing: string;                   // CSS easing function
  speed: number;                    // Animation speed in ms
  shadow: string;                   // CSS box-shadow
  zIndex: number;                   // Z-index value
  showAtBottom: boolean;            // Position at bottom
  template: string;                 // Custom HTML template
  initialPosition: number;          // Starting position (0-1)
  crawl: boolean;                   // Enable crawling animation
  crawlSpeed: number;               // Crawling speed in ms
}
```

### Theme Configuration

```typescript
interface ThemeConfig {
  light: {
    color: "oklch(0.208 0.042 265.755)";
    shadow: "0 0 10px oklch(0.208 0.042 265.755), 0 0 5px oklch(0.208 0.042 265.755)";
  };
  dark: {
    color: "oklch(0.488 0.243 264.376)";
    shadow: "0 0 10px oklch(0.488 0.243 264.376), 0 0 5px oklch(0.488 0.243 264.376)";
  };
}
```

### User Preferences

```typescript
interface Preferences {
  enableAnimations: boolean;        // Enable/disable animations
  showProgressText: boolean;        // Show progress percentage
  autoHideDelay: number;           // Auto hide delay in ms
  enableSounds: boolean;           // Enable loading sounds
}
```

## Components

### TopLoaderSettings

Comprehensive settings panel for configuration:

```tsx
import { TopLoaderSettings } from "@/components/top-loader-settings";

function SettingsPage() {
  return (
    <div>
      <TopLoaderSettings />
    </div>
  );
}
```

### TopLoaderDemo

Interactive demo component:

```tsx
import { TopLoaderDemo } from "@/components/top-loader-demo";

function DemoPage() {
  return (
    <div>
      <TopLoaderDemo />
    </div>
  );
}
```

## Presets

### Fast & Minimal
```typescript
{
  height: 2,
  speed: 100,
  showSpinner: false,
  easing: "ease-out"
}
```

### Smooth & Elegant
```typescript
{
  height: 4,
  speed: 300,
  showSpinner: true,
  easing: "ease-in-out"
}
```

### Bold & Dynamic
```typescript
{
  height: 6,
  speed: 150,
  showSpinner: true,
  easing: "ease"
}
```

## Styling

### CSS Variables Used

```css
:root {
  --color-primary: oklch(0.208 0.042 265.755);
  --color-accent: oklch(0.968 0.007 247.896);
  --color-foreground: oklch(0.129 0.042 264.695);
  --color-background: oklch(1 0 0);
}

.dark {
  --color-primary: oklch(0.488 0.243 264.376);
  --color-accent: oklch(0.279 0.041 260.031);
  --color-foreground: oklch(0.984 0.003 247.858);
  --color-background: oklch(0.129 0.042 264.695);
}
```

### Custom Animations

```css
@keyframes music-wave {
  0%, 100% { transform: scaleY(1); }
  50% { transform: scaleY(1.2); }
}

@keyframes pulse {
  0% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.05); }
  100% { opacity: 1; transform: scale(1); }
}
```

## Performance Optimizations

### Reduced Motion Support
```css
@media (prefers-reduced-motion: reduce) {
  #nprogress .bar,
  #nprogress .peg,
  #nprogress .spinner-icon {
    animation: none !important;
    transition: none !important;
  }
}
```

### Will-Change Optimization
```css
#nprogress .bar,
#nprogress .peg,
#nprogress .spinner-icon {
  will-change: transform, opacity;
}
```

## Analytics

### Tracked Metrics
- Total number of loads
- Individual loading times
- Average loading time
- Performance score calculation
- Historical data (last 100 loads)

### Performance Scoring
- Excellent: < 500ms
- Good: 500ms - 1000ms
- Average: 1000ms - 2000ms
- Needs Improvement: > 2000ms

## Best Practices

### 1. Theme Integration
- Use CSS variables for consistent theming
- Test in both light and dark modes
- Ensure proper contrast ratios

### 2. Performance
- Keep animations lightweight
- Use appropriate z-index values
- Implement reduced motion preferences

### 3. Accessibility
- Provide proper ARIA attributes
- Support keyboard navigation
- Respect user motion preferences

### 4. Configuration
- Start with presets and customize gradually
- Test loading times across different devices
- Monitor analytics for performance insights

## Troubleshooting

### Common Issues

1. **TopLoader not appearing**
   - Check if it's properly included in providers
   - Verify z-index is higher than other elements
   - Ensure the component is mounted

2. **Theme colors not updating**
   - Verify CSS variables are properly defined
   - Check if theme provider is wrapping the component
   - Ensure theme detection is working

3. **Performance issues**
   - Reduce animation complexity
   - Check for memory leaks in analytics
   - Optimize CSS animations

### Debug Mode

Enable debug logging:

```typescript
const { setLoading, setProgress } = useTopLoaderStore();

// Enable debug logging
setLoading(true);
console.log("TopLoader started");

setProgress(50);
console.log("TopLoader progress: 50%");
```

## Migration Guide

### From Basic NextJS TopLoader

```typescript
// Before
import NextTopLoader from 'nextjs-toploader';

function App() {
  return (
    <>
      <NextTopLoader color="#2299DD" />
      {children}
    </>
  );
}

// After
import TopLoader from "@/components/ui/top-loader";

function App() {
  return (
    <>
      <TopLoader
        height={3}
        showSpinner={false}
        customTemplate={true}
      />
      {children}
    </>
  );
}
```

## Contributing

When adding new features:

1. Update the Zustand store with new state
2. Add corresponding UI components
3. Update TypeScript interfaces
4. Add tests for new functionality
5. Update documentation

## License

This implementation follows the same license as your project.