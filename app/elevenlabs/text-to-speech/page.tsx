import { Metadata } from 'next';
import { TextToSpeech } from '@/components/elevenlabs/text-to-speech';

export const metadata: Metadata = {
  title: 'Text-to-Speech - ElevenLabs',
  description: 'Convert text to natural-sounding speech using ElevenLabs AI voices',
};

export default function TextToSpeechPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight">Text-to-Speech</h1>
          <p className="text-muted-foreground mt-2">
            Convert your text into natural-sounding speech with AI voices
          </p>
        </div>
        
        <TextToSpeech />
      </div>
    </div>
  );
}