import { Metadata } from 'next';
import { ConversationalAIDashboard } from '@/components/elevenlabs/conversational-ai-dashboard';

export const metadata: Metadata = {
  title: 'Conversational AI - ElevenLabs',
  description: 'Create and manage real-time voice AI agents with ElevenLabs Conversational AI',
};

export default function ConversationalAIPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight">Conversational AI</h1>
          <p className="text-muted-foreground mt-2">
            Create and deploy real-time voice AI agents with advanced turn-taking and natural conversation flow
          </p>
        </div>
        
        <ConversationalAIDashboard />
      </div>
    </div>
  );
}