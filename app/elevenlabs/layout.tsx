import { Metadata } from 'next';
import { ElevenLabsProvider } from '@/components/elevenlabs/elevenlabs-provider';
import { Suspense } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

export const metadata: Metadata = {
  title: {
    template: '%s | ElevenLabs',
    default: 'ElevenLabs - AI Voice Generation',
  },
  description: 'Generate natural-sounding speech with ElevenLabs AI voices',
};

function LoadingFallback() {
  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-md mx-auto">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 mx-auto mb-4 animate-spin" />
            <p className="text-muted-foreground">Loading ElevenLabs...</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function ElevenLabsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ElevenLabsProvider fallback={<LoadingFallback />}>
      <Suspense fallback={<LoadingFallback />}>
        <div className="min-h-screen bg-background">
          <main>{children}</main>
        </div>
      </Suspense>
    </ElevenLabsProvider>
  );
}