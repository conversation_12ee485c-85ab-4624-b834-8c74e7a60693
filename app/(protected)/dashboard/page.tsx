"use client"

import { ChartVariations } from '@/components/chart-variations'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { AIAssistant } from '@/components/ai-assistant'

export default function DashboardPage() {
  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
      </div>
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$45,231.89</div>
                <p className="text-xs text-muted-foreground">+20.1% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">+2350</div>
                <p className="text-xs text-muted-foreground">+180.1% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Audience</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">+12,234</div>
                <p className="text-xs text-muted-foreground">+19% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Engagement Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">+573</div>
                <p className="text-xs text-muted-foreground">+201 since last hour</p>
              </CardContent>
            </Card>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <ChartVariations
                endpoint="dashboard/overview"
                metrics={['revenue', 'projects', 'audience', 'engagement']}
                title="Overview"
                description="Your performance metrics over time"
              />
            </Card>
            <Card className="col-span-3 h-[500px]">
              <AIAssistant />
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
            <Card>
              <ChartVariations
                endpoint="dashboard/analytics/engagement"
                metrics={['likes', 'shares', 'comments', 'saves']}
                title="Engagement Metrics"
                description="User interaction with your content"
              />
            </Card>
            <Card>
              <ChartVariations
                endpoint="dashboard/analytics/growth"
                metrics={['followers', 'subscribers', 'reach', 'impressions']}
                title="Growth Metrics"
                description="Your audience growth and reach"
              />
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="reports" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
            <Card>
              <ChartVariations
                endpoint="dashboard/reports/performance"
                metrics={['conversion_rate', 'bounce_rate', 'retention_rate']}
                title="Performance Metrics"
                description="Key performance indicators"
              />
            </Card>
            <Card>
              <ChartVariations
                endpoint="dashboard/reports/revenue"
                metrics={['total_revenue', 'average_order_value', 'customer_lifetime_value']}
                title="Revenue Metrics"
                description="Financial performance indicators"
              />
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
