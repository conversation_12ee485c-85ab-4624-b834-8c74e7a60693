export interface PlatformStatus {
  platforms: {
    name: string;
    status: 'Active' | 'Pending' | 'Failed';
    lastSync: string;
    contentCount: number;
    releaseCount: number;
  }[];
  totalContent: number;
  totalReleases: number;
}

export interface DistributionSettings {
  defaultTerritories: string[];
  releaseDelay: number;
  pricing: {
    platform: string;
    price: number;
    currency: string;
  }[];
  formats: {
    format: string;
    enabled: boolean;
    quality: string;
  }[];
}

export interface ReleaseSchedule {
  upcoming: {
    releaseId: string;
    title: string;
    platform: string;
    scheduledDate: string;
    status: 'Scheduled' | 'Processing' | 'Completed';
  }[];
  recent: {
    releaseId: string;
    title: string;
    platform: string;
    releaseDate: string;
    status: 'Success' | 'Failed';
  }[];
}

export interface TerritoryRights {
  territories: {
    region: string;
    rights: string[];
    restrictions: string[];
  }[];
  globalRights: string[];
  restrictedTerritories: string[];
}

export interface ContentDelivery {
  status: {
    platform: string;
    delivered: number;
    pending: number;
    failed: number;
  }[];
  recentDeliveries: {
    contentId: string;
    platform: string;
    date: string;
    status: 'Success' | 'Failed';
    error?: string;
  }[];
}

export interface DistributionReports {
  revenue: {
    platform: string;
    amount: number;
    currency: string;
    period: string;
  }[];
  performance: {
    platform: string;
    streams: number;
    downloads: number;
    revenue: number;
  }[];
  trends: {
    date: string;
    totalRevenue: number;
    totalStreams: number;
    totalDownloads: number;
  }[];
} 