import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { DistributionSettings } from "../types";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";

interface DistributionSettingsCardProps {
  data: DistributionSettings;
}

export function DistributionSettingsCard({ data }: DistributionSettingsCardProps) {
  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>Distribution Settings</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Default Territories</h3>
            <div className="flex flex-wrap gap-2">
              {data.defaultTerritories.map((territory) => (
                <Badge key={territory} variant="secondary">
                  {territory}
                </Badge>
              ))}
            </div>
          </div>
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Release Delay</h3>
            <p className="text-2xl font-bold">{data.releaseDelay} days</p>
          </div>
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Platform Pricing</h3>
            <div className="grid gap-4">
              {data.pricing.map((price) => (
                <div key={price.platform} className="flex items-center justify-between">
                  <span className="font-medium">{price.platform}</span>
                  <span className="text-muted-foreground">
                    {price.currency} {price.price.toFixed(2)}
                  </span>
                </div>
              ))}
            </div>
          </div>
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Content Formats</h3>
            <div className="grid gap-4">
              {data.formats.map((format) => (
                <div key={format.format} className="flex items-center justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{format.format}</span>
                      <Switch checked={format.enabled} />
                    </div>
                    <p className="text-sm text-muted-foreground">Quality: {format.quality}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 