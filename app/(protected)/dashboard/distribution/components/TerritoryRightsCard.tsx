import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { TerritoryRights } from "../types";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";

interface TerritoryRightsCardProps {
  data: TerritoryRights;
}

export function TerritoryRightsCard({ data }: TerritoryRightsCardProps) {
  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>Territory Rights</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Global Rights</h3>
            <div className="flex flex-wrap gap-2">
              {data.globalRights.map((right) => (
                <Badge key={right} variant="secondary">
                  {right}
                </Badge>
              ))}
            </div>
          </div>
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Restricted Territories</h3>
            <div className="flex flex-wrap gap-2">
              {data.restrictedTerritories.map((territory) => (
                <Badge key={territory} variant="destructive">
                  {territory}
                </Badge>
              ))}
            </div>
          </div>
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Territory-Specific Rights</h3>
            <ScrollArea className="h-[300px] pr-4">
              <div className="space-y-4">
                {data.territories.map((territory) => (
                  <div key={territory.region} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{territory.region}</h4>
                      <Badge variant="outline">
                        {territory.rights.length} rights
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex flex-wrap gap-2">
                        {territory.rights.map((right) => (
                          <Badge key={right} variant="secondary">
                            {right}
                          </Badge>
                        ))}
                      </div>
                      {territory.restrictions.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {territory.restrictions.map((restriction) => (
                            <Badge key={restriction} variant="destructive">
                              {restriction}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 