import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { ReleaseSchedule } from "../types";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

interface ReleaseScheduleCardProps {
  data: ReleaseSchedule;
}

export function ReleaseScheduleCard({ data }: ReleaseScheduleCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Scheduled':
        return 'bg-blue-500';
      case 'Processing':
        return 'bg-yellow-500';
      case 'Completed':
        return 'bg-green-500';
      case 'Success':
        return 'bg-green-500';
      case 'Failed':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>Release Schedule</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="upcoming" className="space-y-4">
          <TabsList>
            <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
            <TabsTrigger value="recent">Recent</TabsTrigger>
          </TabsList>
          <TabsContent value="upcoming" className="space-y-4">
            {data.upcoming.map((release) => (
              <div key={release.releaseId} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{release.title}</span>
                      <Badge className={getStatusColor(release.status)}>
                        {release.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{release.platform}</p>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {new Date(release.scheduledDate).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}
          </TabsContent>
          <TabsContent value="recent" className="space-y-4">
            {data.recent.map((release) => (
              <div key={release.releaseId} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{release.title}</span>
                      <Badge className={getStatusColor(release.status)}>
                        {release.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{release.platform}</p>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {new Date(release.releaseDate).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
} 