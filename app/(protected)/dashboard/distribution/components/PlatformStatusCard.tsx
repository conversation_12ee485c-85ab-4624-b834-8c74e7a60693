import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { PlatformStatus } from "../types";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

interface PlatformStatusCardProps {
  data: PlatformStatus;
}

export function PlatformStatusCard({ data }: PlatformStatusCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-500';
      case 'Pending':
        return 'bg-yellow-500';
      case 'Failed':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>Platform Status</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Total Content</h3>
              <p className="text-2xl font-bold">{data.totalContent}</p>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Total Releases</h3>
              <p className="text-2xl font-bold">{data.totalReleases}</p>
            </div>
          </div>
          <div className="space-y-4">
            {data.platforms.map((platform) => (
              <div key={platform.name} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{platform.name}</span>
                    <Badge className={getStatusColor(platform.status)}>
                      {platform.status}
                    </Badge>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    Last sync: {new Date(platform.lastSync).toLocaleDateString()}
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Content</span>
                      <span>{platform.contentCount}</span>
                    </div>
                    <Progress value={(platform.contentCount / data.totalContent) * 100} />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Releases</span>
                      <span>{platform.releaseCount}</span>
                    </div>
                    <Progress value={(platform.releaseCount / data.totalReleases) * 100} />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 