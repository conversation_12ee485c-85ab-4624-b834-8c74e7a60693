import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { ContentDelivery } from "../types";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";

interface ContentDeliveryCardProps {
  data: ContentDelivery;
}

export function ContentDeliveryCard({ data }: ContentDeliveryCardProps) {
  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>Content Delivery</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Delivery Status by Platform</h3>
            {data.status.map((platform) => {
              const total = platform.delivered + platform.pending + platform.failed;
              return (
                <div key={platform.platform} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{platform.platform}</span>
                    <div className="flex gap-2">
                      <Badge className="bg-green-500">{platform.delivered} Delivered</Badge>
                      <Badge className="bg-yellow-500">{platform.pending} Pending</Badge>
                      <Badge className="bg-red-500">{platform.failed} Failed</Badge>
                    </div>
                  </div>
                  <Progress value={(platform.delivered / total) * 100} />
                </div>
              );
            })}
          </div>
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Recent Deliveries</h3>
            <ScrollArea className="h-[200px] pr-4">
              <div className="space-y-4">
                {data.recentDeliveries.map((delivery) => (
                  <div key={`${delivery.contentId}-${delivery.date}`} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">Content ID: {delivery.contentId}</span>
                          <Badge className={delivery.status === 'Success' ? 'bg-green-500' : 'bg-red-500'}>
                            {delivery.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{delivery.platform}</p>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {new Date(delivery.date).toLocaleDateString()}
                      </span>
                    </div>
                    {delivery.error && (
                      <p className="text-sm text-red-500">{delivery.error}</p>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 