import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { DistributionReports } from "../types";
import { BarChart, Bar, LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer, Legend } from "recharts";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";

interface DistributionReportsCardProps {
  data: DistributionReports;
}

export function DistributionReportsCard({ data }: DistributionReportsCardProps) {
  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>Distribution Reports</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="revenue" className="space-y-4">
          <TabsList>
            <TabsTrigger value="revenue">Revenue</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
          </TabsList>
          <TabsContent value="revenue" className="space-y-4">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={data.revenue}>
                  <XAxis dataKey="platform" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="amount" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>
            <div className="grid grid-cols-2 gap-4">
              {data.revenue.map((item) => (
                <div key={item.platform} className="space-y-1">
                  <p className="text-sm font-medium">{item.platform}</p>
                  <p className="text-2xl font-bold">
                    {item.currency} {item.amount.toLocaleString()}
                  </p>
                  <p className="text-sm text-muted-foreground">{item.period}</p>
                </div>
              ))}
            </div>
          </TabsContent>
          <TabsContent value="performance" className="space-y-4">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={data.performance}>
                  <XAxis dataKey="platform" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="streams" fill="#8884d8" name="Streams" />
                  <Bar dataKey="downloads" fill="#82ca9d" name="Downloads" />
                  <Bar dataKey="revenue" fill="#ffc658" name="Revenue" />
                </BarChart>
              </ResponsiveContainer>
            </div>
            <div className="grid grid-cols-2 gap-4">
              {data.performance.map((item) => (
                <div key={item.platform} className="space-y-1">
                  <p className="text-sm font-medium">{item.platform}</p>
                  <p className="text-sm">
                    Streams: {item.streams.toLocaleString()}
                  </p>
                  <p className="text-sm">
                    Downloads: {item.downloads.toLocaleString()}
                  </p>
                  <p className="text-sm">
                    Revenue: ${item.revenue.toLocaleString()}
                  </p>
                </div>
              ))}
            </div>
          </TabsContent>
          <TabsContent value="trends" className="space-y-4">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data.trends}>
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="totalRevenue"
                    stroke="#8884d8"
                    name="Revenue"
                  />
                  <Line
                    type="monotone"
                    dataKey="totalStreams"
                    stroke="#82ca9d"
                    name="Streams"
                  />
                  <Line
                    type="monotone"
                    dataKey="totalDownloads"
                    stroke="#ffc658"
                    name="Downloads"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium">Total Revenue</p>
                <p className="text-2xl font-bold">
                  ${data.trends.reduce((acc, curr) => acc + curr.totalRevenue, 0).toLocaleString()}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Total Streams</p>
                <p className="text-2xl font-bold">
                  {data.trends.reduce((acc, curr) => acc + curr.totalStreams, 0).toLocaleString()}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Total Downloads</p>
                <p className="text-2xl font-bold">
                  {data.trends.reduce((acc, curr) => acc + curr.totalDownloads, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
} 