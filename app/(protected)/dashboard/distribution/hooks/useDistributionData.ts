import { useState, useEffect } from 'react';
import {
  PlatformStatus,
  DistributionSettings,
  ReleaseSchedule,
  TerritoryRights,
  ContentDelivery,
  DistributionReports,
} from '../types';

interface DistributionData {
  platformStatus: PlatformStatus;
  settings: DistributionSettings;
  schedule: ReleaseSchedule;
  territoryRights: TerritoryRights;
  delivery: ContentDelivery;
  reports: DistributionReports;
}

export function useDistributionData() {
  const [data, setData] = useState<DistributionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch('/api/distribution');
        if (!response.ok) {
          throw new Error('Failed to fetch distribution data');
        }
        const distributionData = await response.json();
        setData(distributionData);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('An error occurred'));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
} 