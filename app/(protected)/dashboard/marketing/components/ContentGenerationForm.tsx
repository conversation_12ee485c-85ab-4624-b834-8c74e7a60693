import { useState } from 'react';
import { useAIGeneration } from '../hooks/useAIGeneration';
import { AIGenerationRequest } from '../types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { Loader2 } from 'lucide-react';

export function ContentGenerationForm() {
  const { generateContent, loading, error, response } = useAIGeneration();
  const [formData, setFormData] = useState<AIGenerationRequest>({
    type: 'social',
    platform: '',
    targetAudience: [],
    keywords: [],
    tone: 'professional',
    length: 'medium',
    style: 'informative',
    additionalContext: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await generateContent(formData);
  };

  const handleInputChange = (field: keyof AIGenerationRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleKeywordsChange = (value: string) => {
    const keywords = value.split(',').map(k => k.trim()).filter(Boolean);
    setFormData(prev => ({ ...prev, keywords }));
  };

  const handleAudienceChange = (value: string) => {
    const audience = value.split(',').map(a => a.trim()).filter(Boolean);
    setFormData(prev => ({ ...prev, targetAudience: audience }));
  };

  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>AI Content Generation</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <label className="text-sm font-medium">Content Type</label>
            <Select
              value={formData.type}
              onValueChange={(value) => handleInputChange('type', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select content type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="social">Social Media</SelectItem>
                <SelectItem value="email">Email</SelectItem>
                <SelectItem value="blog">Blog</SelectItem>
                <SelectItem value="ad">Advertisement</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Platform</label>
            <Input
              value={formData.platform}
              onChange={(e) => handleInputChange('platform', e.target.value)}
              placeholder="Enter platform (e.g., Instagram, Facebook, LinkedIn)"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Target Audience (comma-separated)</label>
            <Input
              value={formData.targetAudience.join(', ')}
              onChange={(e) => handleAudienceChange(e.target.value)}
              placeholder="Enter target audience (e.g., young professionals, students, parents)"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Keywords (comma-separated)</label>
            <Input
              value={formData.keywords.join(', ')}
              onChange={(e) => handleKeywordsChange(e.target.value)}
              placeholder="Enter keywords (e.g., music, entertainment, lifestyle)"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Tone</label>
            <Select
              value={formData.tone}
              onValueChange={(value) => handleInputChange('tone', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select tone" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="professional">Professional</SelectItem>
                <SelectItem value="casual">Casual</SelectItem>
                <SelectItem value="friendly">Friendly</SelectItem>
                <SelectItem value="formal">Formal</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Length</label>
            <Select
              value={formData.length}
              onValueChange={(value) => handleInputChange('length', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select length" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="short">Short</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="long">Long</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Style</label>
            <Select
              value={formData.style}
              onValueChange={(value) => handleInputChange('style', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select style" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="informative">Informative</SelectItem>
                <SelectItem value="persuasive">Persuasive</SelectItem>
                <SelectItem value="entertaining">Entertaining</SelectItem>
                <SelectItem value="educational">Educational</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Additional Context</label>
            <Textarea
              value={formData.additionalContext}
              onChange={(e) => handleInputChange('additionalContext', e.target.value)}
              placeholder="Enter any additional context or requirements"
              className="min-h-[100px]"
            />
          </div>

          <Button type="submit" disabled={loading} className="w-full">
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              'Generate Content'
            )}
          </Button>

          {error && (
            <div className="text-red-500 text-sm">{error.message}</div>
          )}

          {response && (
            <div className="space-y-4 mt-6">
              <div className="space-y-2">
                <h3 className="font-medium">Generated Content</h3>
                <div className="p-4 bg-muted rounded-lg">
                  {response.content}
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">AI Suggestions</h3>
                <div className="flex flex-wrap gap-2">
                  {response.suggestions.map((suggestion, index) => (
                    <Badge key={index} variant="secondary">
                      {suggestion}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Content Variations</h3>
                <ScrollArea className="h-[200px] pr-4">
                  <div className="space-y-4">
                    {response.variations.map((variation, index) => (
                      <div key={index} className="p-4 bg-muted rounded-lg">
                        {variation}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Content Metrics</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Readability</span>
                      <span>{response.metadata.readabilityScore}%</span>
                    </div>
                    <Progress value={response.metadata.readabilityScore} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Sentiment</span>
                      <span>{response.metadata.sentimentScore}%</span>
                    </div>
                    <Progress value={response.metadata.sentimentScore} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>SEO Score</span>
                      <span>{response.metadata.seoScore}%</span>
                    </div>
                    <Progress value={response.metadata.seoScore} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Estimated Engagement</span>
                      <span>{response.metadata.estimatedEngagement}</span>
                    </div>
                    <Progress value={(response.metadata.estimatedEngagement / 1500) * 100} />
                  </div>
                </div>
              </div>
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
} 