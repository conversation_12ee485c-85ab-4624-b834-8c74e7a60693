export interface CampaignContent {
  id: string;
  title: string;
  description: string;
  type: 'social' | 'email' | 'blog' | 'ad';
  platform: string;
  targetAudience: string[];
  keywords: string[];
  tone: string;
  status: 'draft' | 'review' | 'approved' | 'published';
  content: string;
  aiSuggestions: string[];
  metrics?: {
    engagement: number;
    reach: number;
    clicks: number;
    conversions: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Campaign {
  id: string;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  status: 'draft' | 'active' | 'paused' | 'completed';
  budget: number;
  targetAudience: string[];
  goals: string[];
  contents: CampaignContent[];
  metrics: {
    totalReach: number;
    totalEngagement: number;
    totalClicks: number;
    totalConversions: number;
    roi: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface AIGenerationRequest {
  type: 'social' | 'email' | 'blog' | 'ad';
  platform: string;
  targetAudience: string[];
  keywords: string[];
  tone: string;
  length: 'short' | 'medium' | 'long';
  style: string;
  additionalContext?: string;
}

export interface AIGenerationResponse {
  content: string;
  suggestions: string[];
  variations: string[];
  metadata: {
    estimatedEngagement: number;
    readabilityScore: number;
    sentimentScore: number;
    seoScore: number;
  };
}

export interface MarketingMetrics {
  campaigns: {
    total: number;
    active: number;
    completed: number;
    draft: number;
  };
  content: {
    total: number;
    published: number;
    draft: number;
    review: number;
  };
  performance: {
    averageEngagement: number;
    averageReach: number;
    averageClicks: number;
    averageConversions: number;
  };
  trends: {
    date: string;
    engagement: number;
    reach: number;
    clicks: number;
    conversions: number;
  }[];
} 