import { useState } from 'react';
import { AIGenerationRequest, AIGenerationResponse } from '../types';

export function useAIGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [response, setResponse] = useState<AIGenerationResponse | null>(null);

  const generateContent = async (request: AIGenerationRequest) => {
    try {
      setLoading(true);
      setError(null);
      setResponse(null);

      const response = await fetch('/api/marketing/ai/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error('Failed to generate content');
      }

      const data = await response.json();
      setResponse(data);
      return data;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An error occurred');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    generateContent,
    loading,
    error,
    response,
  };
} 