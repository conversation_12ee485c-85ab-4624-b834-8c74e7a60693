"use client"

import { Suspense } from 'react'
import { EnhancedChart } from '@/components/enhanced-chart'
import { useChartData } from '@/hooks/use-chart-data'
import { useChartTheme } from '@/hooks/use-chart-theme'
import { useChartCalculations } from '@/hooks/use-chart-calculations'
import { useChartFilters } from '@/hooks/use-chart-filters'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Skeleton } from '@/components/ui/skeleton'
import { MarketingAI } from '@/components/marketing-ai'

export default function MarketingDashboard() {
  const { theme, style } = useChartTheme()

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Marketing Analytics</h2>
      </div>
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="content">Content Performance</TabsTrigger>
          <TabsTrigger value="audience">Audience Insights</TabsTrigger>
          <TabsTrigger value="ai-assistant">AI Assistant</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Suspense fallback={<OverviewSkeleton />}>
            <OverviewTab />
          </Suspense>
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-4">
          <Suspense fallback={<CampaignsSkeleton />}>
            <CampaignsTab />
          </Suspense>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <Suspense fallback={<ContentSkeleton />}>
            <ContentTab />
          </Suspense>
        </TabsContent>

        <TabsContent value="audience" className="space-y-4">
          <Suspense fallback={<AudienceSkeleton />}>
            <AudienceTab />
          </Suspense>
        </TabsContent>

        <TabsContent value="ai-assistant" className="space-y-4">
          <MarketingAI />
        </TabsContent>
      </Tabs>
    </div>
  )
}

function OverviewTab() {
  const { data, metrics, loading, error, timeRange, handleTimeRangeChange } = useChartData({
    endpoint: 'chart-data',
    initialTimeRange: '30d',
  })

  const { filteredData } = useChartFilters({ data })

  const { aggregatedData } = useChartCalculations({
    data: filteredData,
    aggregations: [
      { field: 'engagement', type: 'average' },
      { field: 'conversion', type: 'average' },
      { field: 'retention', type: 'average' },
    ],
  })

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <EnhancedChart
        data={filteredData}
        title="Campaign Performance"
        description="Overall campaign metrics over time"
        type="area"
        timeRange={timeRange}
        onTimeRangeChange={handleTimeRangeChange}
        metrics={metrics}
      />

      <EnhancedChart
        data={filteredData}
        title="Audience Growth"
        description="Audience size and growth rate"
        type="line"
        timeRange={timeRange}
        onTimeRangeChange={handleTimeRangeChange}
      />

      <EnhancedChart
        data={filteredData}
        title="Content Distribution"
        description="Content performance by platform"
        type="pie"
      />

      <EnhancedChart
        data={filteredData}
        title="Performance Metrics"
        description="Key performance indicators"
        type="radar"
      />
    </div>
  )
}

function CampaignsTab() {
  const { data, metrics, loading, error, timeRange, handleTimeRangeChange } = useChartData({
    endpoint: 'chart-data/campaigns',
    initialTimeRange: '30d',
  })

  const { filteredData } = useChartFilters({ data })

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <EnhancedChart
        data={filteredData}
        title="Campaign ROI"
        description="Return on investment by campaign"
        type="bar"
        timeRange={timeRange}
        onTimeRangeChange={handleTimeRangeChange}
        metrics={metrics}
      />

      <EnhancedChart
        data={filteredData}
        title="Campaign Engagement"
        description="Engagement metrics by campaign"
        type="line"
        timeRange={timeRange}
        onTimeRangeChange={handleTimeRangeChange}
      />

      <EnhancedChart
        data={filteredData}
        title="Budget Allocation"
        description="Budget distribution across campaigns"
        type="pie"
      />
    </div>
  )
}

function ContentTab() {
  const { data, metrics, loading, error, timeRange, handleTimeRangeChange } = useChartData({
    endpoint: 'chart-data/content',
    initialTimeRange: '30d',
  })

  const { filteredData } = useChartFilters({ data })

  const { calculatedData } = useChartCalculations({
    data: filteredData,
    calculations: [
      { field: 'engagement', type: 'growth', timeframe: 'month' },
      { field: 'shares', type: 'growth', timeframe: 'month' },
      { field: 'comments', type: 'growth', timeframe: 'month' },
    ],
  })

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <EnhancedChart
        data={filteredData}
        title="Content Performance"
        description="Performance metrics by content type"
        type="bar"
        timeRange={timeRange}
        onTimeRangeChange={handleTimeRangeChange}
        metrics={metrics}
      />

      <EnhancedChart
        data={filteredData}
        title="Engagement Trends"
        description="Engagement metrics over time"
        type="line"
        timeRange={timeRange}
        onTimeRangeChange={handleTimeRangeChange}
      />

      <EnhancedChart
        data={filteredData}
        title="Content Distribution"
        description="Distribution across platforms"
        type="pie"
      />
    </div>
  )
}

function AudienceTab() {
  const { data, metrics, loading, error, timeRange, handleTimeRangeChange } = useChartData({
    endpoint: 'chart-data/audience',
    initialTimeRange: '30d',
  })

  const { filteredData } = useChartFilters({ data })

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <EnhancedChart
        data={filteredData}
        title="Audience Demographics"
        description="Demographic breakdown of audience"
        type="pie"
      />

      <EnhancedChart
        data={filteredData}
        title="Engagement by Age Group"
        description="Engagement metrics by age group"
        type="bar"
        timeRange={timeRange}
        onTimeRangeChange={handleTimeRangeChange}
      />

      <EnhancedChart
        data={filteredData}
        title="Geographic Distribution"
        description="Audience distribution by region"
        type="pie"
      />
    </div>
  )
}

function OverviewSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {Array(4).fill(null).map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <Skeleton className="h-4 w-[250px]" />
            <Skeleton className="h-4 w-[200px]" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[300px]" />
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

function CampaignsSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {Array(3).fill(null).map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <Skeleton className="h-4 w-[250px]" />
            <Skeleton className="h-4 w-[200px]" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[300px]" />
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

function ContentSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {Array(3).fill(null).map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <Skeleton className="h-4 w-[250px]" />
            <Skeleton className="h-4 w-[200px]" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[300px]" />
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

function AudienceSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {Array(3).fill(null).map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <Skeleton className="h-4 w-[250px]" />
            <Skeleton className="h-4 w-[200px]" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[300px]" />
          </CardContent>
        </Card>
      ))}
    </div>
  )
} 