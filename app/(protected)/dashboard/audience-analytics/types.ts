export interface DemographicsData {
  ageGroups: {
    range: string;
    count: number;
    percentage: number;
  }[];
  gender: {
    male: number;
    female: number;
    other: number;
  };
  totalListeners: number;
}

export interface ListeningHabits {
  averageDailyListenTime: number;
  peakListeningHours: {
    hour: number;
    count: number;
  }[];
  favoriteGenres: {
    genre: string;
    percentage: number;
  }[];
  deviceTypes: {
    device: string;
    percentage: number;
  }[];
}

export interface GeographicData {
  countries: {
    country: string;
    listeners: number;
    percentage: number;
  }[];
  topCities: {
    city: string;
    country: string;
    listeners: number;
  }[];
}

export interface EngagementMetrics {
  averageSessionDuration: number;
  retentionRate: number;
  skipRate: number;
  completionRate: number;
  interactionRate: number;
}

export interface GrowthTrends {
  daily: {
    date: string;
    newListeners: number;
    activeListeners: number;
  }[];
  weekly: {
    week: string;
    newListeners: number;
    activeListeners: number;
  }[];
  monthly: {
    month: string;
    newListeners: number;
    activeListeners: number;
  }[];
}

export interface PlatformDistribution {
  platforms: {
    platform: string;
    users: number;
    percentage: number;
  }[];
  deviceTypes: {
    device: string;
    users: number;
    percentage: number;
  }[];
} 