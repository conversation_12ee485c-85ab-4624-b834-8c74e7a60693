"use client";

import { ChartVariations } from '@/components/chart-variations'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { AIInsightsDashboard } from '@/components/ai-insights-dashboard'

export default function AudienceAnalyticsPage() {
  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Audience Analytics</h2>
      </div>
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="demographics">Demographics</TabsTrigger>
          <TabsTrigger value="behavior">Behavior</TabsTrigger>
          <TabsTrigger value="engagement">Engagement</TabsTrigger>
          <TabsTrigger value="ai-insights">AI Insights</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Listeners</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2.7M</div>
                <p className="text-xs text-muted-foreground">+12.3% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1.2M</div>
                <p className="text-xs text-muted-foreground">+8.1% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg. Listen Time</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">45.2m</div>
                <p className="text-xs text-muted-foreground">+5.2% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Retention Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">76.8%</div>
                <p className="text-xs text-muted-foreground">+2.4% from last month</p>
              </CardContent>
            </Card>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
            <Card>
              <ChartVariations
                endpoint="audience/growth"
                metrics={['total_listeners', 'active_users', 'new_users', 'returning_users']}
                title="Listener Growth"
                description="Track your audience growth over time"
              />
            </Card>
            <Card>
              <ChartVariations
                endpoint="audience/genres"
                metrics={['rock', 'pop', 'jazz', 'classical', 'electronic']}
                title="Top Genres"
                description="Most popular music genres among your audience"
              />
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="demographics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
            <Card>
              <ChartVariations
                endpoint="audience/age"
                metrics={['13-17', '18-24', '25-34', '35-44', '45-54', '55+']}
                title="Age Distribution"
                description="Age breakdown of your audience"
              />
            </Card>
            <Card>
              <ChartVariations
                endpoint="audience/geography"
                metrics={['north_america', 'europe', 'asia', 'south_america', 'africa', 'oceania']}
                title="Geographic Distribution"
                description="Where your listeners are located"
              />
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="behavior" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
            <Card>
              <ChartVariations
                endpoint="audience/listening"
                metrics={['morning', 'afternoon', 'evening', 'night']}
                title="Listening Patterns"
                description="When your audience is most active"
              />
            </Card>
            <Card>
              <ChartVariations
                endpoint="audience/platform"
                metrics={['mobile', 'desktop', 'tablet', 'smart_speaker', 'car_system']}
                title="Platform Usage"
                description="How your audience accesses your content"
              />
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="engagement" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
            <Card>
              <ChartVariations
                endpoint="audience/engagement"
                metrics={['likes', 'shares', 'comments', 'playlists_added']}
                title="Engagement Metrics"
                description="How your audience interacts with your content"
              />
            </Card>
            <Card>
              <ChartVariations
                endpoint="audience/interaction"
                metrics={['skip_rate', 'completion_rate', 'repeat_rate', 'follow_rate']}
                title="Interaction Analysis"
                description="Detailed breakdown of listener behavior"
              />
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="ai-insights" className="space-y-4">
          <AIInsightsDashboard />
        </TabsContent>
      </Tabs>
    </div>
  )
} 