import { useState, useEffect } from 'react';
import {
  DemographicsData,
  ListeningHabits,
  GeographicData,
  EngagementMetrics,
  GrowthTrends,
  PlatformDistribution,
} from '../types';

interface AnalyticsData {
  demographics: DemographicsData;
  listeningHabits: ListeningHabits;
  geographic: GeographicData;
  engagement: EngagementMetrics;
  growth: GrowthTrends;
  platforms: PlatformDistribution;
}

export function useAnalyticsData() {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // TODO: Replace with actual API call
        const response = await fetch('/api/analytics');
        if (!response.ok) {
          throw new Error('Failed to fetch analytics data');
        }
        const analyticsData = await response.json();
        setData(analyticsData);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('An error occurred'));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
}

export function useDateRangeFilter() {
  const [dateRange, setDateRange] = useState<'daily' | 'weekly' | 'monthly'>('daily');

  const updateDateRange = (range: 'daily' | 'weekly' | 'monthly') => {
    setDateRange(range);
  };

  return { dateRange, updateDateRange };
} 