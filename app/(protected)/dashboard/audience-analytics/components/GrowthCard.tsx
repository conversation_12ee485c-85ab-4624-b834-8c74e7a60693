import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { GrowthTrends } from "../types";
import { LineChart, Line, XAxis, <PERSON>A<PERSON>s, <PERSON>lt<PERSON>, ResponsiveContainer, Legend } from "recharts";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useState } from "react";

interface GrowthCardProps {
  data: GrowthTrends;
}

export function GrowthCard({ data }: GrowthCardProps) {
  const [timeRange, setTimeRange] = useState<'daily' | 'weekly' | 'monthly'>('daily');

  const getChartData = () => {
    switch (timeRange) {
      case 'daily':
        return data.daily;
      case 'weekly':
        return data.weekly;
      case 'monthly':
        return data.monthly;
      default:
        return data.daily;
    }
  };

  const chartData = getChartData();

  return (
    <Card className="col-span-1">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Growth Trends</CardTitle>
        <Select value={timeRange} onValueChange={(value: 'daily' | 'weekly' | 'monthly') => setTimeRange(value)}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="daily">Daily</SelectItem>
            <SelectItem value="weekly">Weekly</SelectItem>
            <SelectItem value="monthly">Monthly</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData}>
              <XAxis dataKey={timeRange === 'daily' ? 'date' : timeRange} />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="newListeners"
                stroke="#8884d8"
                name="New Listeners"
              />
              <Line
                type="monotone"
                dataKey="activeListeners"
                stroke="#82ca9d"
                name="Active Listeners"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
} 