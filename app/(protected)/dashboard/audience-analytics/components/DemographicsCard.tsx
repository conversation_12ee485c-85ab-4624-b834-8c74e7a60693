import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { DemographicsData } from "../types";
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, <PERSON>A<PERSON>s, Tooltip, ResponsiveContainer } from "recharts";

interface DemographicsCardProps {
  data: DemographicsData;
}

export function DemographicsCard({ data }: DemographicsCardProps) {
  const ageData = data.ageGroups.map(group => ({
    name: group.range,
    value: group.count,
    percentage: group.percentage
  }));

  const genderData = [
    { name: 'Male', value: data.gender.male },
    { name: 'Female', value: data.gender.female },
    { name: 'Other', value: data.gender.other }
  ];

  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>Demographics</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h3 className="text-sm font-medium">Age Distribution</h3>
            <div className="h-[200px] mt-2">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={ageData}>
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
          <div>
            <h3 className="text-sm font-medium">Gender Distribution</h3>
            <div className="h-[200px] mt-2">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={genderData}>
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
          <div className="text-sm text-muted-foreground">
            Total Listeners: {data.totalListeners.toLocaleString()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 