import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { ListeningHabits } from "../types";
import { Line<PERSON>hart, Line, <PERSON>C<PERSON>, Pie, Cell, XAxis, YA<PERSON>s, Tooltip, ResponsiveContainer } from "recharts";

interface ListeningHabitsCardProps {
  data: ListeningHabits;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

export function ListeningHabitsCard({ data }: ListeningHabitsCardProps) {
  const peakHoursData = data.peakListeningHours.map(hour => ({
    hour: `${hour.hour}:00`,
    listeners: hour.count
  }));

  const genreData = data.favoriteGenres.map(genre => ({
    name: genre.genre,
    value: genre.percentage
  }));

  const deviceData = data.deviceTypes.map(device => ({
    name: device.device,
    value: device.percentage
  }));

  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>Listening Habits</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h3 className="text-sm font-medium">Peak Listening Hours</h3>
            <div className="h-[200px] mt-2">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={peakHoursData}>
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="listeners" stroke="#8884d8" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium">Favorite Genres</h3>
              <div className="h-[200px] mt-2">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={genreData}
                      dataKey="value"
                      nameKey="name"
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      label
                    >
                      {genreData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium">Device Types</h3>
              <div className="h-[200px] mt-2">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={deviceData}
                      dataKey="value"
                      nameKey="name"
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      label
                    >
                      {deviceData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
          <div className="text-sm text-muted-foreground">
            Average Daily Listen Time: {Math.round(data.averageDailyListenTime)} minutes
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 