import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { PlatformDistribution } from "../types";
import { <PERSON><PERSON><PERSON>, Pie, Cell, Tooltip, ResponsiveContainer } from "recharts";

interface PlatformCardProps {
  data: PlatformDistribution;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export function PlatformCard({ data }: PlatformCardProps) {
  const platformData = data.platforms.map(platform => ({
    name: platform.platform,
    value: platform.users,
    percentage: platform.percentage
  }));

  const deviceData = data.deviceTypes.map(device => ({
    name: device.device,
    value: device.users,
    percentage: device.percentage
  }));

  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>Platform Distribution</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm font-medium mb-4">Platforms</h3>
            <div className="h-[200px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={platformData}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    label
                  >
                    {platformData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
          <div>
            <h3 className="text-sm font-medium mb-4">Device Types</h3>
            <div className="h-[200px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={deviceData}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    label
                  >
                    {deviceData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 