import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { EngagementMetrics } from "../types";
import { Progress } from "@/components/ui/progress";

interface EngagementCardProps {
  data: EngagementMetrics;
}

export function EngagementCard({ data }: EngagementCardProps) {
  const metrics = [
    {
      name: "Retention Rate",
      value: data.retentionRate,
      color: "bg-green-500"
    },
    {
      name: "Completion Rate",
      value: data.completionRate,
      color: "bg-blue-500"
    },
    {
      name: "Interaction Rate",
      value: data.interactionRate,
      color: "bg-purple-500"
    },
    {
      name: "Skip Rate",
      value: data.skipRate,
      color: "bg-red-500"
    }
  ];

  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>Engagement Metrics</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Average Session Duration</h3>
              <p className="text-2xl font-bold">
                {Math.round(data.averageSessionDuration)} minutes
              </p>
            </div>
          </div>
          <div className="space-y-4">
            {metrics.map((metric) => (
              <div key={metric.name} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="font-medium">{metric.name}</span>
                  <span className="text-muted-foreground">
                    {metric.value.toFixed(1)}%
                  </span>
                </div>
                <Progress value={metric.value} className={metric.color} />
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 