"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { 
  IconBell, 
  IconCheck, 
  IconX, 
  IconMusic, 
  IconTrendingUp, 
  IconUsers,
  IconSettings,
  IconTrash
} from "@tabler/icons-react"
import { useSession } from "next-auth/react"

// Mock notification data
const mockNotifications = [
  {
    id: 1,
    type: 'release',
    title: 'Track Upload Complete',
    message: 'Your track "Summer Vibes" has been successfully uploaded and is now live.',
    timestamp: '2 hours ago',
    read: false,
    icon: IconMusic
  },
  {
    id: 2,
    type: 'analytics',
    title: 'Weekly Analytics Report',
    message: 'Your tracks gained 1,234 new streams this week. Check out your detailed analytics.',
    timestamp: '1 day ago',
    read: true,
    icon: IconTrendingUp
  },
  {
    id: 3,
    type: 'collaboration',
    title: 'New Collaboration Request',
    message: '<PERSON> wants to collaborate on a new track. Review their proposal.',
    timestamp: '3 days ago',
    read: false,
    icon: IconUsers
  },
  {
    id: 4,
    type: 'system',
    title: 'Account Settings Updated',
    message: 'Your notification preferences have been successfully updated.',
    timestamp: '1 week ago',
    read: true,
    icon: IconSettings
  }
]

export default function NotificationsPage() {
  const { data: session } = useSession()
  const [notifications, setNotifications] = useState(mockNotifications)
  const [filter, setFilter] = useState<'all' | 'unread'>('all')

  const unreadCount = notifications.filter(n => !n.read).length
  const filteredNotifications = filter === 'unread' 
    ? notifications.filter(n => !n.read)
    : notifications

  const markAsRead = (id: number) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(n => ({ ...n, read: true }))
    )
  }

  const deleteNotification = (id: number) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'release': return 'bg-blue-500'
      case 'analytics': return 'bg-green-500'
      case 'collaboration': return 'bg-purple-500'
      case 'system': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Notifications</h1>
          <p className="text-muted-foreground">
            Stay updated with your latest activity and updates.
          </p>
        </div>
        <div className="flex items-center gap-2">
          {unreadCount > 0 && (
            <Button variant="outline" size="sm" onClick={markAllAsRead}>
              <IconCheck className="h-4 w-4 mr-2" />
              Mark All Read
            </Button>
          )}
          <Badge variant="secondary">
            {unreadCount} unread
          </Badge>
        </div>
      </div>

      <Tabs value={filter} onValueChange={(value) => setFilter(value as 'all' | 'unread')}>
        <TabsList>
          <TabsTrigger value="all">All Notifications</TabsTrigger>
          <TabsTrigger value="unread">
            Unread {unreadCount > 0 && `(${unreadCount})`}
          </TabsTrigger>
        </TabsList>

        <TabsContent value={filter} className="space-y-4">
          {filteredNotifications.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <IconBell className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    {filter === 'unread' ? 'No unread notifications' : 'No notifications yet'}
                  </p>
                  <p className="text-sm text-muted-foreground mt-2">
                    {filter === 'unread' 
                      ? 'All caught up! Check back later for new updates.'
                      : 'Notifications will appear here when you have activity on your account.'
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-3">
              {filteredNotifications.map((notification) => {
                const IconComponent = notification.icon
                return (
                  <Card 
                    key={notification.id} 
                    className={`transition-colors ${!notification.read ? 'bg-muted/50' : ''}`}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-4">
                        <div className={`p-2 rounded-full ${getTypeColor(notification.type)}`}>
                          <IconComponent className="h-4 w-4 text-white" />
                        </div>
                        
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold">{notification.title}</h3>
                            {!notification.read && (
                              <div className="h-2 w-2 bg-blue-500 rounded-full" />
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {notification.message}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {notification.timestamp}
                          </p>
                        </div>
                        
                        <div className="flex items-center gap-1">
                          {!notification.read && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => markAsRead(notification.id)}
                            >
                              <IconCheck className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteNotification(notification.id)}
                          >
                            <IconTrash className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Notification Settings Quick Access */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconSettings className="h-5 w-5" />
            Notification Settings
          </CardTitle>
          <CardDescription>
            Manage your notification preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Email Notifications</p>
              <p className="text-sm text-muted-foreground">
                Get notified about important updates via email
              </p>
            </div>
            <Button variant="outline" size="sm" asChild>
              <a href="/dashboard/settings?tab=notifications">
                Configure
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}