"use client"

import { AppSidebar } from "@/components/app-sidebar"
import { SiteHeader } from "@/components/site-header"
import { AuthGuard } from "@/components/auth-guard"
import {
  Sidebar,
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar"
import { useUIStore } from "@/lib/stores/ui-store"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { isSidebarOpen, toggleSidebar } = useUIStore()

  return (
    <SidebarProvider defaultOpen={isSidebarOpen} onOpenChange={toggleSidebar}>
      <div className="relative flex min-h-screen w-full">
        <AppSidebar />
        <div className="flex w-full flex-col">
          <SiteHeader />
          <SidebarInset className="flex-1">
            <div className="container mx-auto p-6">
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                {children}
              </div>
            </div>
          </SidebarInset>
        </div>
      </div>
    </SidebarProvider>
  )
}
