import { notFound } from "next/navigation"
import Link from "next/link"
import { ContentDetails } from "@/components/content/content-details"
import { ContentRelated } from "@/components/content/content-related"
import { ContentAnalytics } from "@/components/content/content-analytics"
import { Button } from "@/components/ui/button"
import { ChevronLeft } from "lucide-react"

interface ContentDetailPageProps {
  params: {
    id: string
  }
}

export default async function ContentDetailPage({
  params,
}: ContentDetailPageProps) {
  const { id } = params
  
  // This would be replaced with actual data fetching
  // const content = await getContentById(id)
  
  // For now, we'll use dummy data
  const content = {
    id,
    title: "Sample Content Item",
    type: "audio",
    createdAt: new Date().toISOString(),
    status: "published"
  }
  
  if (!content) {
    notFound()
  }

  return (
    <div className="space-y-8">
      <div>
        <Link href="/dashboard/content-hub">
          <Button variant="ghost" size="sm" className="mb-4">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Content Hub
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">{content.title}</h1>
        <p className="text-muted-foreground">
          {content.type} • {new Date(content.createdAt).toLocaleDateString()} • {content.status}
        </p>
      </div>
      
      <ContentDetails content={content} />
      
      <div className="grid gap-6 md:grid-cols-2">
        <ContentAnalytics contentId={content.id} />
        <ContentRelated contentId={content.id} />
      </div>
    </div>
  )
} 