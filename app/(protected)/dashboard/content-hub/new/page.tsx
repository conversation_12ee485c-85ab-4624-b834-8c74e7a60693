import Link from "next/link"
import { ContentUploadForm } from "@/components/content/content-upload-form"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft } from "lucide-react"

export default function NewContentPage() {
  return (
    <div className="space-y-6">
      <Link href="/dashboard/content-hub">
        <Button variant="ghost" size="sm">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Content Hub
        </Button>
      </Link>
      
      <div>
        <h1 className="text-2xl font-bold">Upload New Content</h1>
        <p className="text-muted-foreground">
          Add new music, videos, artwork, or promotional materials to your content library.
        </p>
      </div>
      
      <ContentUploadForm />
    </div>
  )
} 