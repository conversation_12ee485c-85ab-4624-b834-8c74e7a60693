"use client"

import { ContentGrid } from "@/components/content/content-grid"
import { ContentFilters } from "@/components/content/content-filters"
import { ContentActions } from "@/components/content/content-actions"
import { useContent } from "@/hooks/use-content"

export default function ContentHubPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Content Hub</h1>
        <ContentActions />
      </div>
      
      <p className="text-muted-foreground">
        Manage all your music releases, videos, artwork, and promotional content in one place.
      </p>
      
      <ContentFilters />
      
      <ContentGrid />
    </div>
  )
} 