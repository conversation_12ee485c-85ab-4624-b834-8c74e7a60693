"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { IconCreditCard, IconDownload, IconCheck } from "@tabler/icons-react"
import { useSession } from "next-auth/react"

export default function BillingPage() {
  const { data: session } = useSession()

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Billing & Subscription</h1>
        <p className="text-muted-foreground">
          Manage your subscription and billing information.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Current Plan */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <IconCreditCard className="h-5 w-5" />
              Current Plan
            </CardTitle>
            <CardDescription>
              Your current subscription details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">Free Plan</h3>
                <p className="text-sm text-muted-foreground">
                  Basic features for getting started
                </p>
              </div>
              <Badge variant="secondary">Active</Badge>
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <IconCheck className="h-4 w-4 text-green-500" />
                Up to 5 track uploads per month
              </div>
              <div className="flex items-center gap-2 text-sm">
                <IconCheck className="h-4 w-4 text-green-500" />
                Basic analytics
              </div>
              <div className="flex items-center gap-2 text-sm">
                <IconCheck className="h-4 w-4 text-green-500" />
                Community support
              </div>
            </div>
            
            <Button className="w-full">
              Upgrade to Pro
            </Button>
          </CardContent>
        </Card>

        {/* Billing Information */}
        <Card>
          <CardHeader>
            <CardTitle>Billing Information</CardTitle>
            <CardDescription>
              Your payment method and billing details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center py-8">
              <IconCreditCard className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">
                No payment method on file
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                Add a payment method to upgrade your plan
              </p>
            </div>
            
            <Button variant="outline" className="w-full">
              Add Payment Method
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Usage Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Usage This Month</CardTitle>
          <CardDescription>
            Track your usage against plan limits
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center p-4 bg-muted rounded-lg">
              <p className="text-2xl font-bold text-primary">0/5</p>
              <p className="text-sm text-muted-foreground">Tracks Uploaded</p>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <p className="text-2xl font-bold text-primary">0</p>
              <p className="text-sm text-muted-foreground">Total Streams</p>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <p className="text-2xl font-bold text-primary">0 GB</p>
              <p className="text-sm text-muted-foreground">Storage Used</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Billing History */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Billing History</CardTitle>
              <CardDescription>
                Download your invoices and payment history
              </CardDescription>
            </div>
            <Button variant="outline" size="sm">
              <IconDownload className="h-4 w-4 mr-2" />
              Download All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              No billing history available
            </p>
            <p className="text-sm text-muted-foreground mt-2">
              Your invoices will appear here once you upgrade to a paid plan
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Available Plans */}
      <Card>
        <CardHeader>
          <CardTitle>Available Plans</CardTitle>
          <CardDescription>
            Choose the plan that works best for you
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="border rounded-lg p-6">
              <div className="text-center mb-4">
                <h3 className="text-xl font-bold">Pro Plan</h3>
                <p className="text-3xl font-bold mt-2">$19<span className="text-sm font-normal">/month</span></p>
              </div>
              <div className="space-y-2 mb-6">
                <div className="flex items-center gap-2 text-sm">
                  <IconCheck className="h-4 w-4 text-green-500" />
                  Unlimited track uploads
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <IconCheck className="h-4 w-4 text-green-500" />
                  Advanced analytics
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <IconCheck className="h-4 w-4 text-green-500" />
                  AI-powered insights
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <IconCheck className="h-4 w-4 text-green-500" />
                  Priority support
                </div>
              </div>
              <Button className="w-full">
                Upgrade to Pro
              </Button>
            </div>

            <div className="border rounded-lg p-6">
              <div className="text-center mb-4">
                <h3 className="text-xl font-bold">Enterprise</h3>
                <p className="text-3xl font-bold mt-2">$99<span className="text-sm font-normal">/month</span></p>
              </div>
              <div className="space-y-2 mb-6">
                <div className="flex items-center gap-2 text-sm">
                  <IconCheck className="h-4 w-4 text-green-500" />
                  Everything in Pro
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <IconCheck className="h-4 w-4 text-green-500" />
                  Team collaboration
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <IconCheck className="h-4 w-4 text-green-500" />
                  Custom integrations
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <IconCheck className="h-4 w-4 text-green-500" />
                  Dedicated support
                </div>
              </div>
              <Button variant="outline" className="w-full">
                Contact Sales
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}