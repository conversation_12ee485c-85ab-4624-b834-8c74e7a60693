'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Music, 
  Video, 
  Wand2, 
  Play, 
  Download, 
  Share2, 
  Sparkles,
  Mic,
  Camera,
  Palette,
  Settings,
  Clock,
  Users,
  TrendingUp
} from 'lucide-react';
import { toast } from 'sonner';
import { allTools } from '@/lib/ai-tools';

interface ProjectState {
  songConcept?: any;
  marketTrends?: any;
  marketingContent?: any;
  releasePlan?: any;
  musicVideo?: any;
  promoVideo?: any;
  audioVisualizer?: any;
}

export default function MusicVideoStudioPage() {
  const [activeTab, setActiveTab] = useState('concept');
  const [project, setProject] = useState<ProjectState>({});
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);

  // Song Concept Form
  const [conceptForm, setConceptForm] = useState({
    genre: '',
    mood: '',
    theme: '',
    duration: 'medium',
  });

  // Music Video Form
  const [videoForm, setVideoForm] = useState({
    videoStyle: 'cinematic',
    visualThemes: ['nature', 'abstract'],
    includeTextOverlays: true,
    includeLyrics: false,
    lyrics: '',
  });

  // Promotional Video Form
  const [promoForm, setPromoForm] = useState({
    trackName: '',
    artist: '',
    releaseDate: '',
    promoType: 'teaser',
    duration: 30,
    socialPlatform: 'instagram',
  });

  // Audio Visualizer Form
  const [visualizerForm, setVisualizerForm] = useState({
    visualizerType: 'spectrum',
    colorScheme: 'rainbow',
    backgroundType: 'gradient',
    sensitivity: 1.0,
  });

  const handleGenerateConcept = async () => {
    if (!conceptForm.genre || !conceptForm.mood) {
      toast.error('Please fill in genre and mood');
      return;
    }

    setIsGenerating(true);
    setProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const result = await allTools.generateSongConcept.execute({
        genre: conceptForm.genre,
        mood: conceptForm.mood,
        theme: conceptForm.theme,
        duration: conceptForm.duration as any,
      });

      clearInterval(progressInterval);
      setProgress(100);

      setProject(prev => ({ ...prev, songConcept: result }));
      toast.success('Song concept generated successfully!');
      setActiveTab('video');
    } catch (error) {
      toast.error('Failed to generate song concept');
      console.error(error);
    } finally {
      setIsGenerating(false);
      setProgress(0);
    }
  };

  const handleGenerateMusicVideo = async () => {
    if (!project.songConcept) {
      toast.error('Please generate a song concept first');
      return;
    }

    setIsGenerating(true);
    setProgress(0);

    try {
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 5, 90));
      }, 500);

      const result = await allTools.createMusicVideo.execute({
        songConcept: {
          genre: project.songConcept.genre,
          mood: project.songConcept.mood,
          theme: project.songConcept.theme,
          duration: 180, // 3 minutes
          bpm: project.songConcept.suggestedBPM,
        },
        videoStyle: videoForm.videoStyle as any,
        visualThemes: videoForm.visualThemes,
        includeTextOverlays: videoForm.includeTextOverlays,
        includeLyrics: videoForm.includeLyrics,
        lyrics: videoForm.lyrics,
      });

      clearInterval(progressInterval);
      setProgress(100);

      setProject(prev => ({ ...prev, musicVideo: result }));
      toast.success('Music video generated successfully!');
      setActiveTab('promo');
    } catch (error) {
      toast.error('Failed to generate music video');
      console.error(error);
    } finally {
      setIsGenerating(false);
      setProgress(0);
    }
  };

  const handleGeneratePromoVideo = async () => {
    if (!promoForm.trackName || !promoForm.artist) {
      toast.error('Please fill in track name and artist');
      return;
    }

    setIsGenerating(true);
    setProgress(0);

    try {
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 15, 90));
      }, 300);

      const result = await allTools.createPromotionalVideo.execute({
        releaseInfo: {
          trackName: promoForm.trackName,
          artist: promoForm.artist,
          releaseDate: promoForm.releaseDate,
          genre: project.songConcept?.genre || 'Pop',
        },
        promoType: promoForm.promoType as any,
        duration: promoForm.duration,
        includeCallToAction: true,
        socialPlatform: promoForm.socialPlatform as any,
      });

      clearInterval(progressInterval);
      setProgress(100);

      setProject(prev => ({ ...prev, promoVideo: result }));
      toast.success('Promotional video generated successfully!');
      setActiveTab('visualizer');
    } catch (error) {
      toast.error('Failed to generate promotional video');
      console.error(error);
    } finally {
      setIsGenerating(false);
      setProgress(0);
    }
  };

  const handleGenerateVisualizer = async () => {
    setIsGenerating(true);
    setProgress(0);

    try {
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 12, 90));
      }, 400);

      const result = await allTools.createAudioVisualizer.execute({
        audioFile: 'demo-audio.mp3', // In real app, this would be uploaded
        visualizerType: visualizerForm.visualizerType as any,
        colorScheme: visualizerForm.colorScheme as any,
        backgroundType: visualizerForm.backgroundType as any,
        sensitivity: visualizerForm.sensitivity,
      });

      clearInterval(progressInterval);
      setProgress(100);

      setProject(prev => ({ ...prev, audioVisualizer: result }));
      toast.success('Audio visualizer generated successfully!');
      setActiveTab('results');
    } catch (error) {
      toast.error('Failed to generate audio visualizer');
      console.error(error);
    } finally {
      setIsGenerating(false);
      setProgress(0);
    }
  };

  const handleAnalyzeMarket = async () => {
    if (!project.songConcept) {
      toast.error('Please generate a song concept first');
      return;
    }

    try {
      const result = await allTools.analyzeMarketTrends.execute({
        genre: project.songConcept.genre,
        timeframe: 'current',
        platform: 'all',
      });

      setProject(prev => ({ ...prev, marketTrends: result }));
      toast.success('Market analysis completed!');
    } catch (error) {
      toast.error('Failed to analyze market trends');
      console.error(error);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Music Video Studio</h1>
        <p className="text-muted-foreground">
          Create complete music video projects with AI-powered tools
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="concept" className="flex items-center gap-2">
            <Music className="h-4 w-4" />
            Concept
          </TabsTrigger>
          <TabsTrigger value="video" className="flex items-center gap-2">
            <Video className="h-4 w-4" />
            Music Video
          </TabsTrigger>
          <TabsTrigger value="promo" className="flex items-center gap-2">
            <Camera className="h-4 w-4" />
            Promo
          </TabsTrigger>
          <TabsTrigger value="visualizer" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Visualizer
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            Results
          </TabsTrigger>
        </TabsList>

        {/* Song Concept Tab */}
        <TabsContent value="concept" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Music className="h-5 w-5" />
                  Song Concept Generator
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="genre">Genre</Label>
                  <Input
                    id="genre"
                    placeholder="e.g., Pop, Hip-Hop, Rock"
                    value={conceptForm.genre}
                    onChange={(e) => setConceptForm(prev => ({ ...prev, genre: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="mood">Mood</Label>
                  <Input
                    id="mood"
                    placeholder="e.g., Energetic, Melancholic, Uplifting"
                    value={conceptForm.mood}
                    onChange={(e) => setConceptForm(prev => ({ ...prev, mood: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="theme">Theme (Optional)</Label>
                  <Input
                    id="theme"
                    placeholder="e.g., Love, Freedom, Adventure"
                    value={conceptForm.theme}
                    onChange={(e) => setConceptForm(prev => ({ ...prev, theme: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="duration">Duration</Label>
                  <Select value={conceptForm.duration} onValueChange={(value) => setConceptForm(prev => ({ ...prev, duration: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="short">Short (2-3 min)</SelectItem>
                      <SelectItem value="medium">Medium (3-4 min)</SelectItem>
                      <SelectItem value="long">Long (4-6 min)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button 
                  onClick={handleGenerateConcept}
                  disabled={isGenerating}
                  className="w-full"
                >
                  <Wand2 className="h-4 w-4 mr-2" />
                  {isGenerating ? 'Generating...' : 'Generate Concept'}
                </Button>

                {isGenerating && (
                  <Progress value={progress} className="w-full" />
                )}
              </CardContent>
            </Card>

            {project.songConcept && (
              <Card>
                <CardHeader>
                  <CardTitle>Generated Concept</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="secondary">{project.songConcept.genre}</Badge>
                    <Badge variant="secondary">{project.songConcept.mood}</Badge>
                    <Badge variant="outline">{project.songConcept.suggestedBPM} BPM</Badge>
                    <Badge variant="outline">{project.songConcept.keySignature}</Badge>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">Theme</h4>
                    <p className="text-sm text-muted-foreground">{project.songConcept.theme}</p>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">Structure</h4>
                    <div className="flex flex-wrap gap-1">
                      {project.songConcept.structure.map((section: string, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {section}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">Instruments</h4>
                    <div className="flex flex-wrap gap-1">
                      {project.songConcept.instruments.map((instrument: string, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {instrument}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <Button onClick={handleAnalyzeMarket} variant="outline" className="w-full">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    Analyze Market Trends
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* Music Video Tab */}
        <TabsContent value="video" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Video className="h-5 w-5" />
                  Music Video Generator
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="videoStyle">Video Style</Label>
                  <Select value={videoForm.videoStyle} onValueChange={(value) => setVideoForm(prev => ({ ...prev, videoStyle: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="realistic">Realistic</SelectItem>
                      <SelectItem value="animated">Animated</SelectItem>
                      <SelectItem value="cinematic">Cinematic</SelectItem>
                      <SelectItem value="artistic">Artistic</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="visualThemes">Visual Themes</Label>
                  <Input
                    id="visualThemes"
                    placeholder="e.g., nature, city, abstract (comma separated)"
                    value={videoForm.visualThemes.join(', ')}
                    onChange={(e) => setVideoForm(prev => ({ 
                      ...prev, 
                      visualThemes: e.target.value.split(',').map(t => t.trim()) 
                    }))}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="textOverlays"
                    checked={videoForm.includeTextOverlays}
                    onChange={(e) => setVideoForm(prev => ({ ...prev, includeTextOverlays: e.target.checked }))}
                  />
                  <Label htmlFor="textOverlays">Include Text Overlays</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="includeLyrics"
                    checked={videoForm.includeLyrics}
                    onChange={(e) => setVideoForm(prev => ({ ...prev, includeLyrics: e.target.checked }))}
                  />
                  <Label htmlFor="includeLyrics">Include Lyrics</Label>
                </div>

                {videoForm.includeLyrics && (
                  <div>
                    <Label htmlFor="lyrics">Lyrics</Label>
                    <Textarea
                      id="lyrics"
                      placeholder="Enter song lyrics..."
                      value={videoForm.lyrics}
                      onChange={(e) => setVideoForm(prev => ({ ...prev, lyrics: e.target.value }))}
                      rows={4}
                    />
                  </div>
                )}

                <Button 
                  onClick={handleGenerateMusicVideo}
                  disabled={isGenerating || !project.songConcept}
                  className="w-full"
                >
                  <Video className="h-4 w-4 mr-2" />
                  {isGenerating ? 'Generating Video...' : 'Generate Music Video'}
                </Button>

                {isGenerating && (
                  <Progress value={progress} className="w-full" />
                )}
              </CardContent>
            </Card>

            {project.musicVideo && (
              <Card>
                <CardHeader>
                  <CardTitle>Music Video Generated</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <Video className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground">Video Preview</p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-semibold">{project.musicVideo.title}</p>
                      <p className="text-sm text-muted-foreground">
                        {project.musicVideo.segments} segments • {project.musicVideo.style}
                      </p>
                    </div>
                    <Badge variant="secondary">
                      <Clock className="h-3 w-3 mr-1" />
                      {Math.floor(project.musicVideo.duration / 60)}:{(project.musicVideo.duration % 60).toString().padStart(2, '0')}
                    </Badge>
                  </div>

                  <div className="flex gap-2">
                    <Button size="sm" className="flex-1">
                      <Play className="h-4 w-4 mr-2" />
                      Preview
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1">
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* Promotional Video Tab */}
        <TabsContent value="promo" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Camera className="h-5 w-5" />
                  Promotional Video
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="trackName">Track Name</Label>
                  <Input
                    id="trackName"
                    placeholder="Enter track name"
                    value={promoForm.trackName}
                    onChange={(e) => setPromoForm(prev => ({ ...prev, trackName: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="artist">Artist</Label>
                  <Input
                    id="artist"
                    placeholder="Enter artist name"
                    value={promoForm.artist}
                    onChange={(e) => setPromoForm(prev => ({ ...prev, artist: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="releaseDate">Release Date</Label>
                  <Input
                    id="releaseDate"
                    type="date"
                    value={promoForm.releaseDate}
                    onChange={(e) => setPromoForm(prev => ({ ...prev, releaseDate: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="promoType">Promo Type</Label>
                  <Select value={promoForm.promoType} onValueChange={(value) => setPromoForm(prev => ({ ...prev, promoType: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="teaser">Teaser</SelectItem>
                      <SelectItem value="announcement">Announcement</SelectItem>
                      <SelectItem value="behind-scenes">Behind the Scenes</SelectItem>
                      <SelectItem value="lyric-video">Lyric Video</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="socialPlatform">Platform</Label>
                  <Select value={promoForm.socialPlatform} onValueChange={(value) => setPromoForm(prev => ({ ...prev, socialPlatform: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="instagram">Instagram</SelectItem>
                      <SelectItem value="tiktok">TikTok</SelectItem>
                      <SelectItem value="youtube">YouTube</SelectItem>
                      <SelectItem value="twitter">Twitter</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button 
                  onClick={handleGeneratePromoVideo}
                  disabled={isGenerating}
                  className="w-full"
                >
                  <Camera className="h-4 w-4 mr-2" />
                  {isGenerating ? 'Generating...' : 'Generate Promo Video'}
                </Button>

                {isGenerating && (
                  <Progress value={progress} className="w-full" />
                )}
              </CardContent>
            </Card>

            {project.promoVideo && (
              <Card>
                <CardHeader>
                  <CardTitle>Promotional Video</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <Camera className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground">Promo Preview</p>
                    </div>
                  </div>

                  <div>
                    <p className="font-semibold">{project.promoVideo.type} Video</p>
                    <p className="text-sm text-muted-foreground">
                      {project.promoVideo.platform} • {project.promoVideo.duration}s
                    </p>
                  </div>

                  <div className="flex gap-2">
                    <Button size="sm" className="flex-1">
                      <Play className="h-4 w-4 mr-2" />
                      Preview
                    </Button>
                    <Button size="sm" variant="outline">
                      <Share2 className="h-4 w-4 mr-2" />
                      Share
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* Audio Visualizer Tab */}
        <TabsContent value="visualizer" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  Audio Visualizer
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="visualizerType">Visualizer Type</Label>
                  <Select value={visualizerForm.visualizerType} onValueChange={(value) => setVisualizerForm(prev => ({ ...prev, visualizerType: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="waveform">Waveform</SelectItem>
                      <SelectItem value="spectrum">Spectrum</SelectItem>
                      <SelectItem value="circular">Circular</SelectItem>
                      <SelectItem value="bars">Bars</SelectItem>
                      <SelectItem value="particles">Particles</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="colorScheme">Color Scheme</Label>
                  <Select value={visualizerForm.colorScheme} onValueChange={(value) => setVisualizerForm(prev => ({ ...prev, colorScheme: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="rainbow">Rainbow</SelectItem>
                      <SelectItem value="monochrome">Monochrome</SelectItem>
                      <SelectItem value="warm">Warm</SelectItem>
                      <SelectItem value="cool">Cool</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="backgroundType">Background</Label>
                  <Select value={visualizerForm.backgroundType} onValueChange={(value) => setVisualizerForm(prev => ({ ...prev, backgroundType: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="solid">Solid Color</SelectItem>
                      <SelectItem value="gradient">Gradient</SelectItem>
                      <SelectItem value="image">Image</SelectItem>
                      <SelectItem value="video">Video</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="sensitivity">Sensitivity: {visualizerForm.sensitivity}</Label>
                  <input
                    type="range"
                    id="sensitivity"
                    min="0.1"
                    max="2.0"
                    step="0.1"
                    value={visualizerForm.sensitivity}
                    onChange={(e) => setVisualizerForm(prev => ({ ...prev, sensitivity: parseFloat(e.target.value) }))}
                    className="w-full"
                  />
                </div>

                <Button 
                  onClick={handleGenerateVisualizer}
                  disabled={isGenerating}
                  className="w-full"
                >
                  <Palette className="h-4 w-4 mr-2" />
                  {isGenerating ? 'Generating...' : 'Generate Visualizer'}
                </Button>

                {isGenerating && (
                  <Progress value={progress} className="w-full" />
                )}
              </CardContent>
            </Card>

            {project.audioVisualizer && (
              <Card>
                <CardHeader>
                  <CardTitle>Audio Visualizer</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="aspect-video bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <div className="text-center text-white">
                      <Palette className="h-12 w-12 mx-auto mb-2" />
                      <p className="text-sm">Visualizer Preview</p>
                    </div>
                  </div>

                  <div>
                    <p className="font-semibold">{project.audioVisualizer.type} Visualizer</p>
                    <p className="text-sm text-muted-foreground">
                      {project.audioVisualizer.colorScheme} • {Math.floor(project.audioVisualizer.duration / 60)}:{(project.audioVisualizer.duration % 60).toString().padStart(2, '0')}
                    </p>
                  </div>

                  <div className="flex gap-2">
                    <Button size="sm" className="flex-1">
                      <Play className="h-4 w-4 mr-2" />
                      Preview
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* Results Tab */}
        <TabsContent value="results" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {project.songConcept && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Music className="h-5 w-5" />
                    Song Concept
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Badge variant="secondary">{project.songConcept.genre}</Badge>
                    <p className="text-sm">{project.songConcept.theme}</p>
                    <Button size="sm" variant="outline" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      Export Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {project.musicVideo && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Video className="h-5 w-5" />
                    Music Video
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Badge variant="secondary">{project.musicVideo.style}</Badge>
                    <p className="text-sm">{project.musicVideo.segments} segments</p>
                    <Button size="sm" variant="outline" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      Download Video
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {project.promoVideo && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Camera className="h-5 w-5" />
                    Promo Video
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Badge variant="secondary">{project.promoVideo.platform}</Badge>
                    <p className="text-sm">{project.promoVideo.type}</p>
                    <Button size="sm" variant="outline" className="w-full">
                      <Share2 className="h-4 w-4 mr-2" />
                      Share
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {project.audioVisualizer && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5" />
                    Visualizer
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Badge variant="secondary">{project.audioVisualizer.type}</Badge>
                    <p className="text-sm">{project.audioVisualizer.colorScheme}</p>
                    <Button size="sm" variant="outline" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {project.marketTrends && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Market Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Badge variant="secondary">
                      {project.marketTrends.insights.popularity}% Popular
                    </Badge>
                    <p className="text-sm">{project.marketTrends.insights.growthRate} growth</p>
                    <Button size="sm" variant="outline" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      Full Report
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {Object.keys(project).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Project Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Total Assets Generated</span>
                    <Badge variant="outline">{Object.keys(project).length}</Badge>
                  </div>
                  
                  <Separator />
                  
                  <div className="flex gap-2">
                    <Button className="flex-1">
                      <Download className="h-4 w-4 mr-2" />
                      Download All
                    </Button>
                    <Button variant="outline" className="flex-1">
                      <Share2 className="h-4 w-4 mr-2" />
                      Share Project
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}