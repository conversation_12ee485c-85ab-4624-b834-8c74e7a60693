'use client';

import React, { useCallback } from 'react';
import { 
  VideoStudioLayout
} from '@/components/ai-video-generator';
import { 
  GeneratedVideo
} from '@/lib/video/types/video-types';
import { toast } from 'sonner';

export default function AIVideoGeneratorPage() {
  const handleVideoGenerated = useCallback((video: GeneratedVideo) => {
    toast.success(`Video generated: ${video.prompt.slice(0, 50)}...`);
  }, []);

  const handleVideoExported = useCallback((url: string) => {
    toast.success('Video exported successfully!');
  }, []);

  return (
    <VideoStudioLayout
      onVideoGenerated={handleVideoGenerated}
      onVideoExported={handleVideoExported}
    />
  );
}
