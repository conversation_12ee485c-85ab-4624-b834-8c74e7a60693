'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Mic, 
  Play, 
  Pause,
  Download, 
  Upload,
  Wand2,
  Volume2,
  Settings,
  User,
  Clock,
  Languages
} from 'lucide-react';
import { toast } from 'sonner';

export default function VoiceStudioPage() {
  // TTS State
  const [ttsText, setTtsText] = useState('');
  const [ttsVoice, setTtsVoice] = useState('alloy');
  const [ttsProvider, setTtsProvider] = useState<'openai' | 'elevenlabs'>('openai');
  const [ttsLanguage, setTtsLanguage] = useState('en-US');
  const [ttsSpeed, setTtsSpeed] = useState([1.0]);
  const [ttsPitch, setTtsPitch] = useState([0]);
  const [ttsVolume, setTtsVolume] = useState([1.0]);
  const [isGeneratingTTS, setIsGeneratingTTS] = useState(false);
  const [ttsProgress, setTtsProgress] = useState(0);
  const [generatedTTS, setGeneratedTTS] = useState<any>(null);

  // Voice Cloning State
  const [cloneText, setCloneText] = useState('');
  const [cloneName, setCloneName] = useState('');
  const [cloneDescription, setCloneDescription] = useState('');
  const [isGeneratingClone, setIsGeneratingClone] = useState(false);
  const [cloneProgress, setCloneProgress] = useState(0);
  const [voiceClones, setVoiceClones] = useState<any[]>([]);

  // Playback State
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentAudio, setCurrentAudio] = useState<string | null>(null);

  const handleGenerateTTS = async () => {
    if (!ttsText.trim()) {
      toast.error('Please enter text to convert to speech');
      return;
    }

    setIsGeneratingTTS(true);
    setTtsProgress(0);

    // Simulate generation progress
    const progressInterval = setInterval(() => {
      setTtsProgress(prev => {
        if (prev >= 95) {
          clearInterval(progressInterval);
          return 95;
        }
        return prev + Math.random() * 20;
      });
    }, 200);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      clearInterval(progressInterval);
      setTtsProgress(100);

      // Mock generated TTS
      setGeneratedTTS({
        id: `tts-${Date.now()}`,
        text: ttsText,
        voice: ttsVoice,
        provider: ttsProvider,
        language: ttsLanguage,
        speed: ttsSpeed[0],
        pitch: ttsPitch[0],
        volume: ttsVolume[0],
        duration: Math.ceil(ttsText.length / 10), // Mock duration
        url: 'mock-tts-audio.mp3',
        createdAt: new Date().toISOString(),
      });

      toast.success('Text-to-speech generated successfully!');
    } catch (error) {
      toast.error('Failed to generate speech');
      console.error(error);
    } finally {
      setIsGeneratingTTS(false);
      setTimeout(() => setTtsProgress(0), 1000);
    }
  };

  const handleCreateVoiceClone = async () => {
    if (!cloneText.trim() || !cloneName.trim()) {
      toast.error('Please provide text and name for voice cloning');
      return;
    }

    setIsGeneratingClone(true);
    setCloneProgress(0);

    // Simulate cloning progress
    const progressInterval = setInterval(() => {
      setCloneProgress(prev => {
        if (prev >= 95) {
          clearInterval(progressInterval);
          return 95;
        }
        return prev + Math.random() * 10;
      });
    }, 500);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 8000));
      
      clearInterval(progressInterval);
      setCloneProgress(100);

      // Mock voice clone
      const newClone = {
        id: `clone-${Date.now()}`,
        name: cloneName,
        description: cloneDescription,
        sampleText: cloneText,
        createdAt: new Date().toISOString(),
        status: 'ready',
      };

      setVoiceClones(prev => [...prev, newClone]);
      setCloneName('');
      setCloneDescription('');
      setCloneText('');

      toast.success('Voice clone created successfully!');
    } catch (error) {
      toast.error('Failed to create voice clone');
      console.error(error);
    } finally {
      setIsGeneratingClone(false);
      setTimeout(() => setCloneProgress(0), 1000);
    }
  };

  const openAIVoices = [
    { value: 'alloy', label: 'Alloy (Neutral)' },
    { value: 'echo', label: 'Echo (Male)' },
    { value: 'fable', label: 'Fable (British Male)' },
    { value: 'onyx', label: 'Onyx (Deep Male)' },
    { value: 'nova', label: 'Nova (Female)' },
    { value: 'shimmer', label: 'Shimmer (Soft Female)' },
  ];

  const elevenLabsVoices = [
    { value: 'rachel', label: 'Rachel (Calm Female)' },
    { value: 'domi', label: 'Domi (Strong Female)' },
    { value: 'bella', label: 'Bella (Soft Female)' },
    { value: 'antoni', label: 'Antoni (Well-rounded Male)' },
    { value: 'elli', label: 'Elli (Emotional Female)' },
    { value: 'josh', label: 'Josh (Deep Male)' },
  ];

  const availableVoices = ttsProvider === 'openai' ? openAIVoices : elevenLabsVoices;

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Voice Studio</h1>
        <p className="text-muted-foreground">
          Generate speech from text and create custom voice clones
        </p>
      </div>

      <Tabs defaultValue="tts" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="tts" className="flex items-center gap-2">
            <Volume2 className="h-4 w-4" />
            Text-to-Speech
          </TabsTrigger>
          <TabsTrigger value="cloning" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Voice Cloning
          </TabsTrigger>
        </TabsList>

        {/* Text-to-Speech Tab */}
        <TabsContent value="tts" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* TTS Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mic className="h-5 w-5" />
                  Text-to-Speech Generator
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="ttsText">Text to Convert</Label>
                  <Textarea
                    id="ttsText"
                    placeholder="Enter the text you want to convert to speech..."
                    value={ttsText}
                    onChange={(e) => setTtsText(e.target.value)}
                    rows={4}
                    className="mt-2"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {ttsText.length} characters • ~{Math.ceil(ttsText.length / 10)} seconds
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="ttsProvider">Provider</Label>
                    <Select value={ttsProvider} onValueChange={(value: any) => setTtsProvider(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="openai">OpenAI</SelectItem>
                        <SelectItem value="elevenlabs">ElevenLabs</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="ttsLanguage">Language</Label>
                    <Select value={ttsLanguage} onValueChange={setTtsLanguage}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="en-US">English (US)</SelectItem>
                        <SelectItem value="en-GB">English (UK)</SelectItem>
                        <SelectItem value="es-ES">Spanish</SelectItem>
                        <SelectItem value="fr-FR">French</SelectItem>
                        <SelectItem value="de-DE">German</SelectItem>
                        <SelectItem value="it-IT">Italian</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="ttsVoice">Voice</Label>
                  <Select value={ttsVoice} onValueChange={setTtsVoice}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {availableVoices.map((voice) => (
                        <SelectItem key={voice.value} value={voice.value}>
                          {voice.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label>Speed: {ttsSpeed[0]}</Label>
                    <Slider
                      value={ttsSpeed}
                      onValueChange={setTtsSpeed}
                      max={2}
                      min={0.25}
                      step={0.25}
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label>Pitch: {ttsPitch[0]}</Label>
                    <Slider
                      value={ttsPitch}
                      onValueChange={setTtsPitch}
                      max={20}
                      min={-20}
                      step={1}
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label>Volume: {ttsVolume[0]}</Label>
                    <Slider
                      value={ttsVolume}
                      onValueChange={setTtsVolume}
                      max={1}
                      min={0}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>
                </div>

                <Button 
                  onClick={handleGenerateTTS}
                  disabled={isGeneratingTTS || !ttsText.trim()}
                  className="w-full"
                  size="lg"
                >
                  <Volume2 className="h-4 w-4 mr-2" />
                  {isGeneratingTTS ? 'Generating...' : 'Generate Speech'}
                </Button>

                {isGeneratingTTS && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Generating speech...</span>
                      <span>{Math.round(ttsProgress)}%</span>
                    </div>
                    <Progress value={ttsProgress} className="w-full" />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* TTS Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Play className="h-5 w-5" />
                  Generated Speech
                </CardTitle>
              </CardHeader>
              <CardContent>
                {generatedTTS ? (
                  <div className="space-y-4">
                    <div className="p-4 bg-muted rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-semibold">Generated Audio</h3>
                        <div className="flex gap-2">
                          <Badge variant="secondary">{generatedTTS.provider}</Badge>
                          <Badge variant="outline">
                            <Clock className="h-3 w-3 mr-1" />
                            {generatedTTS.duration}s
                          </Badge>
                        </div>
                      </div>

                      <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
                        {generatedTTS.text}
                      </p>

                      <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                        <div>
                          <span className="text-muted-foreground">Voice:</span>
                          <p className="font-medium">{generatedTTS.voice}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Language:</span>
                          <p className="font-medium">{generatedTTS.language}</p>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button 
                          size="sm" 
                          className="flex-1"
                          onClick={() => setIsPlaying(!isPlaying)}
                        >
                          {isPlaying ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
                          {isPlaying ? 'Pause' : 'Play'}
                        </Button>
                        <Button size="sm" variant="outline" className="flex-1">
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <Volume2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="font-semibold mb-2">No speech generated yet</h3>
                      <p className="text-sm text-muted-foreground">
                        Enter text and click generate to create speech
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Voice Cloning Tab */}
        <TabsContent value="cloning" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Voice Cloning Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wand2 className="h-5 w-5" />
                  Create Voice Clone
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="cloneName">Voice Name</Label>
                  <Input
                    id="cloneName"
                    placeholder="Enter a name for this voice"
                    value={cloneName}
                    onChange={(e) => setCloneName(e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="cloneDescription">Description (Optional)</Label>
                  <Input
                    id="cloneDescription"
                    placeholder="Describe this voice..."
                    value={cloneDescription}
                    onChange={(e) => setCloneDescription(e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="cloneText">Sample Text</Label>
                  <Textarea
                    id="cloneText"
                    placeholder="Provide sample text that represents how you want this voice to sound..."
                    value={cloneText}
                    onChange={(e) => setCloneText(e.target.value)}
                    rows={4}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Longer samples (100+ words) produce better voice clones
                  </p>
                </div>

                <div className="p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>Note:</strong> Voice cloning requires ElevenLabs Pro subscription. 
                    The quality depends on the sample text provided.
                  </p>
                </div>

                <Button 
                  onClick={handleCreateVoiceClone}
                  disabled={isGeneratingClone || !cloneName.trim() || !cloneText.trim()}
                  className="w-full"
                  size="lg"
                >
                  <Wand2 className="h-4 w-4 mr-2" />
                  {isGeneratingClone ? 'Creating Clone...' : 'Create Voice Clone'}
                </Button>

                {isGeneratingClone && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Creating voice clone...</span>
                      <span>{Math.round(cloneProgress)}%</span>
                    </div>
                    <Progress value={cloneProgress} className="w-full" />
                    <p className="text-xs text-muted-foreground">
                      This process may take several minutes
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Voice Clones List */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Your Voice Clones
                </CardTitle>
              </CardHeader>
              <CardContent>
                {voiceClones.length > 0 ? (
                  <div className="space-y-3">
                    {voiceClones.map((clone) => (
                      <div key={clone.id} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold">{clone.name}</h4>
                          <Badge variant="secondary">{clone.status}</Badge>
                        </div>
                        
                        {clone.description && (
                          <p className="text-sm text-muted-foreground mb-2">
                            {clone.description}
                          </p>
                        )}
                        
                        <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
                          Sample: {clone.sampleText}
                        </p>
                        
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline" className="flex-1">
                            <Play className="h-3 w-3 mr-1" />
                            Test
                          </Button>
                          <Button size="sm" variant="outline" className="flex-1">
                            <Settings className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <User className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="font-semibold mb-2">No voice clones yet</h3>
                    <p className="text-sm text-muted-foreground">
                      Create your first voice clone to get started
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}