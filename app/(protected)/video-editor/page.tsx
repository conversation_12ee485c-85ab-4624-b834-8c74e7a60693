'use client';

import { VideoEditor } from '@/components/video-editor';
import { VideoSystemConfig } from '@/lib/video/types/video-types';

export default function VideoEditorPage() {
  const config: Partial<VideoSystemConfig> = {
    aiProviders: {
      // Add your API keys here
      runway: { apiKey: process.env.NEXT_PUBLIC_RUNWAY_API_KEY || '' },
      pika: { apiKey: process.env.NEXT_PUBLIC_PIKA_API_KEY || '' },
      stability: { apiKey: process.env.NEXT_PUBLIC_STABILITY_API_KEY || '' },
      elevenlabs: { apiKey: process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '' },
      openai: { apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY || '' },
    },
    webcodecs: {
      enabled: true,
      hardwareAcceleration: true,
    },
  };

  const handleProjectSave = (projectData: string) => {
    // Save project data to your backend or local storage
    console.log('Saving project:', projectData);
    localStorage.setItem('video-project', projectData);
  };

  const handleVideoExport = (videoUrl: string) => {
    // Handle exported video
    console.log('Video exported:', videoUrl);
    
    // Create download link
    const link = document.createElement('a');
    link.href = videoUrl;
    link.download = 'exported-video.mp4';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="h-screen">
      <VideoEditor
        config={config}
        onProjectSave={handleProjectSave}
        onVideoExport={handleVideoExport}
      />
    </div>
  );
}