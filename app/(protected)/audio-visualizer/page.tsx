'use client';

import React, { useState, useRef } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Palette, 
  Upload, 
  Play, 
  Pause,
  Download, 
  Settings, 
  Waveform,
  BarChart3,
  Circle,
  Sparkles,
  Music
} from 'lucide-react';
import { toast } from 'sonner';

export default function AudioVisualizerPage() {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [visualizerType, setVisualizerType] = useState('spectrum');
  const [colorScheme, setColorScheme] = useState('rainbow');
  const [backgroundType, setBackgroundType] = useState('gradient');
  const [sensitivity, setSensitivity] = useState([1.0]);
  const [smoothing, setSmoothing] = useState([0.8]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [generatedVisualizer, setGeneratedVisualizer] = useState<any>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type.startsWith('audio/')) {
        setAudioFile(file);
        toast.success(`Loaded ${file.name}`);
      } else {
        toast.error('Please select an audio file');
      }
    }
  };

  const handleGenerate = async () => {
    if (!audioFile) {
      toast.error('Please upload an audio file first');
      return;
    }

    setIsGenerating(true);
    setProgress(0);

    // Simulate generation progress
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 95) {
          clearInterval(progressInterval);
          return 95;
        }
        return prev + Math.random() * 15;
      });
    }, 300);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      clearInterval(progressInterval);
      setProgress(100);

      // Mock generated visualizer
      setGeneratedVisualizer({
        id: `visualizer-${Date.now()}`,
        audioFile: audioFile.name,
        type: visualizerType,
        colorScheme,
        backgroundType,
        sensitivity: sensitivity[0],
        smoothing: smoothing[0],
        duration: 180, // Mock duration
        url: 'mock-visualizer-video.mp4',
        createdAt: new Date().toISOString(),
      });

      toast.success('Audio visualizer generated successfully!');
    } catch (error) {
      toast.error('Failed to generate visualizer');
      console.error(error);
    } finally {
      setIsGenerating(false);
      setTimeout(() => setProgress(0), 1000);
    }
  };

  const getVisualizerIcon = (type: string) => {
    switch (type) {
      case 'waveform': return Waveform;
      case 'spectrum': return BarChart3;
      case 'circular': return Circle;
      case 'bars': return BarChart3;
      case 'particles': return Sparkles;
      default: return BarChart3;
    }
  };

  const VisualizerIcon = getVisualizerIcon(visualizerType);

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Audio Visualizer</h1>
        <p className="text-muted-foreground">
          Create stunning reactive visualizations for your audio tracks
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configuration Panel */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Music className="h-5 w-5" />
                Audio Input
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Audio File</Label>
                <div className="mt-2">
                  <Button
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    className="w-full"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    {audioFile ? audioFile.name : 'Upload Audio File'}
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="audio/*"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                </div>
              </div>

              {audioFile && (
                <div className="p-3 bg-muted rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-sm">{audioFile.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {(audioFile.size / (1024 * 1024)).toFixed(2)} MB
                      </p>
                    </div>
                    <Badge variant="secondary">Ready</Badge>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Visualizer Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="visualizerType">Visualizer Type</Label>
                <Select value={visualizerType} onValueChange={setVisualizerType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="waveform">Waveform</SelectItem>
                    <SelectItem value="spectrum">Frequency Spectrum</SelectItem>
                    <SelectItem value="circular">Circular</SelectItem>
                    <SelectItem value="bars">Vertical Bars</SelectItem>
                    <SelectItem value="particles">Particles</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="colorScheme">Color Scheme</Label>
                <Select value={colorScheme} onValueChange={setColorScheme}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="rainbow">Rainbow</SelectItem>
                    <SelectItem value="monochrome">Monochrome</SelectItem>
                    <SelectItem value="warm">Warm Colors</SelectItem>
                    <SelectItem value="cool">Cool Colors</SelectItem>
                    <SelectItem value="neon">Neon</SelectItem>
                    <SelectItem value="pastel">Pastel</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="backgroundType">Background</Label>
                <Select value={backgroundType} onValueChange={setBackgroundType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="solid">Solid Color</SelectItem>
                    <SelectItem value="gradient">Gradient</SelectItem>
                    <SelectItem value="image">Custom Image</SelectItem>
                    <SelectItem value="video">Video Background</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Sensitivity: {sensitivity[0]}</Label>
                <Slider
                  value={sensitivity}
                  onValueChange={setSensitivity}
                  max={2}
                  min={0.1}
                  step={0.1}
                  className="mt-2"
                />
              </div>

              <div>
                <Label>Smoothing: {smoothing[0]}</Label>
                <Slider
                  value={smoothing}
                  onValueChange={setSmoothing}
                  max={1}
                  min={0}
                  step={0.1}
                  className="mt-2"
                />
              </div>

              <Button 
                onClick={handleGenerate}
                disabled={isGenerating || !audioFile}
                className="w-full"
                size="lg"
              >
                <Palette className="h-4 w-4 mr-2" />
                {isGenerating ? 'Generating...' : 'Generate Visualizer'}
              </Button>

              {isGenerating && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Creating visualizer...</span>
                    <span>{Math.round(progress)}%</span>
                  </div>
                  <Progress value={progress} className="w-full" />
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Preview Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <VisualizerIcon className="h-5 w-5" />
              Preview
            </CardTitle>
          </CardHeader>
          <CardContent>
            {generatedVisualizer ? (
              <div className="space-y-4">
                <div className="aspect-video bg-gradient-to-br from-purple-500 via-pink-500 to-red-500 rounded-lg flex items-center justify-center relative overflow-hidden">
                  <div className="absolute inset-0 bg-black/20"></div>
                  <div className="text-center text-white z-10">
                    <VisualizerIcon className="h-16 w-16 mx-auto mb-4 animate-pulse" />
                    <p className="text-lg font-semibold">Live Visualizer Preview</p>
                    <p className="text-sm opacity-80">Audio-reactive visualization</p>
                  </div>
                  
                  {/* Mock visualizer elements */}
                  <div className="absolute bottom-4 left-4 right-4 flex justify-center space-x-1">
                    {Array.from({ length: 20 }).map((_, i) => (
                      <div
                        key={i}
                        className="w-2 bg-white/60 rounded-t animate-pulse"
                        style={{
                          height: `${Math.random() * 60 + 20}px`,
                          animationDelay: `${i * 0.1}s`
                        }}
                      />
                    ))}
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold">Generated Visualizer</h3>
                    <div className="flex gap-2">
                      <Badge variant="secondary">{generatedVisualizer.type}</Badge>
                      <Badge variant="outline">{generatedVisualizer.colorScheme}</Badge>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Audio:</span>
                      <p className="font-medium truncate">{generatedVisualizer.audioFile}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Duration:</span>
                      <p className="font-medium">
                        {Math.floor(generatedVisualizer.duration / 60)}:
                        {(generatedVisualizer.duration % 60).toString().padStart(2, '0')}
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      className="flex-1"
                      onClick={() => setIsPlaying(!isPlaying)}
                    >
                      {isPlaying ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
                      {isPlaying ? 'Pause' : 'Play'}
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <Palette className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="font-semibold mb-2">No visualizer generated yet</h3>
                  <p className="text-sm text-muted-foreground">
                    Upload an audio file and configure settings to create your visualizer
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Presets */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Popular Presets</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { name: 'Classic Spectrum', type: 'spectrum', colors: 'rainbow' },
              { name: 'Neon Bars', type: 'bars', colors: 'neon' },
              { name: 'Particle Storm', type: 'particles', colors: 'cool' },
              { name: 'Circular Wave', type: 'circular', colors: 'warm' },
            ].map((preset) => (
              <Button
                key={preset.name}
                variant="outline"
                className="h-auto p-4 flex flex-col items-center gap-2"
                onClick={() => {
                  setVisualizerType(preset.type);
                  setColorScheme(preset.colors);
                  toast.success(`Applied ${preset.name} preset`);
                }}
              >
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded"></div>
                <span className="text-sm font-medium">{preset.name}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}