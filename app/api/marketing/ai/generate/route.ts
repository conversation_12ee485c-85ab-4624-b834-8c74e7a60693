import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { AIGenerationRequest, AIGenerationResponse } from '@/app/(protected)/dashboard/marketing/types';
import { prisma } from '@/lib/prisma';

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body: AIGenerationRequest = await request.json();

    // For development, return mock data
    const mockResponse: AIGenerationResponse = {
      content: `Generated ${body.type} content for ${body.platform} targeting ${body.targetAudience.join(', ')}. Keywords: ${body.keywords.join(', ')}. Tone: ${body.tone}. Length: ${body.length}. Style: ${body.style}.`,
      suggestions: [
        'Consider adding more emotional appeal',
        'Include a clear call-to-action',
        'Use more engaging visuals',
        'Optimize for mobile viewing'
      ],
      variations: [
        `Alternative ${body.type} content variation 1`,
        `Alternative ${body.type} content variation 2`,
        `Alternative ${body.type} content variation 3`
      ],
      metadata: {
        estimatedEngagement: Math.floor(Math.random() * 1000) + 500,
        readabilityScore: Math.floor(Math.random() * 30) + 70,
        sentimentScore: Math.floor(Math.random() * 30) + 70,
        seoScore: Math.floor(Math.random() * 30) + 70
      }
    };

    // Save the generation request to the database
    await prisma.aiGeneration.create({
      data: {
        userId: session.user.id,
        type: body.type,
        platform: body.platform,
        targetAudience: body.targetAudience,
        keywords: body.keywords,
        tone: body.tone,
        length: body.length,
        style: body.style,
        additionalContext: body.additionalContext,
        response: mockResponse
      }
    });

    return NextResponse.json(mockResponse);
  } catch (error) {
    console.error('Error generating content:', error);
    return NextResponse.json(
      { error: 'Failed to generate content' },
      { status: 500 }
    );
  }
} 