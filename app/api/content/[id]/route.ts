import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { z } from 'zod'

// Content update validation schema
const contentUpdateSchema = z.object({
  title: z.string().min(1, { message: "Title is required" }).optional(),
  description: z.string().optional(),
  type: z.string().min(1, { message: "Content type is required" }).optional(),
  format: z.string().min(1, { message: "Format is required" }).optional(),
  fileUrl: z.string().url({ message: "Valid file URL is required" }).optional(),
  thumbnailUrl: z.string().url().optional(),
  duration: z.string().optional(),
  dimensions: z.string().optional(),
  manualTags: z.array(z.string()).optional(),
  status: z.enum(['draft', 'published', 'archived', 'scheduled']).optional(),
})

// GET - Retrieve a single content item
export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    const userId = session.user.id
    const contentId = params.id
    
    const content = await prisma.content.findUnique({
      where: {
        id: contentId,
        userId,
      },
      include: {
        manualTags: true,
        aiTags: true,
      },
    })
    
    if (!content) {
      return NextResponse.json(
        { error: "Content not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json(content)
  } catch (error) {
    console.error('Error fetching content item:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT - Update a content item
export async function PUT(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    const userId = session.user.id
    const contentId = params.id
    const body = await req.json()
    
    // Validate input
    const result = contentUpdateSchema.safeParse(body)
    
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid input", details: result.error.errors },
        { status: 400 }
      )
    }
    
    // Verify ownership
    const existingContent = await prisma.content.findUnique({
      where: {
        id: contentId,
        userId,
      },
    })
    
    if (!existingContent) {
      return NextResponse.json(
        { error: "Content not found or unauthorized" },
        { status: 404 }
      )
    }
    
    const { manualTags, ...contentData } = result.data
    
    // Update the content with a transaction to handle tags
    const updatedContent = await prisma.$transaction(async (tx: typeof prisma) => {
      // Update the content
      const updated = await tx.content.update({
        where: { id: contentId },
        data: contentData,
      })
      
      // Update tags if provided
      if (manualTags) {
        // Delete existing tags
        await tx.contentTag.deleteMany({
          where: { contentId },
        })
        
        // Create new tags
        if (manualTags.length > 0) {
          await Promise.all(
            manualTags.map((tag) =>
              tx.contentTag.create({
                data: {
                  tag,
                  contentId,
                },
              })
            )
          )
        }
      }
      
      // Return updated content with tags
      return tx.content.findUnique({
        where: { id: contentId },
        include: {
          manualTags: true,
          aiTags: true,
        },
      })
    })
    
    return NextResponse.json(updatedContent)
  } catch (error) {
    console.error('Error updating content:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// DELETE - Delete a content item
export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    const userId = session.user.id
    const contentId = params.id
    
    // Verify ownership
    const existingContent = await prisma.content.findUnique({
      where: {
        id: contentId,
        userId,
      },
    })
    
    if (!existingContent) {
      return NextResponse.json(
        { error: "Content not found or unauthorized" },
        { status: 404 }
      )
    }
    
    // Delete content and related tags
    await prisma.$transaction([
      prisma.contentTag.deleteMany({
        where: { contentId },
      }),
      prisma.content.delete({
        where: { id: contentId },
      }),
    ])
    
    return NextResponse.json(
      { message: "Content deleted successfully" },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error deleting content:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
} 