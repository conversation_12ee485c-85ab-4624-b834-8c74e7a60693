import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { z } from 'zod';

// Content creation validation schema
const contentCreateSchema = z.object({
  title: z.string().min(1, { message: "Title is required" }),
  description: z.string().optional(),
  type: z.string().min(1, { message: "Content type is required" }),
  format: z.string().min(1, { message: "Format is required" }),
  fileUrl: z.string().url({ message: "Valid file URL is required" }),
  thumbnailUrl: z.string().url().optional(),
  duration: z.string().optional(),
  dimensions: z.string().optional(),
  manualTags: z.array(z.string()).optional(),
});

// GET - Retrieve all content for the authenticated user
export async function GET(req: Request) {
  try {
    const session = await getServerSession();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    const { searchParams } = new URL(req.url);
    const type = searchParams.get('type');
    const searchQuery = searchParams.get('query');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;
    
    // Build the where clause based on filters
    const where = {
      userId,
      ...(type ? { type } : {}),
      ...(searchQuery ? {
        OR: [
          { title: { contains: searchQuery, mode: 'insensitive' } },
          { description: { contains: searchQuery, mode: 'insensitive' } },
        ],
      } : {}),
    };
    
    // Fetch content with filters and pagination
    const [content, totalCount] = await Promise.all([
      prisma.content.findMany({
        where,
        include: {
          manualTags: true,
          aiTags: true,
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.content.count({ where }),
    ]);
    
    return NextResponse.json({
      content,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching content:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST - Create new content
export async function POST(req: Request) {
  try {
    const session = await getServerSession();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    const body = await req.json();
    
    // Validate input
    const result = contentCreateSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid input", details: result.error.errors },
        { status: 400 }
      );
    }
    
    const { manualTags, ...contentData } = result.data;
    
    // Create the content with a transaction to handle tags
    const content = await prisma.$transaction(async (tx: typeof prisma) => {
      // Create the content first
      const newContent = await tx.content.create({
        data: {
          ...contentData,
          userId,
        },
      });
      
      // Create manual tags if provided
      if (manualTags && manualTags.length > 0) {
        await Promise.all(
          manualTags.map((tag) =>
            tx.contentTag.create({
              data: {
                tag,
                contentId: newContent.id,
              },
            })
          )
        );
      }
      
      // Return the created content with tags
      return tx.content.findUnique({
        where: { id: newContent.id },
        include: {
          manualTags: true,
          aiTags: true,
        },
      });
    });
    
    return NextResponse.json(content, { status: 201 });
  } catch (error) {
    console.error('Error creating content:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 