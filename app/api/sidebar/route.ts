import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const sidebarPreferencesSchema = z.object({
  isOpen: z.boolean(),
  width: z.number().min(200).max(400).optional(),
  position: z.enum(['left', 'right']).optional(),
});

export async function GET(req: Request) {
  try {
    const session = await getServerSession();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    
    const preferences = await prisma.userPreferences.findUnique({
      where: { userId },
      select: {
        sidebarOpen: true,
        sidebarWidth: true,
        sidebarPosition: true,
      },
    });
    
    return NextResponse.json(preferences || {
      sidebarOpen: true,
      sidebarWidth: 250,
      sidebarPosition: 'left',
    });
  } catch (error) {
    console.error('Error fetching sidebar preferences:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(req: Request) {
  try {
    const session = await getServerSession();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    const body = await req.json();
    
    // Validate input
    const result = sidebarPreferencesSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid input", details: result.error.errors },
        { status: 400 }
      );
    }
    
    const { isOpen, width, position } = result.data;
    
    const preferences = await prisma.userPreferences.upsert({
      where: { userId },
      update: {
        sidebarOpen: isOpen,
        ...(width && { sidebarWidth: width }),
        ...(position && { sidebarPosition: position }),
      },
      create: {
        userId,
        sidebarOpen: isOpen,
        sidebarWidth: width || 250,
        sidebarPosition: position || 'left',
      },
      select: {
        sidebarOpen: true,
        sidebarWidth: true,
        sidebarPosition: true,
      },
    });
    
    return NextResponse.json(preferences);
  } catch (error) {
    console.error('Error updating sidebar preferences:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 