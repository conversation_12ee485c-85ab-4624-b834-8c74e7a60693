import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

interface ChartMetric {
  label: string
  value: number
  change: number
}

interface TimeSeriesData {
  date: string
  desktop: number
  mobile: number
  tablet: number
}

interface PlatformData {
  name: string
  value: number
}

interface PerformanceData {
  metric: string
  value: number
}

interface CorrelationData {
  x: number
  y: number
}

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '30d'
    const chartType = searchParams.get('chartType') || 'area'

    // Calculate date range
    const now = new Date()
    let startDate = new Date()
    switch (timeRange) {
      case '7d':
        startDate.setDate(now.getDate() - 7)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        break
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1)
        break
    }

    // Fetch data based on chart type
    let data: TimeSeriesData[] | PlatformData[] | PerformanceData[] | CorrelationData[] = []
    let metrics: ChartMetric[] = []

    switch (chartType) {
      case 'area':
      case 'bar':
      case 'line':
        // Fetch time series data
        const timeSeriesData = await prisma.audienceData.findMany({
          where: {
            userId: session.user.id,
            date: {
              gte: startDate,
              lte: now,
            },
          },
          orderBy: {
            date: 'asc',
          },
        })

        // Group data by date and platform
        const groupedData = timeSeriesData.reduce((acc: { [key: string]: { desktop: number; mobile: number; tablet: number } }, item) => {
          const date = item.date.toISOString()
          if (!acc[date]) {
            acc[date] = { desktop: 0, mobile: 0, tablet: 0 }
          }
          const value = typeof item.value === 'number' ? item.value : 0
          switch (item.platform) {
            case 'desktop':
              acc[date].desktop = value
              break
            case 'mobile':
              acc[date].mobile = value
              break
            case 'tablet':
              acc[date].tablet = value
              break
          }
          return acc
        }, {})

        data = Object.entries(groupedData).map(([date, values]) => ({
          date,
          ...values,
        }))

        // Calculate metrics
        const lastPeriod = await prisma.audienceData.findMany({
          where: {
            userId: session.user.id,
            date: {
              gte: new Date(startDate.getTime() - (now.getTime() - startDate.getTime())),
              lt: startDate,
            },
          },
        })

        const currentTotal = Object.values(groupedData).reduce((sum, values) => sum + values.desktop + values.mobile + values.tablet, 0)
        const lastPeriodTotal = lastPeriod.reduce((sum, item) => {
          const value = typeof item.value === 'number' ? item.value : 0
          return sum + value
        }, 0)

        metrics = [
          {
            label: 'Total Users',
            value: currentTotal,
            change: calculateChange(lastPeriodTotal, currentTotal),
          },
          {
            label: 'Desktop Users',
            value: Object.values(groupedData).reduce((sum, values) => sum + values.desktop, 0),
            change: calculateChange(
              lastPeriod.filter(item => item.platform === 'desktop').reduce((sum, item) => sum + (typeof item.value === 'number' ? item.value : 0), 0),
              Object.values(groupedData).reduce((sum, values) => sum + values.desktop, 0)
            ),
          },
          {
            label: 'Mobile Users',
            value: Object.values(groupedData).reduce((sum, values) => sum + values.mobile, 0),
            change: calculateChange(
              lastPeriod.filter(item => item.platform === 'mobile').reduce((sum, item) => sum + (typeof item.value === 'number' ? item.value : 0), 0),
              Object.values(groupedData).reduce((sum, values) => sum + values.mobile, 0)
            ),
          },
        ]
        break

      case 'pie':
        // Fetch platform distribution data
        const platformData = await prisma.distributionPlatform.findMany()

        data = platformData.map((item) => ({
          name: item.name,
          value: item.status === 'Active' ? 1 : 0,
        }))
        break

      case 'radar':
        // Fetch performance metrics
        const performanceData = await prisma.audienceData.findMany({
          where: {
            userId: session.user.id,
            date: {
              gte: startDate,
              lte: now,
            },
          },
        })

        const performanceMetrics = ['engagement', 'retention', 'conversion', 'growth', 'satisfaction']
        data = performanceMetrics.map(metric => {
          const value = performanceData
            .filter(item => item.metric === metric)
            .reduce((sum, item) => sum + (typeof item.value === 'number' ? item.value : 0), 0) / performanceData.length
          return { metric, value }
        })
        break

      case 'scatter':
        // Fetch correlation data
        const correlationData = await prisma.audienceData.findMany({
          where: {
            userId: session.user.id,
            date: {
              gte: startDate,
              lte: now,
            },
          },
        })

        const engagementData = correlationData.filter(item => item.metric === 'engagement')
        const conversionData = correlationData.filter(item => item.metric === 'conversion')

        data = engagementData.map((item, index) => ({
          x: typeof item.value === 'number' ? item.value : 0,
          y: typeof conversionData[index]?.value === 'number' ? conversionData[index].value : 0,
        }))
        break
    }

    return NextResponse.json({ data, metrics })
  } catch (error) {
    console.error('Error fetching chart data:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}

function calculateChange(previous: number, current: number): number {
  if (previous === 0) return current > 0 ? 100 : 0
  return ((current - previous) / previous) * 100
} 