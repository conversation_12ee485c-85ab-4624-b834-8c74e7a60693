import { NextRequest } from 'next/server';
import { streamText } from 'ai';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { modelConfigs } from '@/lib/ai-providers';
import { musicTools } from '@/lib/ai-tools';

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    const { messages } = await req.json();

    const result = streamText({
      model: modelConfigs.chat.model,
      messages,
      temperature: modelConfigs.chat.temperature,
      maxTokens: modelConfigs.chat.maxTokens,
      maxSteps: 5,
      tools: musicTools,
      system: `You are a professional music industry AI assistant for Tune Base, a comprehensive music production and distribution platform. You help artists, producers, and music professionals with:

1. **Music Production**: Song concepts, arrangements, chord progressions, and creative ideas
2. **Marketing Strategy**: Social media content, release planning, and audience engagement
3. **Industry Insights**: Market trends, genre analysis, and strategic recommendations
4. **Release Management**: Timeline planning, distribution strategy, and promotional campaigns

Key Guidelines:
- Always provide actionable, industry-relevant advice
- Use tools when users ask for specific analysis or content generation
- Focus on practical solutions that can be implemented immediately
- Consider current music industry trends and best practices
- Maintain a professional yet approachable tone
- Ask clarifying questions when needed to provide better assistance

You have access to specialized tools for:
- Generating song concepts and creative ideas
- Analyzing market trends and opportunities
- Creating platform-specific marketing content
- Developing comprehensive release plans

User Context: This user is authenticated and has access to all premium features of Tune Base.`,
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error('AI Chat API Error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}