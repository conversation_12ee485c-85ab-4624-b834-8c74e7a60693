import { NextRequest } from 'next/server';
import { generateText } from 'ai';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { modelConfigs } from '@/lib/ai-providers';
import { prisma } from '@/lib/prisma';

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    const { 
      trackName, 
      artist, 
      genre, 
      platform, 
      contentType, 
      tone, 
      additionalContext 
    } = await req.json();

    const prompt = `Create ${contentType} content for ${platform} to promote "${trackName}" by ${artist}.

Genre: ${genre}
Tone: ${tone}
${additionalContext ? `Additional context: ${additionalContext}` : ''}

Platform-specific requirements:
${platform === 'instagram' ? `
- Engaging caption with line breaks
- Include relevant hashtags (max 30)
- Use emojis appropriately
- Include call-to-action
- Optimal length: 125-150 characters for better engagement
` : ''}
${platform === 'twitter' ? `
- Concise and impactful (max 280 characters)
- Include relevant hashtags (max 3-5)
- Use emojis sparingly
- Include call-to-action
- Thread-friendly format if needed
` : ''}
${platform === 'tiktok' ? `
- Catchy and trend-focused
- Include trending hashtags and sounds
- Hook in first 3 seconds
- Include call-to-action
- Short and punchy
` : ''}
${platform === 'youtube' ? `
- Detailed description with timestamps
- SEO-optimized keywords
- Include links and credits
- Engaging thumbnail text suggestions
- Community tab posts
` : ''}
${platform === 'facebook' ? `
- Storytelling approach
- Longer format content
- Include visual description
- Community engagement focus
- Event promotion integration
` : ''}

Content Type: ${contentType}
${contentType === 'caption' ? 'Create an engaging social media caption' : ''}
${contentType === 'hashtags' ? 'Generate relevant hashtags only' : ''}
${contentType === 'description' ? 'Create a detailed description' : ''}
${contentType === 'story' ? 'Create story-style content' : ''}

Make it authentic, engaging, and tailored to ${genre} music fans.`;

    const result = await generateText({
      model: modelConfigs.creative.model,
      prompt,
      temperature: modelConfigs.creative.temperature,
      maxTokens: modelConfigs.creative.maxTokens,
    });

    // Save the generation request to the database
    await prisma.aiGeneration.create({
      data: {
        userId: session.user.id,
        type: contentType,
        platform,
        targetAudience: [genre],
        keywords: [trackName, artist, genre],
        tone,
        length: 'medium',
        style: 'social_media',
        additionalContext: additionalContext || '',
        response: {
          content: result.text,
          metadata: {
            model: 'gpt-4o',
            timestamp: new Date().toISOString(),
            platform,
            contentType,
          }
        }
      }
    });

    return Response.json({
      content: result.text,
      metadata: {
        model: 'gpt-4o',
        timestamp: new Date().toISOString(),
        platform,
        contentType,
        usage: result.usage,
      }
    });

  } catch (error) {
    console.error('AI Content Generation Error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}