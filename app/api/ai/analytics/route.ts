import { NextRequest } from 'next/server';
import { generateObject } from 'ai';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { modelConfigs } from '@/lib/ai-providers';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const analyticsSchema = z.object({
  insights: z.array(z.object({
    type: z.enum(['audience_growth', 'engagement', 'revenue', 'content_performance']),
    title: z.string(),
    description: z.string(),
    impact: z.enum(['high', 'medium', 'low']),
    actionable: z.boolean(),
    metric: z.object({
      value: z.number(),
      unit: z.string(),
      trend: z.enum(['increasing', 'decreasing', 'stable']),
      comparison: z.string(),
    }),
  })),
  recommendations: z.array(z.object({
    category: z.enum(['content', 'marketing', 'release', 'audience']),
    title: z.string(),
    description: z.string(),
    priority: z.enum(['high', 'medium', 'low']),
    estimatedImpact: z.string(),
    timeframe: z.string(),
    steps: z.array(z.string()),
  })),
  predictions: z.array(z.object({
    metric: z.string(),
    timeframe: z.string(),
    prediction: z.string(),
    confidence: z.number(),
    factors: z.array(z.string()),
  })),
  opportunities: z.array(z.object({
    title: z.string(),
    description: z.string(),
    marketSize: z.string(),
    difficulty: z.enum(['easy', 'medium', 'hard']),
    timeline: z.string(),
  })),
});

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    const { 
      timeframe = '30d',
      focusAreas = ['audience_growth', 'engagement'],
      genre,
      currentMetrics = {},
    } = await req.json();

    // Fetch user's recent analytics data
    const userAnalytics = await prisma.analytics.findMany({
      where: {
        userId: session.user.id,
        timestamp: {
          gte: new Date(Date.now() - (timeframe === '30d' ? 30 : timeframe === '7d' ? 7 : 90) * 24 * 60 * 60 * 1000)
        }
      },
      orderBy: { timestamp: 'desc' },
      take: 100,
    });

    const prompt = `Analyze the following music analytics data and provide actionable insights:

Time Period: ${timeframe}
Focus Areas: ${focusAreas.join(', ')}
Genre: ${genre || 'Not specified'}
Current Metrics: ${JSON.stringify(currentMetrics)}

Recent Analytics Data:
${userAnalytics.length > 0 ? JSON.stringify(userAnalytics.slice(0, 10), null, 2) : 'Limited data available'}

Provide:
1. Key insights about performance trends
2. Actionable recommendations for improvement
3. Predictions for future performance
4. Market opportunities to explore

Focus on practical, data-driven advice that a music artist can implement immediately.`;

    const result = await generateObject({
      model: modelConfigs.analytical.model,
      prompt,
      schema: analyticsSchema,
      temperature: modelConfigs.analytical.temperature,
    });

    // Save the analytics request
    await prisma.aiGeneration.create({
      data: {
        userId: session.user.id,
        type: 'analytics',
        platform: 'dashboard',
        targetAudience: [genre || 'general'],
        keywords: focusAreas,
        tone: 'professional',
        length: 'long',
        style: 'analytical',
        additionalContext: `Timeframe: ${timeframe}`,
        response: {
          insights: result.object.insights,
          recommendations: result.object.recommendations,
          predictions: result.object.predictions,
          opportunities: result.object.opportunities,
          metadata: {
            model: 'gpt-4o',
            timestamp: new Date().toISOString(),
            dataPoints: userAnalytics.length,
          }
        }
      }
    });

    return Response.json({
      ...result.object,
      metadata: {
        model: 'gpt-4o',
        timestamp: new Date().toISOString(),
        dataPoints: userAnalytics.length,
        usage: result.usage,
      }
    });

  } catch (error) {
    console.error('AI Analytics Error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}