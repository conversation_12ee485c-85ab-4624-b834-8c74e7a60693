import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Fetch analytics data from the database
    const [
      demographics,
      listeningHabits,
      geographic,
      engagement,
      growth,
      platforms
    ] = await Promise.all([
      prisma.audienceData.findFirst({
        where: { 
          userId: session.user.id,
          platform: 'aggregate',
          metric: 'demographics'
        },
        orderBy: { date: 'desc' }
      }),
      prisma.audienceData.findFirst({
        where: { 
          userId: session.user.id,
          platform: 'aggregate',
          metric: 'listening_habits'
        },
        orderBy: { date: 'desc' }
      }),
      prisma.audienceData.findFirst({
        where: { 
          userId: session.user.id,
          platform: 'aggregate',
          metric: 'geographic'
        },
        orderBy: { date: 'desc' }
      }),
      prisma.audienceData.findFirst({
        where: { 
          userId: session.user.id,
          platform: 'aggregate',
          metric: 'engagement'
        },
        orderBy: { date: 'desc' }
      }),
      prisma.audienceData.findFirst({
        where: { 
          userId: session.user.id,
          platform: 'aggregate',
          metric: 'growth'
        },
        orderBy: { date: 'desc' }
      }),
      prisma.audienceData.findFirst({
        where: { 
          userId: session.user.id,
          platform: 'aggregate',
          metric: 'platforms'
        },
        orderBy: { date: 'desc' }
      })
    ]);

    // For development, return mock data if no database entries exist
    if (!demographics || !listeningHabits || !geographic || !engagement || !growth || !platforms) {
      return NextResponse.json({
        demographics: {
          ageGroups: [
            { range: '18-24', count: 1200, percentage: 30 },
            { range: '25-34', count: 2000, percentage: 50 },
            { range: '35-44', count: 600, percentage: 15 },
            { range: '45+', count: 200, percentage: 5 }
          ],
          gender: {
            male: 55,
            female: 40,
            other: 5
          },
          totalListeners: 4000
        },
        listeningHabits: {
          averageDailyListenTime: 45,
          peakListeningHours: [
            { hour: 8, count: 800 },
            { hour: 12, count: 1200 },
            { hour: 18, count: 1500 },
            { hour: 22, count: 1000 }
          ],
          favoriteGenres: [
            { genre: 'Pop', percentage: 40 },
            { genre: 'Rock', percentage: 25 },
            { genre: 'Hip Hop', percentage: 20 },
            { genre: 'Electronic', percentage: 15 }
          ],
          deviceTypes: [
            { device: 'Mobile', percentage: 60 },
            { device: 'Desktop', percentage: 25 },
            { device: 'Tablet', percentage: 15 }
          ]
        },
        geographic: {
          countries: [
            { country: 'United States', listeners: 2000, percentage: 50 },
            { country: 'United Kingdom', listeners: 800, percentage: 20 },
            { country: 'Canada', listeners: 400, percentage: 10 },
            { country: 'Australia', listeners: 400, percentage: 10 },
            { country: 'Others', listeners: 400, percentage: 10 }
          ],
          topCities: [
            { city: 'New York', country: 'United States', listeners: 500 },
            { city: 'London', country: 'United Kingdom', listeners: 300 },
            { city: 'Los Angeles', country: 'United States', listeners: 400 },
            { city: 'Toronto', country: 'Canada', listeners: 200 },
            { city: 'Sydney', country: 'Australia', listeners: 200 }
          ]
        },
        engagement: {
          averageSessionDuration: 25,
          retentionRate: 75,
          skipRate: 15,
          completionRate: 85,
          interactionRate: 45
        },
        growth: {
          daily: Array.from({ length: 7 }, (_, i) => ({
            date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            newListeners: Math.floor(Math.random() * 100),
            activeListeners: Math.floor(Math.random() * 500) + 1000
          })),
          weekly: Array.from({ length: 4 }, (_, i) => ({
            week: `Week ${4 - i}`,
            newListeners: Math.floor(Math.random() * 500),
            activeListeners: Math.floor(Math.random() * 2000) + 3000
          })),
          monthly: Array.from({ length: 3 }, (_, i) => ({
            month: new Date(Date.now() - i * 30 * 24 * 60 * 60 * 1000).toLocaleString('default', { month: 'short' }),
            newListeners: Math.floor(Math.random() * 2000),
            activeListeners: Math.floor(Math.random() * 5000) + 8000
          }))
        },
        platforms: {
          platforms: [
            { platform: 'Spotify', users: 2000, percentage: 50 },
            { platform: 'Apple Music', users: 1000, percentage: 25 },
            { platform: 'YouTube Music', users: 600, percentage: 15 },
            { platform: 'Others', users: 400, percentage: 10 }
          ],
          deviceTypes: [
            { device: 'iOS', users: 1800, percentage: 45 },
            { device: 'Android', users: 1400, percentage: 35 },
            { device: 'Web', users: 800, percentage: 20 }
          ]
        }
      });
    }

    // Return actual data from the database
    return NextResponse.json({
      demographics: demographics.value,
      listeningHabits: listeningHabits.value,
      geographic: geographic.value,
      engagement: engagement.value,
      growth: growth.value,
      platforms: platforms.value
    });
  } catch (error) {
    console.error('Error fetching analytics data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics data' },
      { status: 500 }
    );
  }
} 