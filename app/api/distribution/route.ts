import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Fetch distribution data from the database
    const [
      platformStatus,
      settings,
      schedule,
      territoryRights,
      delivery,
      reports
    ] = await Promise.all([
      prisma.distributionPlatform.findMany(),
      prisma.userPreferences.findUnique({
        where: { userId: session.user.id }
      }),
      prisma.release.findMany({
        where: { userId: session.user.id },
        orderBy: { releaseDate: 'desc' }
      }),
      prisma.audienceData.findFirst({
        where: { 
          userId: session.user.id,
          platform: 'aggregate',
          metric: 'territory_rights'
        }
      }),
      prisma.audienceData.findFirst({
        where: { 
          userId: session.user.id,
          platform: 'aggregate',
          metric: 'content_delivery'
        }
      }),
      prisma.royaltyTransaction.findMany({
        orderBy: { date: 'desc' }
      })
    ]);

    // For development, return mock data if no database entries exist
    if (!platformStatus.length || !settings || !schedule.length || !territoryRights || !delivery || !reports.length) {
      return NextResponse.json({
        platformStatus: {
          platforms: [
            {
              name: 'Spotify',
              status: 'Active',
              lastSync: new Date().toISOString(),
              contentCount: 25,
              releaseCount: 5
            },
            {
              name: 'Apple Music',
              status: 'Active',
              lastSync: new Date().toISOString(),
              contentCount: 25,
              releaseCount: 5
            },
            {
              name: 'YouTube Music',
              status: 'Pending',
              lastSync: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
              contentCount: 20,
              releaseCount: 4
            }
          ],
          totalContent: 25,
          totalReleases: 5
        },
        settings: {
          defaultTerritories: ['Worldwide'],
          releaseDelay: 7,
          pricing: [
            { platform: 'Spotify', price: 9.99, currency: 'USD' },
            { platform: 'Apple Music', price: 9.99, currency: 'USD' },
            { platform: 'YouTube Music', price: 9.99, currency: 'USD' }
          ],
          formats: [
            { format: 'MP3', enabled: true, quality: '320kbps' },
            { format: 'WAV', enabled: true, quality: 'Lossless' },
            { format: 'FLAC', enabled: true, quality: 'Lossless' }
          ]
        },
        schedule: {
          upcoming: [
            {
              releaseId: '1',
              title: 'New Album',
              platform: 'All Platforms',
              scheduledDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
              status: 'Scheduled'
            }
          ],
          recent: [
            {
              releaseId: '2',
              title: 'Single',
              platform: 'All Platforms',
              releaseDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
              status: 'Success'
            }
          ]
        },
        territoryRights: {
          territories: [
            {
              region: 'North America',
              rights: ['Streaming', 'Download'],
              restrictions: []
            },
            {
              region: 'Europe',
              rights: ['Streaming', 'Download'],
              restrictions: []
            }
          ],
          globalRights: ['Streaming', 'Download'],
          restrictedTerritories: []
        },
        delivery: {
          status: [
            {
              platform: 'Spotify',
              delivered: 25,
              pending: 0,
              failed: 0
            },
            {
              platform: 'Apple Music',
              delivered: 25,
              pending: 0,
              failed: 0
            },
            {
              platform: 'YouTube Music',
              delivered: 20,
              pending: 5,
              failed: 0
            }
          ],
          recentDeliveries: [
            {
              contentId: '1',
              platform: 'Spotify',
              date: new Date().toISOString(),
              status: 'Success'
            }
          ]
        },
        reports: {
          revenue: [
            {
              platform: 'Spotify',
              amount: 1000,
              currency: 'USD',
              period: 'Last 30 days'
            },
            {
              platform: 'Apple Music',
              amount: 800,
              currency: 'USD',
              period: 'Last 30 days'
            },
            {
              platform: 'YouTube Music',
              amount: 600,
              currency: 'USD',
              period: 'Last 30 days'
            }
          ],
          performance: [
            {
              platform: 'Spotify',
              streams: 100000,
              downloads: 5000,
              revenue: 1000
            },
            {
              platform: 'Apple Music',
              streams: 80000,
              downloads: 4000,
              revenue: 800
            },
            {
              platform: 'YouTube Music',
              streams: 60000,
              downloads: 3000,
              revenue: 600
            }
          ],
          trends: Array.from({ length: 30 }, (_, i) => ({
            date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            totalRevenue: Math.floor(Math.random() * 1000) + 500,
            totalStreams: Math.floor(Math.random() * 10000) + 50000,
            totalDownloads: Math.floor(Math.random() * 500) + 2500
          }))
        }
      });
    }

    // Return actual data from the database
    return NextResponse.json({
      platformStatus: {
        platforms: platformStatus.map(platform => ({
          name: platform.name,
          status: platform.status,
          lastSync: platform.dateDistributed?.toISOString() || new Date().toISOString(),
          contentCount: platform.contentId ? 1 : 0,
          releaseCount: platform.releaseId ? 1 : 0
        })),
        totalContent: platformStatus.filter(p => p.contentId).length,
        totalReleases: platformStatus.filter(p => p.releaseId).length
      },
      settings: {
        defaultTerritories: ['Worldwide'],
        releaseDelay: 7,
        pricing: [
          { platform: 'Spotify', price: 9.99, currency: 'USD' },
          { platform: 'Apple Music', price: 9.99, currency: 'USD' },
          { platform: 'YouTube Music', price: 9.99, currency: 'USD' }
        ],
        formats: [
          { format: 'MP3', enabled: true, quality: '320kbps' },
          { format: 'WAV', enabled: true, quality: 'Lossless' },
          { format: 'FLAC', enabled: true, quality: 'Lossless' }
        ]
      },
      schedule: {
        upcoming: schedule
          .filter(release => release.releaseDate && release.releaseDate > new Date())
          .map(release => ({
            releaseId: release.id,
            title: release.title,
            platform: 'All Platforms',
            scheduledDate: release.releaseDate?.toISOString() || '',
            status: release.isScheduled ? 'Scheduled' : 'Processing'
          })),
        recent: schedule
          .filter(release => release.releaseDate && release.releaseDate <= new Date())
          .slice(0, 5)
          .map(release => ({
            releaseId: release.id,
            title: release.title,
            platform: 'All Platforms',
            releaseDate: release.releaseDate?.toISOString() || '',
            status: 'Success'
          }))
      },
      territoryRights: territoryRights.value as any,
      delivery: delivery.value as any,
      reports: {
        revenue: reports.map(transaction => ({
          platform: transaction.platform,
          amount: transaction.amount,
          currency: transaction.currency,
          period: transaction.period
        })),
        performance: reports.reduce((acc, transaction) => {
          const existing = acc.find(p => p.platform === transaction.platform);
          if (existing) {
            existing.revenue += transaction.amount;
          } else {
            acc.push({
              platform: transaction.platform,
              streams: 0,
              downloads: 0,
              revenue: transaction.amount
            });
          }
          return acc;
        }, [] as any[]),
        trends: Array.from({ length: 30 }, (_, i) => ({
          date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          totalRevenue: Math.floor(Math.random() * 1000) + 500,
          totalStreams: Math.floor(Math.random() * 10000) + 50000,
          totalDownloads: Math.floor(Math.random() * 500) + 2500
        }))
      }
    });
  } catch (error) {
    console.error('Error fetching distribution data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch distribution data' },
      { status: 500 }
    );
  }
} 