"use client";

import { SessionProvider } from "next-auth/react";
import { ThemeProvider } from "next-themes";
import { ToastProvider } from "@/components/ui/toast";
import { UserProvider } from "@/components/providers/user-provider";
import { TopLoaderProvider } from "@/components/providers/top-loader-provider";
import TopLoader from "@/components/ui/top-loader/top-loader";

type ProvidersProps = {
  children: React.ReactNode;
};

export function Providers({ children }: ProvidersProps) {
  return (
    <SessionProvider>
      <UserProvider>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <TopLoaderProvider>
            <ToastProvider>
              <TopLoader
                height={3}
                showSpinner={false}
                easing="ease"
                speed={200}
                zIndex={1600}
                customTemplate={false}
              />
              {children}
            </ToastProvider>
          </TopLoaderProvider>
        </ThemeProvider>
      </UserProvider>
    </SessionProvider>
  );
} 