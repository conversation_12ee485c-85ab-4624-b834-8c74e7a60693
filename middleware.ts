import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";

export default withAuth(
  function middleware(req) {
    const { pathname } = req.nextUrl;
    const token = req.nextauth.token;

    // If user is authenticated and tries to access login/register, redirect to dashboard
    if (token && (pathname.startsWith('/login') || pathname.startsWith('/register'))) {
      return NextResponse.redirect(new URL('/dashboard', req.url));
    }

    // If user is not authenticated and tries to access protected routes, redirect to login
    if (!token && (pathname.startsWith('/dashboard') || pathname.startsWith('/(protected)'))) {
      const url = new URL('/login', req.url);
      url.searchParams.set('callbackUrl', req.nextUrl.pathname);
      return NextResponse.redirect(url);
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;
        
        // Public routes that don't require authentication
        const publicRoutes = [
          '/login',
          '/register',
          '/api/auth',
          '/api/register',
          '/_next',
          '/favicon.ico',
          '/'
        ];

        // Check if current path is a public route
        const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));
        
        if (isPublicRoute) {
          return true;
        }

        // Protected routes require authentication
        if (pathname.startsWith('/dashboard') || pathname.startsWith('/(protected)')) {
          return !!token;
        }

        // Default: allow access
        return true;
      },
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes that don't need auth)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api/(?!auth|register)|_next/static|_next/image|favicon.ico|public/).*)',
  ],
};