// ElevenLabs Conversational AI Hook
// React hook for managing real-time voice conversations

import { useEffect, useCallback, useRef } from 'react';
import { useConversationalAIStore } from '@/lib/elevenlabs/stores/conversational-ai-store';
import { useElevenLabsStore } from '@/lib/elevenlabs/stores/elevenlabs-store';
import type { 
  ConversationalAIAgent,
  ConversationalAIConfig,
  ConversationalMessage,
  ConversationalAIStatus,
  SessionMetrics
} from '@/lib/elevenlabs/types/conversational-ai-types';

export interface UseConversationalAIOptions {
  apiKey: string;
  autoLoadAgents?: boolean;
  autoLoadDevices?: boolean;
  enableAnalytics?: boolean;
  onSessionStart?: (session: any) => void;
  onSessionEnd?: (metrics: SessionMetrics) => void;
  onMessage?: (message: ConversationalMessage) => void;
  onError?: (error: any) => void;
  onStatusChange?: (status: ConversationalAIStatus) => void;
}

export interface UseConversationalAIReturn {
  // Agents
  agents: ConversationalAIAgent[];
  selectedAgent: ConversationalAIAgent | null;
  isLoadingAgents: boolean;
  agentsError: string | null;
  
  // Session
  session: any;
  sessionStatus: ConversationalAIStatus;
  isConnected: boolean;
  isListening: boolean;
  isSpeaking: boolean;
  isProcessing: boolean;
  sessionMetrics: SessionMetrics | null;
  
  // Messages
  messages: ConversationalMessage[];
  unreadCount: number;
  isTyping: boolean;
  
  // Audio
  isRecording: boolean;
  isPlaying: boolean;
  currentAudioUrl: string | null;
  audioDevices: MediaDeviceInfo[];
  selectedInputDevice: string | null;
  selectedOutputDevice: string | null;
  audioLevel: number;
  isAudioEnabled: boolean;
  isMuted: boolean;
  volume: number;
  
  // Actions
  loadAgents: () => Promise<void>;
  selectAgent: (agent: ConversationalAIAgent) => void;
  createAgent: (config: Partial<ConversationalAIAgent>) => Promise<ConversationalAIAgent>;
  updateAgent: (agentId: string, updates: Partial<ConversationalAIAgent>) => Promise<void>;
  deleteAgent: (agentId: string) => Promise<void>;
  
  startSession: (config?: Partial<ConversationalAIConfig>) => Promise<void>;
  endSession: () => Promise<void>;
  pauseSession: () => Promise<void>;
  resumeSession: () => Promise<void>;
  updateSessionConfig: (updates: Partial<ConversationalAIConfig>) => Promise<void>;
  
  sendTextMessage: (text: string) => Promise<void>;
  clearMessages: () => void;
  markMessagesAsRead: () => void;
  
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<void>;
  playAudio: (audioUrl: string) => Promise<void>;
  stopAudio: () => void;
  setVolume: (volume: number) => void;
  toggleMute: () => void;
  
  interrupt: () => Promise<void>;
  
  loadAudioDevices: () => Promise<void>;
  selectInputDevice: (deviceId: string) => void;
  selectOutputDevice: (deviceId: string) => void;
  
  clearError: (section?: string) => void;
  reset: () => void;
}

export function useConversationalAI(options: UseConversationalAIOptions): UseConversationalAIReturn {
  const {
    apiKey,
    autoLoadAgents = true,
    autoLoadDevices = true,
    enableAnalytics = true,
    onSessionStart,
    onSessionEnd,
    onMessage,
    onError,
    onStatusChange
  } = options;

  const store = useConversationalAIStore();
  const initialized = useRef(false);
  const previousStatus = useRef<ConversationalAIStatus>('disconnected');

  // Initialize service
  useEffect(() => {
    if (!apiKey || initialized.current) return;

    store.initializeService(apiKey);
    initialized.current = true;

    // Load initial data
    if (autoLoadAgents) {
      store.loadAgents();
    }
    if (autoLoadDevices) {
      store.loadAudioDevices();
    }

    // Cleanup on unmount
    return () => {
      store.destroyService();
      initialized.current = false;
    };
  }, [apiKey, autoLoadAgents, autoLoadDevices, store]);

  // Handle status changes
  useEffect(() => {
    if (store.session.status !== previousStatus.current) {
      onStatusChange?.(store.session.status);
      previousStatus.current = store.session.status;
    }
  }, [store.session.status, onStatusChange]);

  // Handle session events
  useEffect(() => {
    if (store.session.current && onSessionStart) {
      onSessionStart(store.session.current);
    }
  }, [store.session.current, onSessionStart]);

  useEffect(() => {
    if (store.session.metrics && onSessionEnd) {
      onSessionEnd(store.session.metrics);
    }
  }, [store.session.metrics, onSessionEnd]);

  // Handle messages
  useEffect(() => {
    const lastMessage = store.messages.list[store.messages.list.length - 1];
    if (lastMessage && onMessage) {
      onMessage(lastMessage);
    }
  }, [store.messages.list, onMessage]);

  // Handle errors
  useEffect(() => {
    if (store.session.lastError && onError) {
      onError(store.session.lastError);
    }
  }, [store.session.lastError, onError]);

  // Memoized actions
  const actions = {
    loadAgents: useCallback(() => store.loadAgents(), [store]),
    selectAgent: useCallback((agent: ConversationalAIAgent) => store.selectAgent(agent), [store]),
    createAgent: useCallback((config: Partial<ConversationalAIAgent>) => store.createAgent(config), [store]),
    updateAgent: useCallback((agentId: string, updates: Partial<ConversationalAIAgent>) => 
      store.updateAgent(agentId, updates), [store]),
    deleteAgent: useCallback((agentId: string) => store.deleteAgent(agentId), [store]),
    
    startSession: useCallback((config?: Partial<ConversationalAIConfig>) => store.startSession(config), [store]),
    endSession: useCallback(() => store.endSession(), [store]),
    pauseSession: useCallback(() => store.pauseSession(), [store]),
    resumeSession: useCallback(() => store.resumeSession(), [store]),
    updateSessionConfig: useCallback((updates: Partial<ConversationalAIConfig>) => 
      store.updateSessionConfig(updates), [store]),
    
    sendTextMessage: useCallback((text: string) => store.sendTextMessage(text), [store]),
    clearMessages: useCallback(() => store.clearMessages(), [store]),
    markMessagesAsRead: useCallback(() => store.markMessagesAsRead(), [store]),
    
    startRecording: useCallback(() => store.startRecording(), [store]),
    stopRecording: useCallback(() => store.stopRecording(), [store]),
    playAudio: useCallback((audioUrl: string) => store.playAudio(audioUrl), [store]),
    stopAudio: useCallback(() => store.stopAudio(), [store]),
    setVolume: useCallback((volume: number) => store.setVolume(volume), [store]),
    toggleMute: useCallback(() => store.toggleMute(), [store]),
    
    interrupt: useCallback(() => store.interrupt(), [store]),
    
    loadAudioDevices: useCallback(() => store.loadAudioDevices(), [store]),
    selectInputDevice: useCallback((deviceId: string) => store.selectInputDevice(deviceId), [store]),
    selectOutputDevice: useCallback((deviceId: string) => store.selectOutputDevice(deviceId), [store]),
    
    clearError: useCallback((section?: string) => store.clearError(section), [store]),
    reset: useCallback(() => store.reset(), [store])
  };

  return {
    // State
    agents: store.agents.list,
    selectedAgent: store.agents.selectedAgent,
    isLoadingAgents: store.agents.isLoading,
    agentsError: store.agents.error,
    
    session: store.session.current,
    sessionStatus: store.session.status,
    isConnected: store.session.isConnected,
    isListening: store.session.isListening,
    isSpeaking: store.session.isSpeaking,
    isProcessing: store.session.isProcessing,
    sessionMetrics: store.session.metrics,
    
    messages: store.messages.list,
    unreadCount: store.messages.unreadCount,
    isTyping: store.messages.isTyping,
    
    isRecording: store.audio.isRecording,
    isPlaying: store.audio.isPlaying,
    currentAudioUrl: store.audio.currentAudioUrl,
    audioDevices: store.audio.audioDevices,
    selectedInputDevice: store.audio.selectedInputDevice,
    selectedOutputDevice: store.audio.selectedOutputDevice,
    audioLevel: store.audio.audioLevel,
    isAudioEnabled: store.audio.isAudioEnabled,
    isMuted: store.audio.isMuted,
    volume: store.audio.volume,
    
    // Actions
    ...actions
  };
}

// Specialized hook for session management
export function useConversationalSession(agentId?: string) {
  const store = useConversationalAIStore();
  
  const startSessionWithAgent = useCallback(async (config?: Partial<ConversationalAIConfig>) => {
    if (agentId) {
      const agent = store.agents.list.find(a => a.agent_id === agentId);
      if (agent) {
        store.selectAgent(agent);
      }
    }
    
    await store.startSession(config);
  }, [agentId, store]);
  
  return {
    session: store.session.current,
    status: store.session.status,
    isConnected: store.session.isConnected,
    metrics: store.session.metrics,
    error: store.session.lastError,
    
    startSession: startSessionWithAgent,
    endSession: store.endSession,
    pauseSession: store.pauseSession,
    resumeSession: store.resumeSession,
    updateConfig: store.updateSessionConfig
  };
}

// Specialized hook for message handling
export function useConversationalMessages() {
  const store = useConversationalAIStore();
  
  const sendMessage = useCallback(async (content: string) => {
    await store.sendTextMessage(content);
  }, [store]);
  
  const getLastMessage = useCallback(() => {
    return store.messages.list[store.messages.list.length - 1];
  }, [store.messages.list]);
  
  const getMessagesByType = useCallback((type: ConversationalMessage['type']) => {
    return store.messages.list.filter(msg => msg.type === type);
  }, [store.messages.list]);
  
  const getMessagesBySender = useCallback((sender: ConversationalMessage['sender']) => {
    return store.messages.list.filter(msg => msg.sender === sender);
  }, [store.messages.list]);
  
  return {
    messages: store.messages.list,
    unreadCount: store.messages.unreadCount,
    isTyping: store.messages.isTyping,
    
    sendMessage,
    clearMessages: store.clearMessages,
    markAsRead: store.markMessagesAsRead,
    
    getLastMessage,
    getMessagesByType,
    getMessagesBySender
  };
}

// Specialized hook for audio management
export function useConversationalAudio() {
  const store = useConversationalAIStore();
  
  const startRecordingWithDevice = useCallback(async (deviceId?: string) => {
    if (deviceId) {
      store.selectInputDevice(deviceId);
    }
    await store.startRecording();
  }, [store]);
  
  const playAudioWithDevice = useCallback(async (audioUrl: string, deviceId?: string) => {
    if (deviceId) {
      store.selectOutputDevice(deviceId);
    }
    await store.playAudio(audioUrl);
  }, [store]);
  
  return {
    isRecording: store.audio.isRecording,
    isPlaying: store.audio.isPlaying,
    currentAudioUrl: store.audio.currentAudioUrl,
    audioDevices: store.audio.audioDevices,
    selectedInputDevice: store.audio.selectedInputDevice,
    selectedOutputDevice: store.audio.selectedOutputDevice,
    audioLevel: store.audio.audioLevel,
    isAudioEnabled: store.audio.isAudioEnabled,
    isMuted: store.audio.isMuted,
    volume: store.audio.volume,
    
    startRecording: startRecordingWithDevice,
    stopRecording: store.stopRecording,
    playAudio: playAudioWithDevice,
    stopAudio: store.stopAudio,
    setVolume: store.setVolume,
    toggleMute: store.toggleMute,
    
    loadDevices: store.loadAudioDevices,
    selectInputDevice: store.selectInputDevice,
    selectOutputDevice: store.selectOutputDevice
  };
}

// Specialized hook for agent management
export function useConversationalAgents() {
  const store = useConversationalAIStore();
  const { apiKey } = useElevenLabsStore();
  
  // Initialize service when API key is available
  useEffect(() => {
    if (apiKey && !store.service) {
      store.initializeService(apiKey);
    }
  }, [apiKey, store]);
  
  const createAgentWithDefaults = useCallback(async (config: Partial<ConversationalAIAgent>) => {
    const defaultConfig: Partial<ConversationalAIAgent> = {
      conversation_config: {
        conversation_timeout_ms: 30000,
        max_conversation_length_ms: 1800000, // 30 minutes
        enable_interruptions: true,
        enable_backchannel: true,
        enable_turn_taking: true,
        silence_threshold_ms: 1000,
        voice_activity_detection: {
          enabled: true,
          threshold: 0.01,
          min_speech_duration_ms: 300,
          max_silence_duration_ms: 1000,
          speech_pad_ms: 100,
          silence_pad_ms: 200,
          energy_threshold: 0.02,
          spectral_centroid_threshold: 0.1
        },
        audio_processing: {
          input_audio_format: {
            encoding: 'pcm_16',
            sample_rate: 44100,
            bit_depth: 16,
            channels: 1
          },
          output_audio_format: {
            encoding: 'pcm_16',
            sample_rate: 44100,
            bit_depth: 16,
            channels: 1
          },
          noise_suppression: true,
          echo_cancellation: true,
          auto_gain_control: true,
          sample_rate: 44100,
          bit_depth: 16,
          channels: 1
        }
      },
      voice_settings: {
        stability: 0.5,
        similarity_boost: 0.5,
        style: 0.0,
        use_speaker_boost: true,
        optimize_streaming_latency: 3,
        output_format: 'pcm_44100'
      },
      turn_taking_settings: {
        enabled: true,
        model_type: 'advanced',
        sensitivity: 0.7,
        max_silence_ms: 1000,
        min_speech_duration_ms: 300,
        interruption_threshold: 0.5,
        turn_detection_method: 'hybrid'
      },
      language: 'en',
      context_window: 4000,
      max_tokens: 500,
      temperature: 0.7,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
      is_public: false,
      tags: [],
      metadata: {}
    };
    
    return await store.createAgent({ ...defaultConfig, ...config });
  }, [store]);
  
  return {
    agents: store.agents.list,
    selectedAgent: store.agents.selectedAgent,
    isLoading: store.agents.isLoading,
    error: store.agents.error,
    
    loadAgents: store.loadAgents,
    selectAgent: store.selectAgent,
    createAgent: createAgentWithDefaults,
    updateAgent: store.updateAgent,
    deleteAgent: store.deleteAgent,
    
    clearError: () => store.clearError('agents')
  };
}