"use client"

import { useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'

interface ContentActionOptions {
  onSuccess?: (action: string, contentId: string) => void
  onError?: (action: string, error: Error, contentId: string) => void
}

export function useContentActions(options: ContentActionOptions = {}) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  const updateContent = useCallback(async (
    contentId: string, 
    data: Record<string, any>
  ) => {
    try {
      setIsLoading(true)
      setError(null)
      
      const response = await fetch(`/api/content/${contentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })
      
      if (!response.ok) {
        throw new Error(`Failed to update content: ${response.statusText}`)
      }
      
      const updatedContent = await response.json()
      
      if (options.onSuccess) {
        options.onSuccess('update', contentId)
      }
      
      return updatedContent
    } catch (error) {
      setError(error as Error)
      
      if (options.onError) {
        options.onError('update', error as Error, contentId)
      }
      
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])
  
  const deleteContent = useCallback(async (contentId: string) => {
    try {
      setIsLoading(true)
      setError(null)
      
      const response = await fetch(`/api/content/${contentId}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) {
        throw new Error(`Failed to delete content: ${response.statusText}`)
      }
      
      if (options.onSuccess) {
        options.onSuccess('delete', contentId)
      }
      
      return true
    } catch (error) {
      setError(error as Error)
      
      if (options.onError) {
        options.onError('delete', error as Error, contentId)
      }
      
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])
  
  const duplicateContent = useCallback(async (contentId: string) => {
    try {
      setIsLoading(true)
      setError(null)
      
      // First get the content to duplicate
      const getResponse = await fetch(`/api/content/${contentId}`)
      
      if (!getResponse.ok) {
        throw new Error(`Failed to fetch content for duplication: ${getResponse.statusText}`)
      }
      
      const content = await getResponse.json()
      
      // Create new content item based on the original
      const duplicateData = {
        title: `${content.title} (Copy)`,
        description: content.description,
        type: content.type,
        format: content.format,
        fileUrl: content.fileUrl,
        thumbnailUrl: content.thumbnailUrl,
        duration: content.duration,
        dimensions: content.dimensions,
        manualTags: content.manualTags?.map((tag: any) => tag.tag),
        status: 'draft',
      }
      
      const createResponse = await fetch('/api/content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(duplicateData),
      })
      
      if (!createResponse.ok) {
        throw new Error(`Failed to duplicate content: ${createResponse.statusText}`)
      }
      
      const newContent = await createResponse.json()
      
      if (options.onSuccess) {
        options.onSuccess('duplicate', newContent.id)
      }
      
      return newContent
    } catch (error) {
      setError(error as Error)
      
      if (options.onError) {
        options.onError('duplicate', error as Error, contentId)
      }
      
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])
  
  const shareContent = useCallback(async (
    contentId: string, 
    shareType: 'email' | 'link' | 'social' = 'link'
  ) => {
    try {
      setIsLoading(true)
      setError(null)
      
      // In a real application, this would interact with a sharing API
      // For now, we'll just simulate success after a delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const shareUrl = `${window.location.origin}/shared/${contentId}`
      
      switch (shareType) {
        case 'email':
          // Simulate email sharing
          console.log(`Email sharing: ${shareUrl}`)
          break
        case 'social':
          // Simulate social sharing
          console.log(`Social sharing: ${shareUrl}`)
          break
        case 'link':
        default:
          // Copy to clipboard
          await navigator.clipboard.writeText(shareUrl)
          break
      }
      
      if (options.onSuccess) {
        options.onSuccess('share', contentId)
      }
      
      return shareUrl
    } catch (error) {
      setError(error as Error)
      
      if (options.onError) {
        options.onError('share', error as Error, contentId)
      }
      
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])
  
  return {
    updateContent,
    deleteContent,
    duplicateContent,
    shareContent,
    isLoading,
    error,
  }
} 