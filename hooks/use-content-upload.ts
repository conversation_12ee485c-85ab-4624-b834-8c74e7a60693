"use client"

import { useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'

interface UploadProgressItem {
  id: string
  name: string
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  errorMessage?: string
}

interface UploadOptions {
  onSuccess?: (contentId: string) => void
  onError?: (error: Error, file: File) => void
  autoNavigate?: boolean
}

export function useContentUpload(options: UploadOptions = {}) {
  const router = useRouter()
  const [uploadingFiles, setUploadingFiles] = useState<UploadProgressItem[]>([])
  const [isUploading, setIsUploading] = useState(false)
  
  // Simulate file upload
  const uploadFile = useCallback(async (file: File): Promise<string> => {
    // In a real implementation, this would upload to a storage service like S3
    // and return the file URL
    return new Promise((resolve, reject) => {
      const fileId = `temp-${Date.now()}-${file.name.replace(/[^a-z0-9]/gi, '-')}`
      
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUploadingFiles(prev => 
          prev.map(item => 
            item.id === fileId
              ? { 
                  ...item, 
                  progress: Math.min(item.progress + 10, 90) 
                }
              : item
          )
        )
      }, 300)
      
      // Simulate upload completion
      setTimeout(() => {
        clearInterval(progressInterval)
        
        // 10% chance of error for demo purposes
        if (Math.random() < 0.1) {
          setUploadingFiles(prev => 
            prev.map(item => 
              item.id === fileId
                ? { 
                    ...item, 
                    status: 'error',
                    errorMessage: 'Upload failed. Please try again.' 
                  }
                : item
            )
          )
          reject(new Error('Upload failed'))
          return
        }
        
        setUploadingFiles(prev => 
          prev.map(item => 
            item.id === fileId
              ? { 
                  ...item, 
                  progress: 100,
                  status: 'success' 
                }
              : item
          )
        )
        
        // Return a fake URL
        resolve(`https://storage.example.com/${fileId}`)
      }, 2000)
      
      // Add to uploading files
      setUploadingFiles(prev => [
        ...prev, 
        { 
          id: fileId, 
          name: file.name, 
          progress: 0, 
          status: 'uploading' 
        }
      ])
    })
  }, [])
  
  const uploadContent = useCallback(async (
    files: File[], 
    metadata: Record<string, any>
  ) => {
    try {
      setIsUploading(true)
      
      // Upload each file
      const filePromises = files.map(uploadFile)
      const fileUrls = await Promise.all(filePromises)
      
      // Create a content item for each file
      const contentPromises = fileUrls.map(async (fileUrl, index) => {
        const file = files[index]
        const fileType = file.type.split('/')[0] // e.g. 'audio', 'video', 'image'
        
        const contentData = {
          title: metadata.title || file.name,
          description: metadata.description || '',
          type: fileType,
          format: file.type,
          fileUrl,
          thumbnailUrl: metadata.thumbnailUrl,
          duration: metadata.duration,
          dimensions: metadata.dimensions,
          manualTags: metadata.tags || [],
          status: metadata.status || 'draft'
        }
        
        const response = await fetch('/api/content', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(contentData),
        })
        
        if (!response.ok) {
          throw new Error(`Failed to create content: ${response.statusText}`)
        }
        
        const content = await response.json()
        return content
      })
      
      const contentItems = await Promise.all(contentPromises)
      
      // Call success callback if provided
      if (options.onSuccess && contentItems.length > 0) {
        options.onSuccess(contentItems[0].id)
      }
      
      // Navigate to content item if auto-navigate is enabled
      if (options.autoNavigate && contentItems.length > 0) {
        router.push(`/dashboard/content-hub/${contentItems[0].id}`)
      }
      
      return contentItems
    } catch (error) {
      console.error('Error uploading content:', error)
      if (options.onError) {
        options.onError(error as Error, files[0])
      }
      throw error
    } finally {
      setIsUploading(false)
    }
  }, [uploadFile, router, options])
  
  const resetUploadProgress = useCallback(() => {
    setUploadingFiles([])
  }, [])
  
  return {
    uploadContent,
    uploadingFiles,
    isUploading,
    resetUploadProgress,
  }
} 