import { useEffect, useState } from 'react';
import { useSidebarState } from './use-sidebar-state';

const MOBILE_BREAKPOINT = 768;

export function useSidebarResponsive() {
  const { setMobile, isMobile } = useSidebarState();
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);

    const handleResize = () => {
      const isMobileView = window.innerWidth < MOBILE_BREAKPOINT;
      setMobile(isMobileView);
    };

    // Initial check
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, [setMobile]);

  return {
    isMobile,
    isMounted,
  };
} 