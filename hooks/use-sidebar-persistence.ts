import { useEffect } from 'react';
import { useSidebarState } from './use-sidebar-state';
import { useSidebarPreferences } from './use-sidebar-preferences';

const SIDEBAR_COOKIE_NAME = 'sidebar_state';
const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7; // 7 days

export function useSidebarPersistence() {
  const { isOpen, setOpen } = useSidebarState();
  const { preferences, updatePreferences } = useSidebarPreferences();

  useEffect(() => {
    // Load initial state from cookie
    const cookieValue = document.cookie
      .split('; ')
      .find(row => row.startsWith(`${SIDEBAR_COOKIE_NAME}=`))
      ?.split('=')[1];

    if (cookieValue) {
      const savedState = cookieValue === 'true';
      setOpen(savedState);
    }
  }, [setOpen]);

  useEffect(() => {
    // Save state to cookie
    document.cookie = `${SIDEBAR_COOKIE_NAME}=${isOpen}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;
    
    // Update preferences in database
    if (preferences?.sidebarOpen !== isOpen) {
      updatePreferences({ sidebarOpen: isOpen });
    }
  }, [isOpen, preferences, updatePreferences]);
} 