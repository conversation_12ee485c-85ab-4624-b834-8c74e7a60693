"use client";

import { useSession, signIn, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useState } from "react";

export function useAuth() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [loginError, setLoginError] = useState<string | null>(null);
  const [isLoadingState, setIsLoadingState] = useState(false);

  const isAuthenticated = status === "authenticated";
  const isLoading = status === "loading" || isLoadingState;
  const user = session?.user;

  const login = async (email: string, password: string, callbackUrl?: string) => {
    setIsLoadingState(true);
    setLoginError(null);

    try {
      const result = await signIn("credentials", {
        redirect: false,
        email,
        password,
      });

      if (result?.error) {
        setLoginError("Invalid email or password");
        setIsLoadingState(false);
        return false;
      }

      if (callbackUrl) {
        router.push(callbackUrl);
      }
      
      return true;
    } catch (error) {
      setLoginError("An error occurred during login");
      setIsLoadingState(false);
      return false;
    } finally {
      setIsLoadingState(false);
    }
  };

  const logout = async () => {
    await signOut({ redirect: true, callbackUrl: "/login" });
  };

  const register = async (
    name: string,
    email: string,
    password: string
  ): Promise<{ success: boolean; error?: string }> => {
    setIsLoadingState(true);
    
    try {
      const response = await fetch("/api/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          email,
          password,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.error || "Registration failed",
        };
      }

      return { success: true };
    } catch (error) {
      let errorMessage = "An unexpected error occurred";
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setIsLoadingState(false);
    }
  };

  return {
    user,
    isAuthenticated,
    isLoading,
    loginError,
    login,
    logout,
    register,
  };
} 