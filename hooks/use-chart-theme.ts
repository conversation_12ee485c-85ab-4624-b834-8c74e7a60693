"use client"

import { useTheme } from 'next-themes'
import { useMemo } from 'react'

interface ChartTheme {
  colors: string[]
  gridColor: string
  textColor: string
  tooltipBackground: string
  tooltipText: string
  areaOpacity: number
}

export function useChartTheme() {
  const { theme } = useTheme()

  const chartTheme = useMemo<ChartTheme>(() => {
    const isDark = theme === 'dark'

    return {
      colors: [
        'hsl(var(--primary))',
        'hsl(var(--secondary))',
        'hsl(var(--accent))',
        'hsl(var(--muted))',
        '#10B981',
        '#6366F1',
        '#F59E0B',
        '#EC4899',
      ],
      gridColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
      textColor: isDark ? 'hsl(var(--foreground))' : 'hsl(var(--foreground))',
      tooltipBackground: isDark ? 'hsl(var(--background))' : 'hsl(var(--background))',
      tooltipText: isDark ? 'hsl(var(--foreground))' : 'hsl(var(--foreground))',
      areaOpacity: isDark ? 0.2 : 0.1,
    }
  }, [theme])

  const getChartStyle = useMemo(
    () => ({
      style: {
        fontSize: '12px',
        fontFamily: 'var(--font-sans)',
      },
      cartesianGrid: {
        strokeDasharray: '3 3',
        stroke: chartTheme.gridColor,
      },
      xAxis: {
        tick: {
          fill: chartTheme.textColor,
        },
        axisLine: {
          stroke: chartTheme.gridColor,
        },
      },
      yAxis: {
        tick: {
          fill: chartTheme.textColor,
        },
        axisLine: {
          stroke: chartTheme.gridColor,
        },
      },
      tooltip: {
        contentStyle: {
          backgroundColor: chartTheme.tooltipBackground,
          border: 'none',
          borderRadius: '6px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          color: chartTheme.tooltipText,
        },
      },
      legend: {
        textStyle: {
          color: chartTheme.textColor,
        },
      },
    }),
    [chartTheme]
  )

  const getColor = (index: number) => chartTheme.colors[index % chartTheme.colors.length]

  return {
    chartTheme,
    getChartStyle,
    getColor,
  }
} 