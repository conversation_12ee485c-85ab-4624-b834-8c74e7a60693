"use client"

import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'

export type TimeRange = '7d' | '30d' | '90d' | '1y'
export type ChartType = 'area' | 'bar' | 'line' | 'pie' | 'radar' | 'scatter'

interface ChartData {
  date: string
  [key: string]: any
}

interface ChartMetric {
  label: string
  value: number
  change: number
}

interface UseChartDataProps {
  endpoint: string
  initialTimeRange?: TimeRange
  initialChartType?: ChartType
  refreshInterval?: number
}

export function useChartData({
  endpoint,
  initialTimeRange = '30d',
  initialChartType = 'area',
  refreshInterval = 30000, // 30 seconds
}: UseChartDataProps) {
  const { data: session } = useSession()
  const [data, setData] = useState<ChartData[]>([])
  const [metrics, setMetrics] = useState<ChartMetric[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState<TimeRange>(initialTimeRange)
  const [chartType, setChartType] = useState<ChartType>(initialChartType)

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/${endpoint}?timeRange=${timeRange}&chartType=${chartType}`)
      if (!response.ok) {
        throw new Error('Failed to fetch data')
      }
      const result = await response.json()
      setData(result.data)
      setMetrics(result.metrics)
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }, [endpoint, timeRange, chartType])

  useEffect(() => {
    fetchData()
    const interval = setInterval(fetchData, refreshInterval)
    return () => clearInterval(interval)
  }, [fetchData, refreshInterval])

  const handleTimeRangeChange = useCallback((newTimeRange: TimeRange) => {
    setTimeRange(newTimeRange)
  }, [])

  const handleChartTypeChange = useCallback((newChartType: ChartType) => {
    setChartType(newChartType)
  }, [])

  const handleRefresh = useCallback(() => {
    fetchData()
  }, [fetchData])

  const handleExport = useCallback(async () => {
    try {
      const response = await fetch(`/api/${endpoint}/export?timeRange=${timeRange}`)
      if (!response.ok) {
        throw new Error('Failed to export data')
      }
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${endpoint}-data-${timeRange}.csv`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to export data')
    }
  }, [endpoint, timeRange])

  return {
    data,
    metrics,
    loading,
    error,
    timeRange,
    chartType,
    handleTimeRangeChange,
    handleChartTypeChange,
    handleRefresh,
    handleExport,
  }
} 