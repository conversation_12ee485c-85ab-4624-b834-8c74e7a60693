"use client"

import { useMemo, useState, useCallback } from 'react'

interface FilterConfig {
  field: string
  operator: 'equals' | 'contains' | 'greaterThan' | 'lessThan' | 'between'
  value: any
  secondValue?: any // For 'between' operator
}

interface SortConfig {
  field: string
  direction: 'asc' | 'desc'
}

interface DataPoint {
  [key: string]: any
}

interface UseChartFiltersProps {
  data: DataPoint[]
  initialFilters?: FilterConfig[]
  initialSort?: SortConfig
}

export function useChartFilters({
  data,
  initialFilters = [],
  initialSort,
}: UseChartFiltersProps) {
  const [filters, setFilters] = useState<FilterConfig[]>(initialFilters)
  const [sortConfig, setSortConfig] = useState<SortConfig | undefined>(initialSort)

  const applyFilter = useCallback((item: DataPoint, filter: FilterConfig) => {
    const value = item[filter.field]
    switch (filter.operator) {
      case 'equals':
        return value === filter.value
      case 'contains':
        return String(value).toLowerCase().includes(String(filter.value).toLowerCase())
      case 'greaterThan':
        return Number(value) > Number(filter.value)
      case 'lessThan':
        return Number(value) < Number(filter.value)
      case 'between':
        return (
          Number(value) >= Number(filter.value) &&
          Number(value) <= Number(filter.secondValue)
        )
      default:
        return true
    }
  }, [])

  const filteredData = useMemo(() => {
    let result = [...data]

    // Apply filters
    filters.forEach(filter => {
      result = result.filter(item => applyFilter(item, filter))
    })

    // Apply sorting
    if (sortConfig) {
      result.sort((a, b) => {
        const aValue = a[sortConfig.field]
        const bValue = b[sortConfig.field]

        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortConfig.direction === 'asc'
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue)
        }

        const numA = Number(aValue)
        const numB = Number(bValue)

        return sortConfig.direction === 'asc' ? numA - numB : numB - numA
      })
    }

    return result
  }, [data, filters, sortConfig, applyFilter])

  const addFilter = useCallback((filter: FilterConfig) => {
    setFilters(prev => [...prev, filter])
  }, [])

  const removeFilter = useCallback((index: number) => {
    setFilters(prev => prev.filter((_, i) => i !== index))
  }, [])

  const updateFilter = useCallback((index: number, filter: FilterConfig) => {
    setFilters(prev => prev.map((f, i) => (i === index ? filter : f)))
  }, [])

  const clearFilters = useCallback(() => {
    setFilters([])
  }, [])

  const updateSort = useCallback((field: string, direction: 'asc' | 'desc') => {
    setSortConfig({ field, direction })
  }, [])

  const clearSort = useCallback(() => {
    setSortConfig(undefined)
  }, [])

  return {
    filteredData,
    filters,
    sortConfig,
    addFilter,
    removeFilter,
    updateFilter,
    clearFilters,
    updateSort,
    clearSort,
  }
} 