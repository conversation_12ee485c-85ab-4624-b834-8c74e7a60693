import { usePathname } from 'next/navigation';
import { sidebarConfig } from '@/config/sidebar';
import { SidebarMenuItem } from '@/types/sidebar';

export function useSidebarMenu() {
  const pathname = usePathname();

  const getActiveItems = (items: SidebarMenuItem[]): SidebarMenuItem[] => {
    return items.map(item => ({
      ...item,
      isActive: pathname === item.href,
      children: item.children ? getActiveItems(item.children) : undefined,
    }));
  };

  const menuItems = sidebarConfig.map(section => ({
    ...section,
    items: getActiveItems(section.items),
  }));

  const activeItem = menuItems
    .flatMap(section => section.items)
    .find(item => item.isActive);

  return {
    menuItems,
    activeItem,
    pathname,
  };
} 