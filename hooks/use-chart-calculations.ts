"use client"

import { useMemo } from 'react'

interface DataPoint {
  [key: string]: any
}

interface CalculationOptions {
  data: DataPoint[]
  metrics: string[]
  timeRange: string
}

export function useChartCalculations({ data, metrics, timeRange }: CalculationOptions) {
  const calculateGrowthRate = useMemo(() => {
    return (metric: string) => {
      if (data.length < 2) return 0
      const firstValue = data[0][metric] || 0
      const lastValue = data[data.length - 1][metric] || 0
      return firstValue === 0 ? 0 : ((lastValue - firstValue) / firstValue) * 100
    }
  }, [data])

  const calculateMovingAverage = useMemo(() => {
    return (metric: string, window = 7) => {
      return data.map((_, index) => {
        const start = Math.max(0, index - window + 1)
        const values = data.slice(start, index + 1).map(d => d[metric] || 0)
        const sum = values.reduce((acc, val) => acc + val, 0)
        return {
          ...data[index],
          [metric]: sum / values.length,
        }
      })
    }
  }, [data])

  const calculateTotals = useMemo(() => {
    return metrics.reduce((acc, metric) => {
      const total = data.reduce((sum, point) => sum + (point[metric] || 0), 0)
      return { ...acc, [metric]: total }
    }, {})
  }, [data, metrics])

  const calculatePercentageChange = useMemo(() => {
    return metrics.reduce((acc, metric) => {
      const values = data.map(point => point[metric] || 0)
      const firstValue = values[0]
      const lastValue = values[values.length - 1]
      const change = firstValue === 0 ? 0 : ((lastValue - firstValue) / firstValue) * 100
      return { ...acc, [metric]: change }
    }, {})
  }, [data, metrics])

  const getTimeRangeLabel = useMemo(() => {
    const ranges = {
      '7d': 'Last 7 Days',
      '30d': 'Last 30 Days',
      '90d': 'Last 90 Days',
      '1y': 'Last Year',
    }
    return ranges[timeRange as keyof typeof ranges] || timeRange
  }, [timeRange])

  const formatValue = (value: number, type: 'number' | 'percentage' | 'currency' = 'number') => {
    switch (type) {
      case 'percentage':
        return `${value.toFixed(1)}%`
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
        }).format(value)
      default:
        return new Intl.NumberFormat('en-US').format(value)
    }
  }

  return {
    calculateGrowthRate,
    calculateMovingAverage,
    calculateTotals,
    calculatePercentageChange,
    getTimeRangeLabel,
    formatValue,
  }
} 