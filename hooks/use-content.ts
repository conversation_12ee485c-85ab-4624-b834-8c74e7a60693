"use client"

import { useState, useEffect, useCallback } from 'react'

type ContentType = 'audio' | 'video' | 'image' | 'document' | 'all'
type ContentStatus = 'draft' | 'published' | 'scheduled' | 'archived' | 'all'

interface ContentTag {
  id: string
  tag: string
  contentId: string
}

interface ContentItem {
  id: string
  title: string
  description?: string
  type: string
  format: string
  fileUrl: string
  thumbnailUrl?: string
  duration?: string
  dimensions?: string
  status: ContentStatus
  createdAt: string
  updatedAt: string
  userId: string
  manualTags?: ContentTag[]
  aiTags?: ContentTag[]
}

interface ContentFilters {
  type?: ContentType
  status?: ContentStatus
  search?: string
  dateFrom?: string
  dateTo?: string
  page?: number
  limit?: number
}

interface ContentPagination {
  page: number
  limit: number
  totalCount: number
  totalPages: number
}

interface ContentResponse {
  content: ContentItem[]
  pagination: ContentPagination
}

export function useContent(initialFilters: ContentFilters = {}) {
  const [content, setContent] = useState<ContentItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const [filters, setFilters] = useState<ContentFilters>(initialFilters)
  const [pagination, setPagination] = useState<ContentPagination>({
    page: initialFilters.page || 1,
    limit: initialFilters.limit || 20,
    totalCount: 0,
    totalPages: 0,
  })

  const fetchContent = useCallback(async (filters: ContentFilters = {}) => {
    try {
      setIsLoading(true)
      setError(null)
      
      // Build query params
      const params = new URLSearchParams()
      if (filters.type && filters.type !== 'all') params.append('type', filters.type)
      if (filters.status && filters.status !== 'all') params.append('status', filters.status)
      if (filters.search) params.append('query', filters.search)
      if (filters.dateFrom) params.append('dateFrom', filters.dateFrom)
      if (filters.dateTo) params.append('dateTo', filters.dateTo)
      if (filters.page) params.append('page', String(filters.page))
      if (filters.limit) params.append('limit', String(filters.limit))
      
      const response = await fetch(`/api/content?${params.toString()}`)
      
      if (!response.ok) {
        throw new Error(`Error fetching content: ${response.statusText}`)
      }
      
      const data: ContentResponse = await response.json()
      setContent(data.content)
      setPagination(data.pagination)
    } catch (err) {
      setError(err as Error)
      console.error('Error fetching content:', err)
    } finally {
      setIsLoading(false)
    }
  }, [])
  
  const updateFilters = useCallback((newFilters: Partial<ContentFilters>) => {
    setFilters(prev => {
      // Reset to page 1 if filters other than page changed
      const shouldResetPage = Object.keys(newFilters).some(key => key !== 'page')
      
      return {
        ...prev, 
        ...newFilters, 
        ...(shouldResetPage ? { page: 1 } : {})
      }
    })
  }, [])
  
  const refreshContent = useCallback(() => {
    fetchContent(filters)
  }, [fetchContent, filters])
  
  useEffect(() => {
    fetchContent(filters)
  }, [fetchContent, filters])
  
  return {
    content,
    isLoading,
    error,
    filters,
    pagination,
    updateFilters,
    refreshContent,
  }
} 