import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface SidebarState {
  isOpen: boolean
  isMobile: boolean
  setOpen: (open: boolean) => void
  setMobile: (isMobile: boolean) => void
  toggle: () => void
}

export const useSidebarState = create<SidebarState>()(
  persist(
    (set) => ({
      isOpen: true,
      isMobile: false,
      setOpen: (open) => set({ isOpen: open }),
      setMobile: (isMobile) => set({ isMobile }),
      toggle: () => set((state) => ({ isOpen: !state.isOpen })),
    }),
    {
      name: 'sidebar-storage',
    }
  )
) 