// ElevenLabs React Hooks
// Custom hooks for ElevenLabs functionality

import { useCallback, useEffect, useState } from 'react';
import { useElevenLabsStore } from '@/lib/elevenlabs/stores/elevenlabs-store';
import { useVoiceStore } from '@/lib/elevenlabs/stores/voice-store';
import { ElevenLabsService } from '@/lib/elevenlabs/services/elevenlabs-service';
import { ElevenLabsStreamingService } from '@/lib/elevenlabs/services/streaming-service';
import { ElevenLabsVoiceService } from '@/lib/elevenlabs/services/voice-service';
import { ElevenLabsHistoryService } from '@/lib/elevenlabs/services/history-service';
import { ElevenLabsUserService } from '@/lib/elevenlabs/services/user-service';
import { validateApiKey } from '@/lib/elevenlabs/utils';
import type { 
  ElevenLabsVoice, 
  ElevenLabsModel, 
  TTSRequest, 
  VoiceCloneRequest, 
  VoiceDesignRequest,
  HistoryItem,
  UserSubscription
} from '@/lib/elevenlabs/types';

/**
 * Main ElevenLabs hook
 */
export function useElevenLabs() {
  const {
    apiKey,
    setApi<PERSON>ey,
    voices,
    selectedVoice,
    models,
    selectedModel,
    generation,
    voiceClone,
    voiceDesign,
    history,
    user,
    loadVoices,
    loadModels,
    selectVoice,
    selectModel,
    generateSpeech,
    streamSpeech,
    cloneVoice,
    designVoice,
    loadHistory,
    loadUser,
    clearError,
    reset,
  } = useElevenLabsStore();

  // Initialize services when API key changes
  const [service, setService] = useState<ElevenLabsService | null>(null);
  const [streamingService, setStreamingService] = useState<ElevenLabsStreamingService | null>(null);
  const [voiceService, setVoiceService] = useState<ElevenLabsVoiceService | null>(null);
  const [historyService, setHistoryService] = useState<ElevenLabsHistoryService | null>(null);
  const [userService, setUserService] = useState<ElevenLabsUserService | null>(null);

  useEffect(() => {
    if (apiKey && validateApiKey(apiKey)) {
      setService(new ElevenLabsService(apiKey));
      setStreamingService(new ElevenLabsStreamingService(apiKey));
      setVoiceService(new ElevenLabsVoiceService(apiKey));
      setHistoryService(new ElevenLabsHistoryService(apiKey));
      setUserService(new ElevenLabsUserService(apiKey));
    } else {
      setService(null);
      setStreamingService(null);
      setVoiceService(null);
      setHistoryService(null);
      setUserService(null);
    }
  }, [apiKey]);

  // Auto-load data when API key is set
  useEffect(() => {
    if (service && voices.length === 0) {
      loadVoices();
    }
  }, [service, voices.length, loadVoices]);

  useEffect(() => {
    if (service && models.length === 0) {
      loadModels();
    }
  }, [service, models.length, loadModels]);

  const isConfigured = Boolean(apiKey && validateApiKey(apiKey));

  return {
    // Configuration
    apiKey,
    setApiKey,
    isConfigured,
    
    // Services
    service,
    streamingService,
    voiceService,
    historyService,
    userService,
    
    // Data
    voices,
    selectedVoice,
    models,
    selectedModel,
    generation,
    voiceClone,
    voiceDesign,
    history,
    user,
    
    // Actions
    loadVoices,
    loadModels,
    selectVoice,
    selectModel,
    generateSpeech,
    streamSpeech,
    cloneVoice,
    designVoice,
    loadHistory,
    loadUser,
    clearError,
    reset,
  };
}

/**
 * Text-to-Speech hook
 */
export function useTextToSpeech() {
  const {
    apiKey,
    selectedVoice,
    selectedModel,
    generation,
    generateSpeech,
    streamSpeech,
    clearError,
  } = useElevenLabsStore();

  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null);

  const synthesize = useCallback(async (
    text: string,
    options?: {
      voice?: ElevenLabsVoice;
      model?: ElevenLabsModel;
      settings?: TTSRequest['voice_settings'];
    }
  ) => {
    if (!apiKey || !selectedVoice) return;

    const request: TTSRequest = {
      text,
      voice_id: options?.voice?.voice_id || selectedVoice.voice_id,
      model_id: options?.model?.model_id || selectedModel?.model_id,
      voice_settings: options?.settings || selectedVoice.settings,
    };

    await generateSpeech(request);
  }, [apiKey, selectedVoice, selectedModel, generateSpeech]);

  const play = useCallback(() => {
    if (generation.audioUrl) {
      const audio = new Audio(generation.audioUrl);
      setAudioElement(audio);
      audio.play();
    }
  }, [generation.audioUrl]);

  const pause = useCallback(() => {
    if (audioElement) {
      audioElement.pause();
    }
  }, [audioElement]);

  const stop = useCallback(() => {
    if (audioElement) {
      audioElement.pause();
      audioElement.currentTime = 0;
    }
  }, [audioElement]);

  const download = useCallback((filename?: string) => {
    if (generation.generatedAudio) {
      const blob = new Blob([generation.generatedAudio], { type: 'audio/mpeg' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename || `speech_${Date.now()}.mp3`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  }, [generation.generatedAudio]);

  return {
    generation,
    synthesize,
    play,
    pause,
    stop,
    download,
    clearError: () => clearError('generation'),
  };
}

/**
 * Voice management hook
 */
export function useVoiceManagement() {
  const {
    library,
    comparison,
    favorites,
    settings,
    preview,
    loadVoiceLibrary,
    searchVoices,
    filterVoices,
    sortVoices,
    clearFilters,
    addToComparison,
    removeFromComparison,
    compareVoices,
    clearComparison,
    addToFavorites,
    removeFromFavorites,
    loadFavorites,
    previewVoice,
    stopPreview,
    clearError,
  } = useVoiceStore();

  const { apiKey } = useElevenLabsStore();

  // Auto-load voice library when API key is available
  useEffect(() => {
    if (apiKey && library.voices.length === 0) {
      loadVoiceLibrary(apiKey);
    }
  }, [apiKey, library.voices.length, loadVoiceLibrary]);

  // Auto-load favorites when API key is available
  useEffect(() => {
    if (apiKey && favorites.voiceIds.length > 0 && favorites.voices.length === 0) {
      loadFavorites(apiKey);
    }
  }, [apiKey, favorites.voiceIds.length, favorites.voices.length, loadFavorites]);

  const search = useCallback((query: string) => {
    searchVoices(query);
  }, [searchVoices]);

  const filter = useCallback((filters: any) => {
    filterVoices(filters);
  }, [filterVoices]);

  const sort = useCallback((field: any, direction?: any) => {
    sortVoices(field, direction);
  }, [sortVoices]);

  const toggleFavorite = useCallback((voiceId: string) => {
    if (favorites.voiceIds.includes(voiceId)) {
      removeFromFavorites(voiceId);
    } else {
      addToFavorites(voiceId);
    }
  }, [favorites.voiceIds, addToFavorites, removeFromFavorites]);

  const toggleComparison = useCallback((voice: ElevenLabsVoice) => {
    if (comparison.selectedVoices.find(v => v.voice_id === voice.voice_id)) {
      removeFromComparison(voice.voice_id);
    } else {
      addToComparison(voice);
    }
  }, [comparison.selectedVoices, addToComparison, removeFromComparison]);

  const startComparison = useCallback(async () => {
    if (apiKey) {
      await compareVoices(apiKey);
    }
  }, [apiKey, compareVoices]);

  const startPreview = useCallback(async (voice: ElevenLabsVoice, text?: string) => {
    if (apiKey) {
      await previewVoice(apiKey, voice, text);
    }
  }, [apiKey, previewVoice]);

  return {
    library,
    comparison,
    favorites,
    settings,
    preview,
    search,
    filter,
    sort,
    clearFilters,
    toggleFavorite,
    toggleComparison,
    startComparison,
    clearComparison,
    startPreview,
    stopPreview,
    clearError,
  };
}

/**
 * Voice cloning hook
 */
export function useVoiceCloning() {
  const { voiceClone, cloneVoice, clearError } = useElevenLabsStore();

  const clone = useCallback(async (request: VoiceCloneRequest) => {
    await cloneVoice(request);
  }, [cloneVoice]);

  return {
    voiceClone,
    clone,
    clearError: () => clearError('voiceClone'),
  };
}

/**
 * Voice design hook
 */
export function useVoiceDesign() {
  const { voiceDesign, designVoice, clearError } = useElevenLabsStore();

  const design = useCallback(async (request: VoiceDesignRequest) => {
    await designVoice(request);
  }, [designVoice]);

  return {
    voiceDesign,
    design,
    clearError: () => clearError('voiceDesign'),
  };
}

/**
 * History management hook
 */
export function useHistoryManagement() {
  const { history, loadHistory, clearError } = useElevenLabsStore();
  const { apiKey } = useElevenLabsStore();

  const [historyService, setHistoryService] = useState<ElevenLabsHistoryService | null>(null);

  useEffect(() => {
    if (apiKey && validateApiKey(apiKey)) {
      setHistoryService(new ElevenLabsHistoryService(apiKey));
    } else {
      setHistoryService(null);
    }
  }, [apiKey]);

  const loadPage = useCallback(async (page: number, pageSize?: number) => {
    await loadHistory(page, pageSize);
  }, [loadHistory]);

  const searchHistory = useCallback(async (query: any) => {
    if (!historyService) return { history: [], has_more: false, total_count: 0 };
    return historyService.searchHistory(query);
  }, [historyService]);

  const deleteItem = useCallback(async (itemId: string) => {
    if (!historyService) return;
    await historyService.deleteHistoryItem(itemId);
    // Reload current page
    await loadHistory(history.currentPage);
  }, [historyService, history.currentPage, loadHistory]);

  const downloadItem = useCallback(async (itemId: string, filename?: string) => {
    if (!historyService) return;
    await historyService.downloadHistoryItem(itemId, filename);
  }, [historyService]);

  const getAnalytics = useCallback(async (timeRange?: any) => {
    if (!historyService) return null;
    return historyService.getHistoryAnalytics(timeRange);
  }, [historyService]);

  const exportHistory = useCallback(async (format?: 'json' | 'csv', filters?: any) => {
    if (!historyService) return '';
    return historyService.exportHistory(format, filters);
  }, [historyService]);

  return {
    history,
    loadPage,
    searchHistory,
    deleteItem,
    downloadItem,
    getAnalytics,
    exportHistory,
    clearError: () => clearError('history'),
  };
}

/**
 * User management hook
 */
export function useUserManagement() {
  const { user, loadUser, clearError } = useElevenLabsStore();
  const { apiKey } = useElevenLabsStore();

  const [userService, setUserService] = useState<ElevenLabsUserService | null>(null);

  useEffect(() => {
    if (apiKey && validateApiKey(apiKey)) {
      setUserService(new ElevenLabsUserService(apiKey));
    } else {
      setUserService(null);
    }
  }, [apiKey]);

  // Auto-load user data when API key is available
  useEffect(() => {
    if (userService && !user.subscription) {
      loadUser();
    }
  }, [userService, user.subscription, loadUser]);

  const getUsageStatistics = useCallback(async () => {
    if (!userService) return null;
    return userService.getUsageStatistics();
  }, [userService]);

  const checkQuota = useCallback(async (charactersNeeded: number) => {
    if (!userService) return null;
    return userService.checkQuota(charactersNeeded);
  }, [userService]);

  const getBillingInfo = useCallback(async () => {
    if (!userService) return null;
    return userService.getBillingInfo();
  }, [userService]);

  const getAvailablePlans = useCallback(async () => {
    if (!userService) return [];
    return userService.getAvailablePlans();
  }, [userService]);

  const getFeatureAvailability = useCallback(async () => {
    if (!userService) return null;
    return userService.getFeatureAvailability();
  }, [userService]);

  const estimateCost = useCallback(async (text: string) => {
    if (!userService) return null;
    return userService.estimateCost(text);
  }, [userService]);

  return {
    user,
    getUsageStatistics,
    checkQuota,
    getBillingInfo,
    getAvailablePlans,
    getFeatureAvailability,
    estimateCost,
    clearError: () => clearError('user'),
  };
}

/**
 * Streaming hook
 */
export function useStreaming() {
  const { apiKey } = useElevenLabsStore();
  const [streamingService, setStreamingService] = useState<ElevenLabsStreamingService | null>(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamReader, setStreamReader] = useState<ReadableStreamDefaultReader<Uint8Array> | null>(null);

  useEffect(() => {
    if (apiKey && validateApiKey(apiKey)) {
      setStreamingService(new ElevenLabsStreamingService(apiKey));
    } else {
      setStreamingService(null);
    }
  }, [apiKey]);

  const startStream = useCallback(async (
    request: TTSRequest,
    onChunk: (chunk: Uint8Array) => void,
    options?: any
  ) => {
    if (!streamingService) return;

    setIsStreaming(true);
    
    try {
      const stream = await streamingService.streamTextToSpeech(request, options);
      const reader = stream.getReader();
      setStreamReader(reader);

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        if (value) {
          onChunk(value);
        }
      }
    } catch (error) {
      console.error('Streaming error:', error);
    } finally {
      setIsStreaming(false);
      setStreamReader(null);
    }
  }, [streamingService]);

  const stopStream = useCallback(() => {
    if (streamReader) {
      streamReader.cancel();
      setStreamReader(null);
      setIsStreaming(false);
    }
  }, [streamReader]);

  return {
    isStreaming,
    startStream,
    stopStream,
  };
}