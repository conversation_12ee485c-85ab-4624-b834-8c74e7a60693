import { useState } from 'react';

interface Toast {
  title: string;
  description?: string;
  variant?: 'default' | 'destructive';
}

export function useToast() {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const toast = (props: Toast) => {
    // For now, just console.log the toast
    // In a real implementation, you'd show a toast UI component
    console.log('Toast:', props);
    
    // Add to toasts array for potential UI implementation
    setToasts(prev => [...prev, props]);
    
    // Remove after 3 seconds
    setTimeout(() => {
      setToasts(prev => prev.slice(1));
    }, 3000);
  };

  return { toast, toasts };
}