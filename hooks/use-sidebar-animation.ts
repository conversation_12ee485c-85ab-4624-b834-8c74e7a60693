import { useEffect, useState } from 'react';
import { useSidebarState } from './use-sidebar-state';

const ANIMATION_DURATION = 200; // ms

export function useSidebarAnimation() {
  const { isOpen } = useSidebarState();
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    setIsAnimating(true);
    const timer = setTimeout(() => {
      setIsAnimating(false);
    }, ANIMATION_DURATION);

    return () => clearTimeout(timer);
  }, [isOpen]);

  return {
    isAnimating,
    animationDuration: ANIMATION_DURATION,
  };
} 