import { useEffect } from 'react';
import { useSidebarState } from './use-sidebar-state';

const SIDEBAR_KEYBOARD_SHORTCUT = 'b';

export function useSidebarShortcuts() {
  const { toggle } = useSidebarState();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (
        event.key.toLowerCase() === SIDEBAR_KEYBOARD_SHORTCUT &&
        (event.metaKey || event.ctrlKey)
      ) {
        event.preventDefault();
        toggle();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [toggle]);
} 