import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

export type TimeRange = '7d' | '30d' | '90d' | '1y'
export type ChartType = 'area' | 'bar' | 'line' | 'pie' | 'radar' | 'scatter'

interface DashboardData {
  metrics: {
    revenue: number
    growth: number
    audience: number
    engagement: number
    [key: string]: number
  }
  timeSeries: Array<{
    date: string
    [key: string]: number | string
  }>
  distribution: Array<{
    name: string
    value: number
    [key: string]: number | string
  }>
  performance: Array<{
    metric: string
    value: number
    [key: string]: number | string
  }>
}

export function useDashboardData(section: string) {
  const { data: session } = useSession()
  const [timeRange, setTimeRange] = useState<TimeRange>('30d')
  const [chartType, setChartType] = useState<ChartType>('area')
  const [data, setData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/${section}?timeRange=${timeRange}`)
        if (!response.ok) throw new Error('Failed to fetch data')
        const jsonData = await response.json()
        setData(jsonData)
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error'))
      } finally {
        setLoading(false)
      }
    }

    if (session?.user) {
      fetchData()
    }
  }, [section, timeRange, session])

  const updateTimeRange = (range: TimeRange) => {
    setTimeRange(range)
  }

  const updateChartType = (type: ChartType) => {
    setChartType(type)
  }

  const refreshData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/${section}/refresh?timeRange=${timeRange}`)
      if (!response.ok) throw new Error('Failed to refresh data')
      const jsonData = await response.json()
      setData(jsonData)
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'))
    } finally {
      setLoading(false)
    }
  }

  return {
    data,
    loading,
    error,
    timeRange,
    chartType,
    updateTimeRange,
    updateChartType,
    refreshData,
  }
} 