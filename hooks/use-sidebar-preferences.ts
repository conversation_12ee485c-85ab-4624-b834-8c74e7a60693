import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSidebarState } from './use-sidebar-state';

interface SidebarPreferences {
  sidebarOpen: boolean;
  sidebarWidth: number;
  sidebarPosition: 'left' | 'right';
}

async function fetchSidebarPreferences(): Promise<SidebarPreferences> {
  const response = await fetch('/api/sidebar');
  if (!response.ok) {
    throw new Error('Failed to fetch sidebar preferences');
  }
  return response.json();
}

async function updateSidebarPreferences(preferences: Partial<SidebarPreferences>): Promise<SidebarPreferences> {
  const response = await fetch('/api/sidebar', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(preferences),
  });
  
  if (!response.ok) {
    throw new Error('Failed to update sidebar preferences');
  }
  
  return response.json();
}

export function useSidebarPreferences() {
  const queryClient = useQueryClient();
  const { isOpen, setOpen, isMobile, setMobile } = useSidebarState();

  const { data: preferences, isLoading } = useQuery({
    queryKey: ['sidebar-preferences'],
    queryFn: fetchSidebarPreferences,
  });

  const { mutate: updatePreferences } = useMutation({
    mutationFn: updateSidebarPreferences,
    onSuccess: (data) => {
      queryClient.setQueryData(['sidebar-preferences'], data);
      setOpen(data.sidebarOpen);
    },
  });

  const toggleSidebar = () => {
    const newState = !isOpen;
    setOpen(newState);
    updatePreferences({ sidebarOpen: newState });
  };

  return {
    preferences,
    isLoading,
    isOpen,
    isMobile,
    setMobile,
    toggleSidebar,
    updatePreferences,
  };
} 