"use client";

import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { useUIStore } from "@/lib/stores/ui-store";

interface TopLoaderConfig {
  color: string;
  height: number;
  showSpinner: boolean;
  easing: string;
  speed: number;
  shadow: string;
  zIndex: number;
  showAtBottom: boolean;
  template: string;
  initialPosition: number;
  crawl: boolean;
  crawlSpeed: number;
}

interface TopLoaderState {
  config: TopLoaderConfig;
  isLoading: boolean;
  progress: number;
}

export const useTopLoader = () => {
  const { theme, resolvedTheme } = useTheme();
  const [state, setState] = useState<TopLoaderState>({
    config: getDefaultConfig(),
    isLoading: false,
    progress: 0,
  });

  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Get theme-based configuration
  function getDefaultConfig(): TopLoaderConfig {
    return {
      color: "oklch(0.208 0.042 265.755)",
      height: 3,
      showSpinner: false,
      easing: "ease",
      speed: 200,
      shadow: "0 0 10px var(--color-primary), 0 0 5px var(--color-primary)",
      zIndex: 1600,
      showAtBottom: false,
      template: '<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>',
      initialPosition: 0.08,
      crawl: true,
      crawlSpeed: 200,
    };
  }

  // Update configuration based on theme
  useEffect(() => {
    if (!mounted) return;

    const currentTheme = resolvedTheme || theme;
    
    const themeColors = {
      dark: "oklch(0.488 0.243 264.376)",
      light: "oklch(0.208 0.042 265.755)",
    };

    const color = themeColors[currentTheme as keyof typeof themeColors] || themeColors.light;
    
    setState(prev => ({
      ...prev,
      config: {
        ...prev.config,
        color,
        shadow: `0 0 10px ${color}, 0 0 5px ${color}`,
      }
    }));
  }, [theme, resolvedTheme, mounted]);

  // Update configuration
  const updateConfig = (newConfig: Partial<TopLoaderConfig>) => {
    setState(prev => ({
      ...prev,
      config: {
        ...prev.config,
        ...newConfig,
      }
    }));
  };

  // Reset to default configuration
  const resetConfig = () => {
    setState(prev => ({
      ...prev,
      config: getDefaultConfig(),
    }));
  };

  // Set loading state
  const setLoading = (loading: boolean) => {
    setState(prev => ({
      ...prev,
      isLoading: loading,
    }));
  };

  // Set progress
  const setProgress = (progress: number) => {
    setState(prev => ({
      ...prev,
      progress: Math.min(100, Math.max(0, progress)),
    }));
  };

  // Get theme-specific configurations
  const getThemeConfig = () => {
    const currentTheme = resolvedTheme || theme;
    
    const configs = {
      dark: {
        color: "oklch(0.488 0.243 264.376)",
        shadow: "0 0 10px oklch(0.488 0.243 264.376), 0 0 5px oklch(0.488 0.243 264.376)",
      },
      light: {
        color: "oklch(0.208 0.042 265.755)",
        shadow: "0 0 10px oklch(0.208 0.042 265.755), 0 0 5px oklch(0.208 0.042 265.755)",
      },
    };

    return configs[currentTheme as keyof typeof configs] || configs.light;
  };

  return {
    ...state,
    updateConfig,
    resetConfig,
    setLoading,
    setProgress,
    getThemeConfig,
    mounted,
  };
};