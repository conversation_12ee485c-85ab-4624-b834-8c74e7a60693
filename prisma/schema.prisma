// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Main artist/user model
model User {
  id              String   @id @default(cuid())
  name            String?
  email           String?  @unique
  emailVerified   DateTime?
  image           String?
  accounts        Account[]
  sessions        Session[]
  content         Content[]
  audienceData    AudienceData[]
  aiGenerations   AIGeneration[]
  campaigns       Campaign[]
  password        String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  releases        Release[]
  collaborations  Collaboration[]
  insightReports  InsightReport[]
  marketingAssets MarketingAsset[]
  preferences     UserPreferences?

  @@map("users")
}

// Content model (audio, video, images, etc.)
model Content {
  id          String   @id @default(cuid())
  title       String
  description String?
  type        String   // audio, video, image
  format      String   // mp3, wav, mp4, jpg, etc.
  fileUrl     String
  thumbnailUrl String?
  duration    String?  // for audio/video
  dimensions  String?  // for images
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  userId      String
  
  // Relations
  user          User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  releases      ReleaseContent[]
  aiTags        AiContentTag[]
  manualTags    ContentTag[]
  analytics     ContentAnalytics[]

  @@map("contents")
}

// AI-generated tags for content
model AiContentTag {
  id        String   @id @default(cuid())
  tag       String
  confidence Float    // 0-1 confidence score
  contentId String
  createdAt DateTime @default(now())
  
  // Relations
  content   Content  @relation(fields: [contentId], references: [id], onDelete: Cascade)

  @@map("ai_content_tags")
}

// Manual tags for content
model ContentTag {
  id        String   @id @default(cuid())
  tag       String
  contentId String
  createdAt DateTime @default(now())
  
  // Relations
  content   Content  @relation(fields: [contentId], references: [id], onDelete: Cascade)

  @@map("content_tags")
}

// Content performance analytics
model ContentAnalytics {
  id        String   @id @default(cuid())
  contentId String
  platform  String   // Spotify, Apple Music, YouTube, etc.
  streams   Int      @default(0)
  likes     Int      @default(0)
  shares    Int      @default(0)
  comments  Int      @default(0)
  sentiment Float?   // AI-calculated sentiment (-1 to 1)
  date      DateTime @default(now())
  
  // Relations
  content   Content  @relation(fields: [contentId], references: [id], onDelete: Cascade)

  @@map("content_analytics")
}

// Releases (albums, singles, EPs)
model Release {
  id            String   @id @default(cuid())
  title         String
  type          String   // Album, Single, EP
  releaseDate   DateTime?
  isScheduled   Boolean  @default(false)
  description   String?
  artworkUrl    String?
  optimized     Boolean  @default(false) // AI optimized release
  readiness     Int      @default(0) // 0-100% completion
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  userId        String
  
  // Relations
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  contents      ReleaseContent[]
  insights      InsightReport[]

  @@map("releases")
}

// Many-to-many relationship between releases and content
model ReleaseContent {
  id        String   @id @default(cuid())
  releaseId String
  contentId String
  order     Int      @default(0)
  
  // Relations
  release   Release  @relation(fields: [releaseId], references: [id], onDelete: Cascade)
  content   Content  @relation(fields: [contentId], references: [id], onDelete: Cascade)

  @@unique([releaseId, contentId])
  @@map("release_contents")
}

// Collaborations
model Collaboration {
  id            String   @id @default(cuid())
  userId        String   // Owner/inviter
  collaboratorId String  // Collaborator
  role          String   // Producer, Songwriter, etc.
  projectName   String
  status        String   // Pending, Active, Completed
  createdAt     DateTime @default(now())
  
  // Relations
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("collaborations")
}

// AI-generated insights
model InsightReport {
  id            String   @id @default(cuid())
  title         String
  type          String   // Audience, Content, Release
  insights      Json     // Array of insight objects
  createdAt     DateTime @default(now())
  userId        String
  releaseId     String?
  
  // Relations
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  release       Release? @relation(fields: [releaseId], references: [id], onDelete: SetNull)

  @@map("insight_reports")
}

// AI-generated marketing assets
model MarketingAsset {
  id            String   @id @default(cuid())
  assetType     String   // Social post, Email, Press release
  content       String   // The generated content
  prompt        String   // What was used to generate it
  createdAt     DateTime @default(now())
  userId        String
  
  // Relations
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("marketing_assets")
}

// Audience data
model AudienceData {
  id        String   @id @default(cuid())
  userId    String
  platform  String
  metric    String
  value     Json
  date      DateTime @default(now())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id])

  @@index([userId])
  @@map("audience_data")
}

// Royalty transactions
model RoyaltyTransaction {
  id            String   @id @default(cuid())
  platform      String
  amount        Float
  currency      String
  period        String   // Q1 2023, etc.
  contentId     String?
  date          DateTime @default(now())
  
  @@map("royalty_transactions")
}

// Distribution platforms
model DistributionPlatform {
  id            String   @id @default(cuid())
  name          String
  status        String   // Active, Pending, Failed
  contentId     String?
  releaseId     String?
  dateDistributed DateTime?
  
  @@map("distribution_platforms")
}

model UserPreferences {
  id              String   @id @default(cuid())
  userId          String   @unique
  sidebarOpen     Boolean  @default(true)
  sidebarWidth    Int      @default(250)
  sidebarPosition String   @default("left")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_preferences")
}

model Analytics {
  id        String   @id @default(cuid())
  type      String   @unique
  data      Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("analytics")
}

model AIGeneration {
  id              String   @id @default(cuid())
  userId          String
  type            String
  platform        String
  targetAudience  String[]
  keywords        String[]
  tone            String
  length          String
  style           String
  additionalContext String?
  response        Json
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  user            User     @relation(fields: [userId], references: [id])

  @@index([userId])
}

model Campaign {
  id              String   @id @default(cuid())
  userId          String
  name            String
  description     String
  startDate       DateTime
  endDate         DateTime
  status          String   @default("draft")
  budget          Float
  targetAudience  String[]
  goals           String[]
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  user            User     @relation(fields: [userId], references: [id])
  contents        CampaignContent[]

  @@index([userId])
}

model CampaignContent {
  id              String   @id @default(cuid())
  campaignId      String
  title           String
  description     String
  type            String
  platform        String
  targetAudience  String[]
  keywords        String[]
  tone            String
  status          String   @default("draft")
  content         String
  aiSuggestions   String[]
  metrics         Json?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  campaign        Campaign @relation(fields: [campaignId], references: [id])

  @@index([campaignId])
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
} 