const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  // First, get or create a test user
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Test User',
      password: '$2b$10$dqyYw5XovLjpmkYNiRDEWuwKaRAvLaG45fnXE5b3KTccKZcRPka2m' // "password"
    }
  });

  const analyticsData = [
    {
      platform: 'aggregate',
      metric: 'demographics',
      value: {
        ageGroups: [
          { range: '18-24', count: 1200, percentage: 30 },
          { range: '25-34', count: 2000, percentage: 50 },
          { range: '35-44', count: 600, percentage: 15 },
          { range: '45+', count: 200, percentage: 5 }
        ],
        gender: {
          male: 55,
          female: 40,
          other: 5
        },
        totalListeners: 4000
      },
      userId: testUser.id
    },
    {
      platform: 'aggregate',
      metric: 'listening_habits',
      value: {
        averageDailyListenTime: 45,
        peakListeningHours: [
          { hour: 8, count: 800 },
          { hour: 12, count: 1200 },
          { hour: 18, count: 1500 },
          { hour: 22, count: 1000 }
        ],
        favoriteGenres: [
          { genre: 'Pop', percentage: 40 },
          { genre: 'Rock', percentage: 25 },
          { genre: 'Hip Hop', percentage: 20 },
          { genre: 'Electronic', percentage: 15 }
        ],
        deviceTypes: [
          { device: 'Mobile', percentage: 60 },
          { device: 'Desktop', percentage: 25 },
          { device: 'Tablet', percentage: 15 }
        ]
      },
      userId: testUser.id
    },
    {
      platform: 'aggregate',
      metric: 'geographic',
      value: {
        countries: [
          { country: 'United States', listeners: 2000, percentage: 50 },
          { country: 'United Kingdom', listeners: 800, percentage: 20 },
          { country: 'Canada', listeners: 400, percentage: 10 },
          { country: 'Australia', listeners: 400, percentage: 10 },
          { country: 'Others', listeners: 400, percentage: 10 }
        ],
        topCities: [
          { city: 'New York', country: 'United States', listeners: 500 },
          { city: 'London', country: 'United Kingdom', listeners: 300 },
          { city: 'Los Angeles', country: 'United States', listeners: 400 },
          { city: 'Toronto', country: 'Canada', listeners: 200 },
          { city: 'Sydney', country: 'Australia', listeners: 200 }
        ]
      },
      userId: testUser.id
    },
    {
      platform: 'aggregate',
      metric: 'engagement',
      value: {
        averageSessionDuration: 25,
        retentionRate: 75,
        skipRate: 15,
        completionRate: 85,
        interactionRate: 45
      },
      userId: testUser.id
    },
    {
      platform: 'aggregate',
      metric: 'growth',
      value: {
        daily: Array.from({ length: 7 }, (_, i) => ({
          date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          newListeners: Math.floor(Math.random() * 100),
          activeListeners: Math.floor(Math.random() * 500) + 1000
        })),
        weekly: Array.from({ length: 4 }, (_, i) => ({
          week: `Week ${4 - i}`,
          newListeners: Math.floor(Math.random() * 500),
          activeListeners: Math.floor(Math.random() * 2000) + 3000
        })),
        monthly: Array.from({ length: 3 }, (_, i) => ({
          month: new Date(Date.now() - i * 30 * 24 * 60 * 60 * 1000).toLocaleString('default', { month: 'short' }),
          newListeners: Math.floor(Math.random() * 2000),
          activeListeners: Math.floor(Math.random() * 5000) + 8000
        }))
      },
      userId: testUser.id
    },
    {
      platform: 'aggregate',
      metric: 'platforms',
      value: {
        platforms: [
          { platform: 'Spotify', users: 2000, percentage: 50 },
          { platform: 'Apple Music', users: 1000, percentage: 25 },
          { platform: 'YouTube Music', users: 600, percentage: 15 },
          { platform: 'Others', users: 400, percentage: 10 }
        ],
        deviceTypes: [
          { device: 'iOS', users: 1800, percentage: 45 },
          { device: 'Android', users: 1400, percentage: 35 },
          { device: 'Web', users: 800, percentage: 20 }
        ]
      },
      userId: testUser.id
    }
  ];

  for (const data of analyticsData) {
    await prisma.audienceData.create({
      data
    });
  }

  console.log('Analytics data seeded successfully');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 