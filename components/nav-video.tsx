"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { 
  IconVideo, 
  IconDeviceDesktop, 
  IconPalette, 
  IconWand,
  IconMicrophone,
  type Icon 
} from "@tabler/icons-react"

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { useUIStore } from "@/lib/stores/ui-store"
import { cn } from "@/lib/utils"

const videoItems = [
  {
    title: "Music Video Studio",
    url: "/music-video-studio",
    icon: IconVideo,
    description: "Create AI-powered music videos"
  },
  {
    title: "Video Editor",
    url: "/video-editor",
    icon: IconDeviceDesktop,
    description: "Professional video editing tools"
  },
  {
    title: "AI Video Generator",
    url: "/ai-video-generator",
    icon: IconWand,
    description: "Generate videos from text prompts"
  },
  {
    title: "Audio Visualizer",
    url: "/audio-visualizer",
    icon: IconPalet<PERSON>,
    description: "Create reactive audio visualizations"
  },
  {
    title: "Voice Studio",
    url: "/voice-studio",
    icon: IconMicrophone,
    description: "Text-to-speech and voice cloning"
  }
]

export function NavVideo() {
  const router = useRouter()
  const { activeItem, setActiveItem } = useUIStore()

  const handleNavigation = (url: string) => {
    setActiveItem(url)
    router.push(url)
  }

  return (
    <SidebarGroup>
      <SidebarGroupLabel className="text-xs font-semibold text-muted-foreground">
        Video & AI Tools
      </SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu>
          {videoItems.map((item) => (
            <SidebarMenuItem 
              key={item.title}
              className={cn(activeItem === item.url && "bg-primary/10")}
            >
              <SidebarMenuButton 
                tooltip={`${item.title} - ${item.description}`}
                onClick={() => handleNavigation(item.url)}
                isActive={activeItem === item.url}
                className="group"
              >
                <item.icon className="h-4 w-4" />
                <div className="flex flex-col items-start">
                  <span className="text-sm font-medium">{item.title}</span>
                  <span className="text-xs text-muted-foreground group-hover:text-foreground transition-colors">
                    {item.description}
                  </span>
                </div>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}