'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Mic, 
  MicOff, 
  Send, 
  Pause, 
  Play, 
  Volume2, 
  VolumeX,
  Bot,
  User,
  MessageCircle,
  Activity,
  AlertCircle,
  Loader2,
  Settings,
  PhoneCall,
  PhoneOff,
  Clock,
  Zap
} from 'lucide-react';
import { useConversationalAI } from '@/hooks/use-conversational-ai';
import { AudioPlayer } from './audio-player';
import { formatDuration, formatDurationMs } from '@/lib/elevenlabs/utils';
import type { ConversationalMessage } from '@/lib/elevenlabs/types/conversational-ai-types';

interface ConversationalAIChatProps {
  apiKey: string;
  agentId?: string;
  onSessionStart?: (session: any) => void;
  onSessionEnd?: (metrics: any) => void;
  onMessage?: (message: ConversationalMessage) => void;
  className?: string;
}

export function ConversationalAIChat({
  apiKey,
  agentId,
  onSessionStart,
  onSessionEnd,
  onMessage,
  className = ''
}: ConversationalAIChatProps) {
  const {
    agents,
    selectedAgent,
    isLoadingAgents,
    agentsError,
    session,
    sessionStatus,
    isConnected,
    isListening,
    isSpeaking,
    isProcessing,
    sessionMetrics,
    messages,
    unreadCount,
    isRecording,
    isPlaying,
    currentAudioUrl,
    isMuted,
    volume,
    loadAgents,
    selectAgent,
    startSession,
    endSession,
    pauseSession,
    resumeSession,
    sendTextMessage,
    clearMessages,
    markMessagesAsRead,
    startRecording,
    stopRecording,
    playAudio,
    stopAudio,
    setVolume,
    toggleMute,
    interrupt,
    clearError
  } = useConversationalAI({
    apiKey,
    autoLoadAgents: true,
    autoLoadDevices: true,
    enableAnalytics: true,
    onSessionStart,
    onSessionEnd,
    onMessage
  });

  const [textInput, setTextInput] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Auto-select agent if provided
  useEffect(() => {
    if (agentId && agents.length > 0 && !selectedAgent) {
      const agent = agents.find(a => a.agent_id === agentId);
      if (agent) {
        selectAgent(agent);
      }
    }
  }, [agentId, agents, selectedAgent, selectAgent]);

  // Mark messages as read when component is visible
  useEffect(() => {
    if (unreadCount > 0) {
      markMessagesAsRead();
    }
  }, [unreadCount, markMessagesAsRead]);

  const handleStartSession = async () => {
    if (!selectedAgent) return;
    
    try {
      await startSession({
        agentId: selectedAgent.agent_id,
        voiceSettings: selectedAgent.voice_settings,
        turnTaking: selectedAgent.turn_taking_settings,
        audioConfig: {
          enableRecording: true,
          sampleRate: 44100,
          enableNoiseSuppression: true,
          enableEchoCancellation: true,
          enableAutoGainControl: true
        }
      });
    } catch (error) {
      console.error('Failed to start session:', error);
    }
  };

  const handleEndSession = async () => {
    try {
      await endSession();
      clearMessages();
    } catch (error) {
      console.error('Failed to end session:', error);
    }
  };

  const handleSendText = async () => {
    if (!textInput.trim() || !isConnected) return;
    
    try {
      await sendTextMessage(textInput);
      setTextInput('');
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendText();
    }
  };

  const handleRecordingToggle = async () => {
    if (isRecording) {
      await stopRecording();
    } else {
      await startRecording();
    }
  };

  const getStatusColor = (status: typeof sessionStatus) => {
    switch (status) {
      case 'connected':
      case 'active':
        return 'bg-green-500';
      case 'listening':
        return 'bg-blue-500 animate-pulse';
      case 'speaking':
        return 'bg-purple-500 animate-pulse';
      case 'processing':
        return 'bg-yellow-500 animate-pulse';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = (status: typeof sessionStatus) => {
    switch (status) {
      case 'connected':
        return 'Connected';
      case 'active':
        return 'Active';
      case 'listening':
        return 'Listening...';
      case 'speaking':
        return 'Speaking...';
      case 'processing':
        return 'Processing...';
      case 'error':
        return 'Error';
      case 'disconnected':
        return 'Disconnected';
      default:
        return 'Unknown';
    }
  };

  const renderMessage = (message: ConversationalMessage) => {
    const isUser = message.sender === 'user';
    const isSystem = message.sender === 'system';
    
    return (
      <div
        key={message.id}
        className={`flex gap-3 ${isUser ? 'justify-end' : 'justify-start'} ${
          isSystem ? 'justify-center' : ''
        }`}
      >
        {!isUser && !isSystem && (
          <Avatar className="w-8 h-8 flex-shrink-0">
            <AvatarFallback>
              <Bot className="w-4 h-4" />
            </AvatarFallback>
          </Avatar>
        )}
        
        <div className={`max-w-[80%] ${isUser ? 'order-first' : ''}`}>
          <div
            className={`p-3 rounded-lg ${
              isUser 
                ? 'bg-primary text-primary-foreground ml-auto' 
                : isSystem
                  ? 'bg-muted text-muted-foreground text-center text-sm'
                  : 'bg-muted'
            }`}
          >
            {message.type === 'text' ? (
              <p className="whitespace-pre-wrap">{message.content as string}</p>
            ) : message.type === 'audio' && message.content instanceof Blob ? (
              <div className="space-y-2">
                <p className="text-sm opacity-75">Audio message</p>
                <AudioPlayer
                  src={URL.createObjectURL(message.content)}
                  showDownload={false}
                  className="w-full"
                />
              </div>
            ) : (
              <p className="text-sm opacity-75">Unsupported message type</p>
            )}
          </div>
          
          <div className="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
            <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
            {message.metadata?.responseTime && (
              <span>• {message.metadata.responseTime}ms</span>
            )}
            {message.metadata?.confidence && (
              <span>• {Math.round(message.metadata.confidence * 100)}%</span>
            )}
          </div>
        </div>
        
        {isUser && (
          <Avatar className="w-8 h-8 flex-shrink-0">
            <AvatarFallback>
              <User className="w-4 h-4" />
            </AvatarFallback>
          </Avatar>
        )}
      </div>
    );
  };

  if (isLoadingAgents) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading agents...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (agentsError) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load agents: {agentsError}
              <Button
                variant="outline"
                size="sm"
                onClick={loadAgents}
                className="ml-2"
              >
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="w-5 h-5" />
            Conversational AI
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {/* Status Indicator */}
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${getStatusColor(sessionStatus)}`} />
              <span className="text-sm text-muted-foreground">
                {getStatusText(sessionStatus)}
              </span>
            </div>
            
            {/* Session Metrics */}
            {sessionMetrics && (
              <Badge variant="outline" className="text-xs">
                {formatDurationMs(sessionMetrics.currentDuration || 0)}
              </Badge>
            )}
          </div>
        </div>
        
        {/* Agent Selection */}
        {!isConnected && (
          <div className="flex items-center gap-2 pt-2">
            <span className="text-sm text-muted-foreground">Agent:</span>
            <select
              value={selectedAgent?.agent_id || ''}
              onChange={(e) => {
                const agent = agents.find(a => a.agent_id === e.target.value);
                if (agent) selectAgent(agent);
              }}
              className="text-sm border rounded px-2 py-1"
            >
              <option value="">Select an agent...</option>
              {agents.map(agent => (
                <option key={agent.agent_id} value={agent.agent_id}>
                  {agent.name}
                </option>
              ))}
            </select>
          </div>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Connection Controls */}
        <div className="flex items-center gap-2">
          {!isConnected ? (
            <Button
              onClick={handleStartSession}
              disabled={!selectedAgent}
              className="flex-1"
            >
              <PhoneCall className="w-4 h-4 mr-2" />
              Start Conversation
            </Button>
          ) : (
            <>
              <Button
                onClick={handleEndSession}
                variant="destructive"
                size="sm"
              >
                <PhoneOff className="w-4 h-4 mr-2" />
                End
              </Button>
              
              <Button
                onClick={sessionStatus === 'active' ? pauseSession : resumeSession}
                variant="outline"
                size="sm"
              >
                {sessionStatus === 'active' ? (
                  <>
                    <Pause className="w-4 h-4 mr-2" />
                    Pause
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Resume
                  </>
                )}
              </Button>
              
              {(isSpeaking || isProcessing) && (
                <Button
                  onClick={interrupt}
                  variant="outline"
                  size="sm"
                >
                  <Zap className="w-4 h-4 mr-2" />
                  Interrupt
                </Button>
              )}
            </>
          )}
        </div>
        
        {isConnected && (
          <>
            <Separator />
            
            {/* Messages */}
            <div
              ref={chatContainerRef}
              className="h-96 overflow-y-auto space-y-4 p-4 bg-muted/30 rounded-lg"
            >
              {messages.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  <Bot className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>Start speaking or type a message to begin</p>
                </div>
              ) : (
                messages.map(renderMessage)
              )}
              <div ref={messagesEndRef} />
            </div>
            
            {/* Input Controls */}
            <div className="space-y-3">
              {/* Audio Controls */}
              <div className="flex items-center gap-2">
                <Button
                  onClick={handleRecordingToggle}
                  variant={isRecording ? "destructive" : "outline"}
                  size="sm"
                  disabled={!isConnected}
                >
                  {isRecording ? (
                    <>
                      <MicOff className="w-4 h-4 mr-2" />
                      Stop Recording
                    </>
                  ) : (
                    <>
                      <Mic className="w-4 h-4 mr-2" />
                      Start Recording
                    </>
                  )}
                </Button>
                
                <Button
                  onClick={toggleMute}
                  variant="outline"
                  size="sm"
                >
                  {isMuted ? (
                    <VolumeX className="w-4 h-4" />
                  ) : (
                    <Volume2 className="w-4 h-4" />
                  )}
                </Button>
                
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={volume}
                  onChange={(e) => setVolume(parseFloat(e.target.value))}
                  className="w-20"
                />
                
                {isListening && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Activity className="w-4 h-4 animate-pulse" />
                    Listening...
                  </div>
                )}
              </div>
              
              {/* Text Input */}
              <div className="flex items-center gap-2">
                <Input
                  value={textInput}
                  onChange={(e) => setTextInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message..."
                  disabled={!isConnected}
                  className="flex-1"
                />
                <Button
                  onClick={handleSendText}
                  disabled={!textInput.trim() || !isConnected}
                  size="sm"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </div>
            
            {/* Current Audio Playback */}
            {currentAudioUrl && (
              <div className="p-3 bg-muted/50 rounded-lg">
                <p className="text-sm text-muted-foreground mb-2">AI Response:</p>
                <AudioPlayer
                  src={currentAudioUrl}
                  showDownload={false}
                  className="w-full"
                />
              </div>
            )}
            
            {/* Session Info */}
            {sessionMetrics && (
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="font-semibold">{sessionMetrics.messagesCount}</div>
                  <div className="text-muted-foreground">Messages</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold">
                    {Math.round(sessionMetrics.averageResponseTime)}ms
                  </div>
                  <div className="text-muted-foreground">Avg Response</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold">
                    {formatDurationMs(sessionMetrics.currentDuration || 0)}
                  </div>
                  <div className="text-muted-foreground">Duration</div>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}