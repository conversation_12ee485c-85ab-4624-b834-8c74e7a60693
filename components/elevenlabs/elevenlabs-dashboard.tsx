'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Volume2, 
  Users, 
  History, 
  Settings, 
  Zap, 
  TrendingUp, 
  Clock, 
  AlertCircle,
  CheckCircle,
  ExternalLink,
  FileAudio,
  Mic,
  Waveform,
  Headphones
} from 'lucide-react';
import { useElevenLabs } from '@/hooks/use-elevenlabs';
import { useUserManagement } from '@/hooks/use-elevenlabs';
import { useHistoryManagement } from '@/hooks/use-elevenlabs';
import { ApiKeySetup } from './api-key-setup';
import { TextToSpeech } from './text-to-speech';
import { VoiceSelector } from './voice-selector';
import { VoiceCloning } from './voice-cloning';
import { QuotaIndicator } from './quota-indicator';
import { formatNumber } from '@/lib/elevenlabs/utils';

export function ElevenLabsDashboard() {
  const { isConfigured, voices, selectedVoice, generation, voiceClone } = useElevenLabs();
  const { user, getUsageStatistics } = useUserManagement();
  const { history, getAnalytics } = useHistoryManagement();
  
  const [activeTab, setActiveTab] = useState('overview');
  const [usageStats, setUsageStats] = useState<any>(null);
  const [analyticsData, setAnalyticsData] = useState<any>(null);

  useEffect(() => {
    if (isConfigured) {
      // Load usage statistics
      getUsageStatistics().then(setUsageStats);
      
      // Load analytics
      getAnalytics().then(setAnalyticsData);
    }
  }, [isConfigured, getUsageStatistics, getAnalytics]);

  if (!isConfigured) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight">ElevenLabs AI Voice Generation</h1>
          <p className="text-muted-foreground mt-2">
            Generate natural-sounding speech with advanced AI voices
          </p>
        </div>
        
        <ApiKeySetup />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">ElevenLabs Dashboard</h1>
          <p className="text-muted-foreground mt-2">
            Manage your AI voice generation workflow
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {user.subscription?.tier || 'Unknown Plan'}
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('https://elevenlabs.io/app', '_blank')}
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            ElevenLabs App
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Characters Used</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(user.subscription?.character_count || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              of {formatNumber(user.subscription?.character_limit || 0)} limit
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Voices</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{voices.length}</div>
            <p className="text-xs text-muted-foreground">
              voices in library
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">History Items</CardTitle>
            <History className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{history.items.length}</div>
            <p className="text-xs text-muted-foreground">
              generations saved
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cloned Voices</CardTitle>
            <Mic className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{voiceClone.clonedVoices.length}</div>
            <p className="text-xs text-muted-foreground">
              custom voices created
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="text-to-speech">Text-to-Speech</TabsTrigger>
          <TabsTrigger value="voices">Voices</TabsTrigger>
          <TabsTrigger value="clone">Voice Cloning</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Usage Overview */}
            <div className="lg:col-span-2">
              <QuotaIndicator />
            </div>
            
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Start generating speech right away
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  onClick={() => setActiveTab('text-to-speech')}
                  className="w-full justify-start"
                >
                  <Volume2 className="h-4 w-4 mr-2" />
                  Generate Speech
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => setActiveTab('voices')}
                  className="w-full justify-start"
                >
                  <Headphones className="h-4 w-4 mr-2" />
                  Browse Voices
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => setActiveTab('clone')}
                  className="w-full justify-start"
                >
                  <Users className="h-4 w-4 mr-2" />
                  Clone Voice
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Your latest voice generations
              </CardDescription>
            </CardHeader>
            <CardContent>
              {history.items.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <FileAudio className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No recent activity</p>
                  <p className="text-sm">Start generating speech to see your history here</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {history.items.slice(0, 5).map((item) => (
                    <div key={item.history_item_id} className="flex items-center gap-3 p-3 rounded-lg border">
                      <div className="flex-shrink-0">
                        <FileAudio className="h-8 w-8 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">{item.text.substring(0, 50)}...</p>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <span>{item.voice_name}</span>
                          <span>•</span>
                          <span>{new Date(item.date_unix * 1000).toLocaleDateString()}</span>
                        </div>
                      </div>
                      <Badge variant="secondary">{item.text.length} chars</Badge>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Analytics */}
          {analyticsData && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Usage Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Total Generations</span>
                      <span className="font-medium">{analyticsData.totalGenerations}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Total Characters</span>
                      <span className="font-medium">{formatNumber(analyticsData.totalCharacters)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Avg. per Generation</span>
                      <span className="font-medium">{analyticsData.averageCharactersPerGeneration}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Most Used Voices</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsData.mostUsedVoices.slice(0, 5).map((voice: any, index: number) => (
                      <div key={voice.voice_id} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">{index + 1}.</span>
                          <span className="text-sm">{voice.voice_name}</span>
                        </div>
                        <Badge variant="secondary">{voice.count}</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        {/* Text-to-Speech Tab */}
        <TabsContent value="text-to-speech">
          <TextToSpeech />
        </TabsContent>

        {/* Voices Tab */}
        <TabsContent value="voices">
          <VoiceSelector />
        </TabsContent>

        {/* Voice Cloning Tab */}
        <TabsContent value="clone">
          <VoiceCloning />
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Generation History</CardTitle>
              <CardDescription>
                View and manage your voice generation history
              </CardDescription>
            </CardHeader>
            <CardContent>
              {history.items.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No history found</p>
                  <p className="text-sm">Your voice generations will appear here</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {history.items.map((item) => (
                    <div key={item.history_item_id} className="flex items-center gap-3 p-4 rounded-lg border">
                      <div className="flex-shrink-0">
                        <FileAudio className="h-8 w-8 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium mb-1">{item.text.substring(0, 100)}...</p>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>Voice: {item.voice_name}</span>
                          <span>Model: {item.model_id}</span>
                          <span>Date: {new Date(item.date_unix * 1000).toLocaleDateString()}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">{item.text.length} chars</Badge>
                        <Button size="sm" variant="outline">
                          <Waveform className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}