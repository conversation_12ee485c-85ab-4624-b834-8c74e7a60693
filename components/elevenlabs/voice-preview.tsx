'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Play, 
  Pause, 
  Volume2, 
  Loader2, 
  AlertCircle,
  Waveform,
  Settings
} from 'lucide-react';
import { useElevenLabs } from '@/hooks/use-elevenlabs';
import { AudioPlayer } from './audio-player';
import { VoiceSettings } from './voice-settings';
import { getVoiceGender, getVoiceAge, getVoiceAccent, getVoiceCategoryLabel } from '@/lib/elevenlabs/utils';
import type { ElevenLabsVoice } from '@/lib/elevenlabs/types';

interface VoicePreviewProps {
  voice: ElevenLabsVoice;
  onClose?: () => void;
  className?: string;
}

export function VoicePreview({ voice, onClose, className = '' }: VoicePreviewProps) {
  const { generateSpeech, generation } = useElevenLabs();
  
  const [previewText, setPreviewText] = useState(
    'Hello, this is a preview of my voice. I can speak in different languages and tones.'
  );
  const [voiceSettings, setVoiceSettings] = useState(voice.settings || {
    stability: 0.5,
    similarity_boost: 0.5,
    style: 0.0,
    use_speaker_boost: true,
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showSettings, setShowSettings] = useState(false);

  const handlePreview = async () => {
    if (!previewText.trim()) return;

    setIsGenerating(true);
    setError(null);
    setAudioUrl(null);

    try {
      // Generate speech for this specific voice
      const response = await generateSpeech({
        text: previewText,
        voice_id: voice.voice_id,
        voice_settings: voiceSettings,
      });

      // Note: This is a simplified version - in the real implementation,
      // you would need to handle the response properly
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate preview');
    } finally {
      setIsGenerating(false);
    }
  };

  // Use the generated audio from the main store
  useEffect(() => {
    if (generation.audioUrl && generation.currentText === previewText) {
      setAudioUrl(generation.audioUrl);
    }
  }, [generation.audioUrl, generation.currentText, previewText]);

  const sampleTexts = [
    'Hello, this is a preview of my voice. I can speak in different languages and tones.',
    'The quick brown fox jumps over the lazy dog. This sentence contains every letter of the alphabet.',
    'Welcome to our service! I hope you find my voice pleasant and natural-sounding.',
    'In a world full of artificial voices, I strive to sound as human and engaging as possible.',
    'Technology has come a long way, and I\'m excited to be part of your creative projects.',
  ];

  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Voice Info */}
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-semibold">{voice.name}</h3>
                <Badge variant="secondary">
                  {getVoiceCategoryLabel(voice.category)}
                </Badge>
              </div>
              
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span>Gender: {getVoiceGender(voice)}</span>
                <span>Age: {getVoiceAge(voice)}</span>
                <span>Accent: {getVoiceAccent(voice)}</span>
              </div>
              
              {voice.description && (
                <p className="text-sm text-muted-foreground">{voice.description}</p>
              )}
            </div>
            
            {onClose && (
              <Button variant="ghost" size="sm" onClick={onClose}>
                ×
              </Button>
            )}
          </div>

          <Separator />

          {/* Preview Text */}
          <div className="space-y-3">
            <Label htmlFor="preview-text">Preview Text</Label>
            <Input
              id="preview-text"
              value={previewText}
              onChange={(e) => setPreviewText(e.target.value)}
              placeholder="Enter text to preview..."
              maxLength={500}
            />
            
            {/* Sample Texts */}
            <div className="space-y-2">
              <Label className="text-xs">Sample Texts:</Label>
              <div className="grid grid-cols-1 gap-1">
                {sampleTexts.map((text, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="sm"
                    onClick={() => setPreviewText(text)}
                    className="justify-start text-xs h-auto p-2 font-normal"
                  >
                    {text.substring(0, 60)}...
                  </Button>
                ))}
              </div>
            </div>
          </div>

          {/* Voice Settings Toggle */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-4 w-4 mr-2" />
              {showSettings ? 'Hide' : 'Show'} Settings
            </Button>
          </div>

          {/* Voice Settings */}
          {showSettings && (
            <VoiceSettings
              settings={voiceSettings}
              onChange={setVoiceSettings}
              voice={voice}
            />
          )}

          {/* Generate Button */}
          <Button
            onClick={handlePreview}
            disabled={isGenerating || !previewText.trim()}
            className="w-full"
          >
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating Preview...
              </>
            ) : (
              <>
                <Volume2 className="h-4 w-4 mr-2" />
                Generate Preview
              </>
            )}
          </Button>

          {/* Error Display */}
          {error && (
            <div className="flex items-center gap-2 text-sm text-destructive">
              <AlertCircle className="h-4 w-4" />
              {error}
            </div>
          )}

          {/* Audio Player */}
          {audioUrl && (
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Waveform className="h-4 w-4" />
                Voice Preview
              </Label>
              <AudioPlayer
                src={audioUrl}
                filename={`${voice.name}_preview.mp3`}
                className="w-full"
              />
            </div>
          )}

          {/* Voice Details */}
          <div className="space-y-3">
            <Label>Voice Details</Label>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Voice ID:</span>
                <span className="ml-2 font-mono text-xs">{voice.voice_id}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Category:</span>
                <span className="ml-2">{getVoiceCategoryLabel(voice.category)}</span>
              </div>
              {voice.labels && (
                <>
                  {voice.labels.language && (
                    <div>
                      <span className="text-muted-foreground">Language:</span>
                      <span className="ml-2">{voice.labels.language}</span>
                    </div>
                  )}
                  {voice.labels['use case'] && (
                    <div>
                      <span className="text-muted-foreground">Use Case:</span>
                      <span className="ml-2">{voice.labels['use case']}</span>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>

          {/* Default Settings */}
          {voice.settings && (
            <div className="space-y-3">
              <Label>Default Settings</Label>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Stability:</span>
                  <span className="ml-2">{voice.settings.stability?.toFixed(2) || 'N/A'}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">Similarity:</span>
                  <span className="ml-2">{voice.settings.similarity_boost?.toFixed(2) || 'N/A'}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">Style:</span>
                  <span className="ml-2">{voice.settings.style?.toFixed(2) || 'N/A'}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">Speaker Boost:</span>
                  <span className="ml-2">{voice.settings.use_speaker_boost ? 'On' : 'Off'}</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}