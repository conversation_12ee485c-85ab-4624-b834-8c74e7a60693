'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Palette, 
  AlertCircle, 
  CheckCircle, 
  Loader2,
  Lightbulb,
  Wand2,
  User,
  Heart,
  Brain
} from 'lucide-react';
import { useVoiceDesign } from '@/hooks/use-elevenlabs';
import { useUserManagement } from '@/hooks/use-elevenlabs';
import { VoicePreview } from './voice-preview';
import type { VoiceDesignRequest } from '@/lib/elevenlabs/types';

interface VoiceDesignProps {
  onVoiceDesigned?: (voice: any) => void;
  className?: string;
}

export function VoiceDesign({ onVoiceDesigned, className = '' }: VoiceDesignProps) {
  const { voiceDesign, design, clearError } = useVoiceDesign();
  const { getFeatureAvailability } = useUserManagement();

  const [formData, setFormData] = useState({
    name: '',
    text: '',
    voice_description: '',
  });

  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [featureAvailable, setFeatureAvailable] = useState<boolean | null>(null);
  const [previewVoice, setPreviewVoice] = useState<any>(null);

  React.useEffect(() => {
    const checkFeatureAvailability = async () => {
      try {
        const features = await getFeatureAvailability();
        setFeatureAvailable(features?.voice_design || false);
      } catch (error) {
        setFeatureAvailable(false);
      }
    };

    checkFeatureAvailability();
  }, [getFeatureAvailability]);

  const validateForm = () => {
    const errors: string[] = [];

    if (!formData.name.trim()) {
      errors.push('Voice name is required');
    }

    if (formData.name.length > 100) {
      errors.push('Voice name must be 100 characters or less');
    }

    if (!formData.text.trim()) {
      errors.push('Sample text is required');
    }

    if (formData.text.length < 50) {
      errors.push('Sample text must be at least 50 characters long');
    }

    if (formData.text.length > 1000) {
      errors.push('Sample text must be 1000 characters or less');
    }

    if (!formData.voice_description.trim()) {
      errors.push('Voice description is required');
    }

    if (formData.voice_description.length < 10) {
      errors.push('Voice description must be at least 10 characters long');
    }

    if (formData.voice_description.length > 500) {
      errors.push('Voice description must be 500 characters or less');
    }

    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const errors = validateForm();
    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }

    setValidationErrors([]);
    clearError();

    const request: VoiceDesignRequest = {
      name: formData.name.trim(),
      text: formData.text.trim(),
      voice_description: formData.voice_description.trim(),
    };

    try {
      await design(request);
      
      if (!voiceDesign.error) {
        // Reset form on success
        setFormData({
          name: '',
          text: '',
          voice_description: '',
        });
        
        const newVoice = voiceDesign.designedVoices[voiceDesign.designedVoices.length - 1];
        onVoiceDesigned?.(newVoice);
        setPreviewVoice(newVoice);
      }
    } catch (error) {
      console.error('Voice design failed:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      text: '',
      voice_description: '',
    });
    setValidationErrors([]);
    setPreviewVoice(null);
    clearError();
  };

  const exampleDescriptions = [
    'A warm and friendly female voice with a slight American accent, perfect for audiobook narration',
    'A deep, authoritative male voice with a British accent, ideal for documentary voiceovers',
    'A young, energetic voice with a cheerful tone, great for children\'s content and educational materials',
    'A mature, sophisticated voice with a neutral accent, suitable for corporate presentations',
    'A calm and soothing voice with a gentle tone, perfect for meditation and relaxation content',
  ];

  const exampleTexts = [
    'Welcome to our innovative platform where creativity meets technology. Here, you can bring your ideas to life with the power of artificial intelligence.',
    'In a world where communication is key, having the right voice can make all the difference. Our advanced AI technology ensures your message is heard clearly.',
    'Every story deserves to be told with the perfect voice. Whether you\'re creating content for entertainment, education, or business, we have you covered.',
    'The future of voice synthesis is here, and it\'s more natural and expressive than ever before. Join us in revolutionizing how we communicate.',
    'From podcasts to presentations, from audiobooks to advertisements, your voice has the power to captivate and inspire audiences worldwide.',
  ];

  if (featureAvailable === false) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Voice Design
          </CardTitle>
          <CardDescription>
            Create custom voices from text descriptions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Voice design is not available with your current subscription plan. 
              Please upgrade to access this feature.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Main Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Voice Design
          </CardTitle>
          <CardDescription>
            Describe your ideal voice and let our AI create it for you
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Voice Information */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="voice-name">Voice Name *</Label>
                <Input
                  id="voice-name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter a unique name for your voice"
                  maxLength={100}
                />
                <p className="text-xs text-muted-foreground">
                  {formData.name.length} / 100 characters
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="voice-description">Voice Description *</Label>
                <Textarea
                  id="voice-description"
                  value={formData.voice_description}
                  onChange={(e) => setFormData(prev => ({ ...prev, voice_description: e.target.value }))}
                  placeholder="Describe the voice characteristics, tone, accent, age, and intended use..."
                  maxLength={500}
                  rows={4}
                />
                <p className="text-xs text-muted-foreground">
                  {formData.voice_description.length} / 500 characters
                </p>
              </div>

              {/* Example Descriptions */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Lightbulb className="h-4 w-4" />
                  Example Descriptions
                </Label>
                <div className="grid grid-cols-1 gap-2">
                  {exampleDescriptions.map((description, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      size="sm"
                      onClick={() => setFormData(prev => ({ ...prev, voice_description: description }))}
                      className="justify-start text-xs h-auto p-3 font-normal text-left"
                    >
                      {description}
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            <Separator />

            {/* Sample Text */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="sample-text">Sample Text *</Label>
                <Textarea
                  id="sample-text"
                  value={formData.text}
                  onChange={(e) => setFormData(prev => ({ ...prev, text: e.target.value }))}
                  placeholder="Enter text for the AI to speak in your designed voice..."
                  maxLength={1000}
                  rows={4}
                />
                <p className="text-xs text-muted-foreground">
                  {formData.text.length} / 1000 characters (minimum 50 characters)
                </p>
              </div>

              {/* Example Texts */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Brain className="h-4 w-4" />
                  Example Texts
                </Label>
                <div className="grid grid-cols-1 gap-2">
                  {exampleTexts.map((text, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      size="sm"
                      onClick={() => setFormData(prev => ({ ...prev, text }))}
                      className="justify-start text-xs h-auto p-3 font-normal text-left"
                    >
                      {text.substring(0, 100)}...
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            {/* Validation Errors */}
            {validationErrors.length > 0 && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <ul className="list-disc pl-4">
                    {validationErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {/* Design Error */}
            {voiceDesign.error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{voiceDesign.error}</AlertDescription>
              </Alert>
            )}

            {/* Progress */}
            {voiceDesign.isDesigning && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Designing Voice</span>
                  <span className="text-sm text-muted-foreground">
                    {Math.round(voiceDesign.progress)}%
                  </span>
                </div>
                <Progress value={voiceDesign.progress} className="h-2" />
                <p className="text-sm text-muted-foreground">
                  This may take a few minutes. Please don't close this page.
                </p>
              </div>
            )}

            {/* Success Message */}
            {voiceDesign.designedVoices.length > 0 && !voiceDesign.isDesigning && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Voice designed successfully! You can now use "{voiceDesign.designedVoices[voiceDesign.designedVoices.length - 1]?.name}" in your projects.
                </AlertDescription>
              </Alert>
            )}

            {/* Form Actions */}
            <div className="flex gap-2">
              <Button
                type="submit"
                disabled={voiceDesign.isDesigning || formData.text.length < 50 || !formData.name.trim() || !formData.voice_description.trim()}
              >
                {voiceDesign.isDesigning ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Designing Voice...
                  </>
                ) : (
                  <>
                    <Wand2 className="h-4 w-4 mr-2" />
                    Design Voice
                  </>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={resetForm}
                disabled={voiceDesign.isDesigning}
              >
                Reset
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Designed Voices */}
      {voiceDesign.designedVoices.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Heart className="h-5 w-5" />
              Your Designed Voices
            </CardTitle>
            <CardDescription>
              Voices you've created with AI voice design
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {voiceDesign.designedVoices.map((voice, index) => (
                <Card key={index} className="cursor-pointer hover:bg-accent transition-colors">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">{voice.name}</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-2">
                      <Badge variant="secondary">Designed</Badge>
                      <p className="text-xs text-muted-foreground line-clamp-3">
                        {voice.description}
                      </p>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setPreviewVoice(voice)}
                        className="w-full"
                      >
                        Preview Voice
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Voice Preview Modal */}
      {previewVoice && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="max-w-2xl w-full max-h-[90vh] overflow-auto">
            <VoicePreview
              voice={previewVoice}
              onClose={() => setPreviewVoice(null)}
            />
          </div>
        </div>
      )}
    </div>
  );
}