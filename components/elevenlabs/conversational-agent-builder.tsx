'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bot, 
  Save, 
  Play, 
  Settings, 
  Mic, 
  Volume2, 
  Brain,
  Zap,
  Shield,
  Palette,
  MessageSquare,
  Users,
  Clock,
  AlertCircle,
  CheckCircle,
  Loader2,
  <PERSON><PERSON>,
  Trash2,
  Edit
} from 'lucide-react';
import { useConversationalAgents } from '@/hooks/use-conversational-ai';
import { useElevenLabs } from '@/hooks/use-elevenlabs';
import { VoiceSelector } from './voice-selector';
import { ConversationalAIChat } from './conversational-ai-chat';
import type { 
  ConversationalAIAgent,
  VoiceSettings,
  TurnTakingSettings,
  ConversationConfig
} from '@/lib/elevenlabs/types/conversational-ai-types';

interface ConversationalAgentBuilderProps {
  apiKey: string;
  initialAgent?: Partial<ConversationalAIAgent>;
  onAgentCreated?: (agent: ConversationalAIAgent) => void;
  onAgentUpdated?: (agent: ConversationalAIAgent) => void;
  className?: string;
}

export function ConversationalAgentBuilder({
  apiKey,
  initialAgent,
  onAgentCreated,
  onAgentUpdated,
  className = ''
}: ConversationalAgentBuilderProps) {
  const { voices, loadVoices } = useElevenLabs();
  const {
    agents,
    selectedAgent,
    isLoading,
    error,
    loadAgents,
    selectAgent,
    createAgent,
    updateAgent,
    deleteAgent,
    clearError
  } = useConversationalAgents();

  const [formData, setFormData] = useState<Partial<ConversationalAIAgent>>({
    name: '',
    description: '',
    voice_id: '',
    model_id: 'gpt-3.5-turbo',
    system_prompt: '',
    language: 'en',
    conversation_config: {
      conversation_timeout_ms: 30000,
      max_conversation_length_ms: 1800000,
      enable_interruptions: true,
      enable_backchannel: true,
      enable_turn_taking: true,
      silence_threshold_ms: 1000,
      voice_activity_detection: {
        enabled: true,
        threshold: 0.01,
        min_speech_duration_ms: 300,
        max_silence_duration_ms: 1000,
        speech_pad_ms: 100,
        silence_pad_ms: 200,
        energy_threshold: 0.02,
        spectral_centroid_threshold: 0.1
      },
      audio_processing: {
        input_audio_format: {
          encoding: 'pcm_16',
          sample_rate: 44100,
          bit_depth: 16,
          channels: 1
        },
        output_audio_format: {
          encoding: 'pcm_16',
          sample_rate: 44100,
          bit_depth: 16,
          channels: 1
        },
        noise_suppression: true,
        echo_cancellation: true,
        auto_gain_control: true,
        sample_rate: 44100,
        bit_depth: 16,
        channels: 1
      }
    },
    voice_settings: {
      stability: 0.5,
      similarity_boost: 0.5,
      style: 0.0,
      use_speaker_boost: true,
      optimize_streaming_latency: 3,
      output_format: 'pcm_44100'
    },
    turn_taking_settings: {
      enabled: true,
      model_type: 'advanced',
      sensitivity: 0.7,
      max_silence_ms: 1000,
      min_speech_duration_ms: 300,
      interruption_threshold: 0.5,
      turn_detection_method: 'hybrid'
    },
    context_window: 4000,
    max_tokens: 500,
    temperature: 0.7,
    top_p: 0.9,
    frequency_penalty: 0,
    presence_penalty: 0,
    is_public: false,
    tags: [],
    metadata: {}
  });

  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testingAgentId, setTestingAgentId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('basic');

  // Load initial data
  useEffect(() => {
    loadVoices();
    loadAgents();
  }, [loadVoices, loadAgents]);

  // Initialize form with provided agent
  useEffect(() => {
    if (initialAgent || selectedAgent) {
      const agent = initialAgent || selectedAgent;
      setFormData(agent as Partial<ConversationalAIAgent>);
    }
  }, [initialAgent, selectedAgent]);

  const validateForm = (): boolean => {
    const errors: string[] = [];

    if (!formData.name?.trim()) {
      errors.push('Agent name is required');
    }

    if (!formData.voice_id) {
      errors.push('Voice selection is required');
    }

    if (!formData.system_prompt?.trim()) {
      errors.push('System prompt is required');
    }

    if (formData.system_prompt && formData.system_prompt.length > 5000) {
      errors.push('System prompt must be 5000 characters or less');
    }

    if (formData.max_tokens && (formData.max_tokens < 1 || formData.max_tokens > 4000)) {
      errors.push('Max tokens must be between 1 and 4000');
    }

    if (formData.temperature && (formData.temperature < 0 || formData.temperature > 2)) {
      errors.push('Temperature must be between 0 and 2');
    }

    setValidationErrors(errors);
    return errors.length === 0;
  };

  const handleCreate = async () => {
    if (!validateForm()) return;

    setIsCreating(true);
    try {
      const agent = await createAgent(formData);
      onAgentCreated?.(agent);
      setFormData({
        ...formData,
        name: '',
        description: '',
        system_prompt: ''
      });
      clearError();
    } catch (error) {
      console.error('Failed to create agent:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleUpdate = async () => {
    if (!selectedAgent || !validateForm()) return;

    setIsUpdating(true);
    try {
      await updateAgent(selectedAgent.agent_id, formData);
      onAgentUpdated?.(selectedAgent);
      clearError();
    } catch (error) {
      console.error('Failed to update agent:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleTest = () => {
    if (!selectedAgent) return;
    setTestingAgentId(selectedAgent.agent_id);
    setIsTesting(true);
  };

  const handleCloseTest = () => {
    setIsTesting(false);
    setTestingAgentId(null);
  };

  const updateVoiceSettings = (updates: Partial<VoiceSettings>) => {
    setFormData(prev => ({
      ...prev,
      voice_settings: {
        ...prev.voice_settings!,
        ...updates
      }
    }));
  };

  const updateTurnTakingSettings = (updates: Partial<TurnTakingSettings>) => {
    setFormData(prev => ({
      ...prev,
      turn_taking_settings: {
        ...prev.turn_taking_settings!,
        ...updates
      }
    }));
  };

  const updateConversationConfig = (updates: Partial<ConversationConfig>) => {
    setFormData(prev => ({
      ...prev,
      conversation_config: {
        ...prev.conversation_config!,
        ...updates
      }
    }));
  };

  const promptTemplates = [
    {
      name: 'Customer Support',
      description: 'Helpful customer service agent',
      prompt: `You are a helpful and friendly customer support agent. Your goal is to assist customers with their inquiries, resolve issues, and provide excellent service. Always be polite, patient, and professional. If you cannot solve a problem, escalate it appropriately.

Key behaviors:
- Listen actively to customer concerns
- Ask clarifying questions when needed
- Provide clear, step-by-step solutions
- Show empathy for customer frustrations
- Follow up to ensure satisfaction`
    },
    {
      name: 'Sales Assistant',
      description: 'Engaging sales representative',
      prompt: `You are an enthusiastic and knowledgeable sales assistant. Your role is to help customers discover products that meet their needs, answer questions about features and benefits, and guide them through the purchasing process.

Key behaviors:
- Ask about customer needs and preferences
- Highlight relevant product benefits
- Handle objections professionally
- Create urgency when appropriate
- Build rapport and trust`
    },
    {
      name: 'Personal Tutor',
      description: 'Educational assistant and mentor',
      prompt: `You are a patient and encouraging personal tutor. Your mission is to help students learn effectively by providing clear explanations, answering questions, and adapting your teaching style to their learning preferences.

Key behaviors:
- Break down complex concepts into simple steps
- Use examples and analogies to explain ideas
- Encourage questions and curiosity
- Provide positive reinforcement
- Adapt explanations based on understanding level`
    },
    {
      name: 'Healthcare Assistant',
      description: 'Medical information helper',
      prompt: `You are a knowledgeable healthcare assistant providing general health information and guidance. Always emphasize that you are not a replacement for professional medical advice and encourage users to consult healthcare providers for serious concerns.

Key behaviors:
- Provide accurate, evidence-based health information
- Encourage professional medical consultation
- Be empathetic about health concerns
- Avoid diagnosing or prescribing
- Focus on general wellness and prevention`
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Agent List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Existing Agents
          </CardTitle>
          <CardDescription>
            Select an agent to edit or create a new one
          </CardDescription>
        </CardHeader>
        <CardContent>
          {agents.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">
              No agents found. Create your first conversational AI agent below.
            </p>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {agents.map(agent => (
                <Card 
                  key={agent.agent_id}
                  className={`cursor-pointer transition-colors ${
                    selectedAgent?.agent_id === agent.agent_id ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => selectAgent(agent)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm">{agent.name}</CardTitle>
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleTest();
                          }}
                        >
                          <Play className="w-3 h-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (confirm('Delete this agent?')) {
                              deleteAgent(agent.agent_id);
                            }
                          }}
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                    {agent.description && (
                      <p className="text-xs text-muted-foreground">
                        {agent.description}
                      </p>
                    )}
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex items-center gap-2 text-xs">
                      <Badge variant="secondary">{agent.language}</Badge>
                      <Badge variant="outline">{agent.model_id}</Badge>
                      {agent.is_public && (
                        <Badge variant="default">Public</Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Agent Builder */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="w-5 h-5" />
            {selectedAgent ? 'Edit Agent' : 'Create New Agent'}
          </CardTitle>
          <CardDescription>
            {selectedAgent 
              ? `Editing: ${selectedAgent.name}`
              : 'Build a custom conversational AI agent'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="basic">Basic</TabsTrigger>
              <TabsTrigger value="prompt">Prompt</TabsTrigger>
              <TabsTrigger value="voice">Voice</TabsTrigger>
              <TabsTrigger value="conversation">Conversation</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            {/* Basic Settings */}
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Agent Name *</Label>
                  <Input
                    id="name"
                    value={formData.name || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Customer Support Bot"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="language">Language</Label>
                  <select
                    id="language"
                    value={formData.language || 'en'}
                    onChange={(e) => setFormData(prev => ({ ...prev, language: e.target.value }))}
                    className="w-full border border-input rounded-md px-3 py-2"
                  >
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                    <option value="it">Italian</option>
                    <option value="pt">Portuguese</option>
                    <option value="zh">Chinese</option>
                    <option value="ja">Japanese</option>
                    <option value="ko">Korean</option>
                  </select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe what this agent does..."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="model">AI Model</Label>
                <select
                  id="model"
                  value={formData.model_id || 'gpt-3.5-turbo'}
                  onChange={(e) => setFormData(prev => ({ ...prev, model_id: e.target.value }))}
                  className="w-full border border-input rounded-md px-3 py-2"
                >
                  <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                  <option value="gpt-4">GPT-4</option>
                  <option value="gpt-4-turbo">GPT-4 Turbo</option>
                  <option value="claude-3-haiku">Claude 3 Haiku</option>
                  <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                  <option value="claude-3-opus">Claude 3 Opus</option>
                  <option value="gemini-pro">Gemini Pro</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="public"
                  checked={formData.is_public || false}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_public: checked }))}
                />
                <Label htmlFor="public">Make this agent public</Label>
              </div>
            </TabsContent>

            {/* Prompt Configuration */}
            <TabsContent value="prompt" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="system-prompt">System Prompt *</Label>
                <Textarea
                  id="system-prompt"
                  value={formData.system_prompt || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, system_prompt: e.target.value }))}
                  placeholder="Define the agent's personality, role, and behavior..."
                  rows={10}
                  className="font-mono text-sm"
                />
                <p className="text-xs text-muted-foreground">
                  {formData.system_prompt?.length || 0} / 5000 characters
                </p>
              </div>

              {/* Prompt Templates */}
              <div className="space-y-3">
                <Label>Template Examples</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {promptTemplates.map((template, index) => (
                    <Card key={index} className="cursor-pointer hover:bg-accent transition-colors">
                      <CardHeader 
                        className="pb-2"
                        onClick={() => setFormData(prev => ({ ...prev, system_prompt: template.prompt }))}
                      >
                        <CardTitle className="text-sm">{template.name}</CardTitle>
                        <CardDescription className="text-xs">
                          {template.description}
                        </CardDescription>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </div>
            </TabsContent>

            {/* Voice Settings */}
            <TabsContent value="voice" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label>Select Voice</Label>
                  <VoiceSelector
                    onVoiceSelect={(voice) => 
                      setFormData(prev => ({ ...prev, voice_id: voice.voice_id }))
                    }
                    selectedVoice={voices.find(v => v.voice_id === formData.voice_id)}
                    compact
                  />
                </div>

                {formData.voice_settings && (
                  <>
                    <Separator />
                    <div className="space-y-4">
                      <h4 className="font-semibold">Voice Settings</h4>
                      
                      <div className="space-y-3">
                        <div>
                          <Label>Stability: {formData.voice_settings.stability}</Label>
                          <Slider
                            value={[formData.voice_settings.stability]}
                            onValueChange={([value]) => updateVoiceSettings({ stability: value })}
                            min={0}
                            max={1}
                            step={0.01}
                            className="mt-2"
                          />
                        </div>

                        <div>
                          <Label>Similarity Boost: {formData.voice_settings.similarity_boost}</Label>
                          <Slider
                            value={[formData.voice_settings.similarity_boost]}
                            onValueChange={([value]) => updateVoiceSettings({ similarity_boost: value })}
                            min={0}
                            max={1}
                            step={0.01}
                            className="mt-2"
                          />
                        </div>

                        <div>
                          <Label>Style: {formData.voice_settings.style}</Label>
                          <Slider
                            value={[formData.voice_settings.style]}
                            onValueChange={([value]) => updateVoiceSettings({ style: value })}
                            min={0}
                            max={1}
                            step={0.01}
                            className="mt-2"
                          />
                        </div>

                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={formData.voice_settings.use_speaker_boost}
                            onCheckedChange={(checked) => updateVoiceSettings({ use_speaker_boost: checked })}
                          />
                          <Label>Use Speaker Boost</Label>
                        </div>

                        <div>
                          <Label>Streaming Latency: {formData.voice_settings.optimize_streaming_latency}</Label>
                          <Slider
                            value={[formData.voice_settings.optimize_streaming_latency]}
                            onValueChange={([value]) => updateVoiceSettings({ optimize_streaming_latency: value })}
                            min={0}
                            max={4}
                            step={1}
                            className="mt-2"
                          />
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </TabsContent>

            {/* Conversation Settings */}
            <TabsContent value="conversation" className="space-y-4">
              {formData.turn_taking_settings && (
                <div className="space-y-4">
                  <h4 className="font-semibold">Turn Taking</h4>
                  
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={formData.turn_taking_settings.enabled}
                      onCheckedChange={(checked) => updateTurnTakingSettings({ enabled: checked })}
                    />
                    <Label>Enable Turn Taking</Label>
                  </div>

                  <div>
                    <Label>Sensitivity: {formData.turn_taking_settings.sensitivity}</Label>
                    <Slider
                      value={[formData.turn_taking_settings.sensitivity]}
                      onValueChange={([value]) => updateTurnTakingSettings({ sensitivity: value })}
                      min={0}
                      max={1}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label>Max Silence (ms): {formData.turn_taking_settings.max_silence_ms}</Label>
                    <Slider
                      value={[formData.turn_taking_settings.max_silence_ms]}
                      onValueChange={([value]) => updateTurnTakingSettings({ max_silence_ms: value })}
                      min={500}
                      max={5000}
                      step={100}
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label>Min Speech Duration (ms): {formData.turn_taking_settings.min_speech_duration_ms}</Label>
                    <Slider
                      value={[formData.turn_taking_settings.min_speech_duration_ms]}
                      onValueChange={([value]) => updateTurnTakingSettings({ min_speech_duration_ms: value })}
                      min={100}
                      max={1000}
                      step={50}
                      className="mt-2"
                    />
                  </div>
                </div>
              )}

              {formData.conversation_config && (
                <>
                  <Separator />
                  <div className="space-y-4">
                    <h4 className="font-semibold">Conversation Settings</h4>
                    
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={formData.conversation_config.enable_interruptions}
                        onCheckedChange={(checked) => updateConversationConfig({ enable_interruptions: checked })}
                      />
                      <Label>Enable Interruptions</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={formData.conversation_config.enable_backchannel}
                        onCheckedChange={(checked) => updateConversationConfig({ enable_backchannel: checked })}
                      />
                      <Label>Enable Backchannel</Label>
                    </div>

                    <div>
                      <Label>Conversation Timeout (ms): {formData.conversation_config.conversation_timeout_ms}</Label>
                      <Slider
                        value={[formData.conversation_config.conversation_timeout_ms]}
                        onValueChange={([value]) => updateConversationConfig({ conversation_timeout_ms: value })}
                        min={10000}
                        max={300000}
                        step={5000}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <Label>Max Conversation Length (ms): {formData.conversation_config.max_conversation_length_ms}</Label>
                      <Slider
                        value={[formData.conversation_config.max_conversation_length_ms]}
                        onValueChange={([value]) => updateConversationConfig({ max_conversation_length_ms: value })}
                        min={300000}
                        max={7200000}
                        step={300000}
                        className="mt-2"
                      />
                    </div>
                  </div>
                </>
              )}
            </TabsContent>

            {/* Advanced Settings */}
            <TabsContent value="advanced" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Max Tokens: {formData.max_tokens}</Label>
                  <Slider
                    value={[formData.max_tokens || 500]}
                    onValueChange={([value]) => setFormData(prev => ({ ...prev, max_tokens: value }))}
                    min={50}
                    max={4000}
                    step={50}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label>Temperature: {formData.temperature}</Label>
                  <Slider
                    value={[formData.temperature || 0.7]}
                    onValueChange={([value]) => setFormData(prev => ({ ...prev, temperature: value }))}
                    min={0}
                    max={2}
                    step={0.1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label>Top P: {formData.top_p}</Label>
                  <Slider
                    value={[formData.top_p || 0.9]}
                    onValueChange={([value]) => setFormData(prev => ({ ...prev, top_p: value }))}
                    min={0}
                    max={1}
                    step={0.1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label>Context Window: {formData.context_window}</Label>
                  <Slider
                    value={[formData.context_window || 4000]}
                    onValueChange={([value]) => setFormData(prev => ({ ...prev, context_window: value }))}
                    min={1000}
                    max={16000}
                    step={1000}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label>Frequency Penalty: {formData.frequency_penalty}</Label>
                  <Slider
                    value={[formData.frequency_penalty || 0]}
                    onValueChange={([value]) => setFormData(prev => ({ ...prev, frequency_penalty: value }))}
                    min={-2}
                    max={2}
                    step={0.1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label>Presence Penalty: {formData.presence_penalty}</Label>
                  <Slider
                    value={[formData.presence_penalty || 0]}
                    onValueChange={([value]) => setFormData(prev => ({ ...prev, presence_penalty: value }))}
                    min={-2}
                    max={2}
                    step={0.1}
                    className="mt-2"
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc pl-4">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* Service Error */}
          {error && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearError}
                  className="ml-2"
                >
                  Dismiss
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 mt-6">
            {selectedAgent ? (
              <>
                <Button
                  onClick={handleUpdate}
                  disabled={isUpdating || !validateForm()}
                >
                  {isUpdating ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Update Agent
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleTest}
                  disabled={isTesting}
                >
                  <Play className="w-4 h-4 mr-2" />
                  Test Agent
                </Button>
              </>
            ) : (
              <Button
                onClick={handleCreate}
                disabled={isCreating || !validateForm()}
              >
                {isCreating ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Create Agent
                  </>
                )}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Test Chat Interface */}
      {isTesting && testingAgentId && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="max-w-4xl w-full max-h-[90vh] overflow-auto">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Test Agent: {selectedAgent?.name}</CardTitle>
                  <Button variant="outline" onClick={handleCloseTest}>
                    Close Test
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <ConversationalAIChat
                  apiKey={apiKey}
                  agentId={testingAgentId}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}