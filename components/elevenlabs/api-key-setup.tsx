'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Eye, EyeOff, Key, ExternalLink, CheckCircle, AlertCircle } from 'lucide-react';
import { useElevenLabs } from '@/hooks/use-elevenlabs';
import { validateApiKey } from '@/lib/elevenlabs/utils';

interface ApiKeySetupProps {
  onSuccess?: () => void;
  className?: string;
}

export function ApiKeySetup({ onSuccess, className = '' }: ApiKeySetupProps) {
  const { apiKey, setApiKey, isConfigured } = useElevenLabs();
  const [inputValue, setInputValue] = useState(apiKey || '');
  const [showKey, setShowKey] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!inputValue.trim()) {
      setValidationError('API key is required');
      return;
    }

    if (!validateApiKey(inputValue)) {
      setValidationError('Invalid API key format. ElevenLabs API keys should start with "sk-" followed by 32 characters.');
      return;
    }

    setIsValidating(true);
    setValidationError(null);

    try {
      // Test the API key by making a simple request
      const response = await fetch('https://api.elevenlabs.io/v1/user', {
        headers: {
          'xi-api-key': inputValue,
        },
      });

      if (!response.ok) {
        throw new Error('Invalid API key or insufficient permissions');
      }

      setApiKey(inputValue);
      onSuccess?.();
    } catch (error) {
      setValidationError(error instanceof Error ? error.message : 'Failed to validate API key');
    } finally {
      setIsValidating(false);
    }
  };

  const handleClear = () => {
    setInputValue('');
    setApiKey('');
    setValidationError(null);
  };

  if (isConfigured) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            API Key Configured
          </CardTitle>
          <CardDescription>
            Your ElevenLabs API key is configured and ready to use.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2">
            <div className="flex-1">
              <Label htmlFor="current-key">Current API Key</Label>
              <div className="flex gap-2 mt-1">
                <Input
                  id="current-key"
                  type={showKey ? 'text' : 'password'}
                  value={apiKey || ''}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowKey(!showKey)}
                >
                  {showKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => {
                setInputValue(apiKey || '');
                setShowKey(false);
              }}
            >
              Update Key
            </Button>
            <Button
              variant="outline"
              onClick={handleClear}
            >
              Clear Key
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="h-5 w-5" />
          ElevenLabs API Key Setup
        </CardTitle>
        <CardDescription>
          Enter your ElevenLabs API key to access voice synthesis features.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="api-key">API Key</Label>
            <div className="flex gap-2">
              <Input
                id="api-key"
                type={showKey ? 'text' : 'password'}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="sk-..."
                className="font-mono text-sm"
                disabled={isValidating}
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowKey(!showKey)}
                disabled={isValidating}
              >
                {showKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          {validationError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{validationError}</AlertDescription>
            </Alert>
          )}

          <div className="flex gap-2">
            <Button
              type="submit"
              disabled={!inputValue.trim() || isValidating}
            >
              {isValidating ? 'Validating...' : 'Save API Key'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => window.open('https://elevenlabs.io/docs/api-reference/getting-started', '_blank')}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Get API Key
            </Button>
          </div>
        </form>

        <div className="mt-6 p-4 bg-muted rounded-lg">
          <h4 className="font-semibold mb-2">How to get your API key:</h4>
          <ol className="list-decimal list-inside space-y-1 text-sm text-muted-foreground">
            <li>Sign up at <a href="https://elevenlabs.io" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">elevenlabs.io</a></li>
            <li>Go to your Profile Settings</li>
            <li>Navigate to the API Keys section</li>
            <li>Create a new API key</li>
            <li>Copy and paste it here</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
}