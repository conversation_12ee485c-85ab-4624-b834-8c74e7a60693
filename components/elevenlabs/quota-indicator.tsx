'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Zap, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  ExternalLink,
  TrendingUp,
  Users
} from 'lucide-react';
import { useUserManagement } from '@/hooks/use-elevenlabs';
import { formatNumber } from '@/lib/elevenlabs/utils';

interface QuotaIndicatorProps {
  className?: string;
  compact?: boolean;
}

export function QuotaIndicator({ className = '', compact = false }: QuotaIndicatorProps) {
  const { user, getUsageStatistics } = useUserManagement();
  const [usageStats, setUsageStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadUsageStats = async () => {
      try {
        const stats = await getUsageStatistics();
        setUsageStats(stats);
      } catch (error) {
        console.error('Failed to load usage statistics:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUsageStats();
  }, [getUsageStatistics]);

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-16 bg-muted rounded-lg" />
      </div>
    );
  }

  if (!user.subscription && !usageStats) {
    return null;
  }

  const subscription = user.subscription;
  const stats = usageStats;
  
  if (!subscription) return null;

  const usagePercentage = stats?.usage_percentage || 0;
  const charactersUsed = subscription.character_count || 0;
  const charactersLimit = subscription.character_limit || 0;
  const charactersRemaining = charactersLimit - charactersUsed;
  const resetDate = new Date(subscription.next_character_count_reset_unix * 1000);
  const daysUntilReset = stats?.days_until_reset || 0;

  const getStatusColor = (percentage: number) => {
    if (percentage >= 90) return 'destructive';
    if (percentage >= 75) return 'warning';
    return 'default';
  };

  const getStatusIcon = (percentage: number) => {
    if (percentage >= 90) return <AlertTriangle className="h-4 w-4" />;
    if (percentage >= 75) return <Clock className="h-4 w-4" />;
    return <CheckCircle className="h-4 w-4" />;
  };

  if (compact) {
    return (
      <Card className={className}>
        <CardContent className="p-3">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              {getStatusIcon(usagePercentage)}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium">
                  {formatNumber(charactersUsed)} / {formatNumber(charactersLimit)}
                </span>
                <Badge variant={getStatusColor(usagePercentage) as any}>
                  {usagePercentage}%
                </Badge>
              </div>
              <Progress value={usagePercentage} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-primary" />
              <h3 className="font-semibold">Usage & Quota</h3>
            </div>
            <Badge variant="outline">{subscription.tier}</Badge>
          </div>

          {/* Usage Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Character Usage</span>
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">
                  {formatNumber(charactersUsed)} / {formatNumber(charactersLimit)}
                </span>
                <Badge variant={getStatusColor(usagePercentage) as any}>
                  {usagePercentage}%
                </Badge>
              </div>
            </div>
            <Progress value={usagePercentage} className="h-3" />
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>{formatNumber(charactersRemaining)} remaining</span>
              <span>Resets in {daysUntilReset} days</span>
            </div>
          </div>

          {/* Quota Warnings */}
          {usagePercentage >= 90 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                You've used {usagePercentage}% of your quota. Consider upgrading your plan.
              </AlertDescription>
            </Alert>
          )}

          {usagePercentage >= 75 && usagePercentage < 90 && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                You've used {usagePercentage}% of your quota. Your quota resets on{' '}
                {resetDate.toLocaleDateString()}.
              </AlertDescription>
            </Alert>
          )}

          {/* Additional Stats */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {subscription.voice_limit || 0}
              </div>
              <div className="text-xs text-muted-foreground">Voice Limit</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {subscription.professional_voice_limit || 0}
              </div>
              <div className="text-xs text-muted-foreground">Pro Voice Limit</div>
            </div>
          </div>

          {/* Usage Trends */}
          {stats && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">Usage Trends</span>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">This Month:</span>
                  <span className="ml-2 font-medium">
                    {formatNumber(stats.current_period?.characters_used || 0)}
                  </span>
                </div>
                <div>
                  <span className="text-muted-foreground">Last Month:</span>
                  <span className="ml-2 font-medium">
                    {formatNumber(stats.previous_period?.characters_used || 0)}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open('https://elevenlabs.io/pricing', '_blank')}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Upgrade Plan
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open('https://elevenlabs.io/app/subscription', '_blank')}
            >
              <Users className="h-4 w-4 mr-2" />
              Manage Subscription
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}