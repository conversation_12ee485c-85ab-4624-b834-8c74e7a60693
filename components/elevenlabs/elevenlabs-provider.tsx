'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useElevenLabs } from '@/hooks/use-elevenlabs';
import { ErrorBoundary } from './error-boundary';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw } from 'lucide-react';

interface ElevenLabsContextType {
  isReady: boolean;
  error: string | null;
  retry: () => void;
}

const ElevenLabsContext = createContext<ElevenLabsContextType | undefined>(undefined);

export function useElevenLabsContext() {
  const context = useContext(ElevenLabsContext);
  if (context === undefined) {
    throw new Error('useElevenLabsContext must be used within an ElevenLabsProvider');
  }
  return context;
}

interface ElevenLabsProviderProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function ElevenLabsProvider({ children, fallback }: ElevenLabsProviderProps) {
  const { isConfigured, loadVoices, loadModels, loadUser } = useElevenLabs();
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const initialize = async () => {
    if (!isConfigured) {
      setIsReady(true);
      return;
    }

    try {
      setError(null);
      
      // Load initial data
      await Promise.all([
        loadVoices(),
        loadModels(),
        loadUser(),
      ]);
      
      setIsReady(true);
    } catch (err) {
      console.error('Failed to initialize ElevenLabs:', err);
      setError(err instanceof Error ? err.message : 'Failed to initialize ElevenLabs');
      setIsReady(false);
    }
  };

  useEffect(() => {
    initialize();
  }, [isConfigured, retryCount]);

  const retry = () => {
    setRetryCount(prev => prev + 1);
  };

  const contextValue: ElevenLabsContextType = {
    isReady,
    error,
    retry,
  };

  if (error && isConfigured) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>Failed to initialize ElevenLabs: {error}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={retry}
              className="ml-4"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
        {fallback}
      </div>
    );
  }

  if (!isReady && isConfigured) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 mx-auto mb-4 animate-spin" />
            <p className="text-muted-foreground">Initializing ElevenLabs...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <ElevenLabsContext.Provider value={contextValue}>
      <ErrorBoundary>
        {children}
      </ErrorBoundary>
    </ElevenLabsContext.Provider>
  );
}