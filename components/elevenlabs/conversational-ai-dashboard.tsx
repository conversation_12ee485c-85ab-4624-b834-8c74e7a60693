'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  MessageSquare, 
  Bot, 
  Users, 
  BarChart3, 
  Settings, 
  Play, 
  Plus,
  AlertCircle,
  CheckCircle,
  Clock,
  Zap,
  TrendingUp,
  PhoneCall,
  Mic,
  Volume2,
  Activity
} from 'lucide-react';
import { useElevenLabs } from '@/hooks/use-elevenlabs';
import { useConversationalAgents } from '@/hooks/use-conversational-ai';
import { ApiKeySetup } from './api-key-setup';
import { ConversationalAIChat } from './conversational-ai-chat';
import { ConversationalAgentBuilder } from './conversational-agent-builder';
import { formatDuration, formatNumber, formatDurationMs } from '@/lib/elevenlabs/utils';

interface ConversationalAIDashboardProps {
  className?: string;
}

export function ConversationalAIDashboard({ className = '' }: ConversationalAIDashboardProps) {
  const { isConfigured, apiKey } = useElevenLabs();
  const {
    agents,
    selectedAgent,
    isLoading,
    error,
    loadAgents,
    selectAgent,
    clearError
  } = useConversationalAgents();

  const [activeTab, setActiveTab] = useState('overview');
  const [isCreatingAgent, setIsCreatingAgent] = useState(false);
  const [isChatting, setIsChatting] = useState(false);
  const [selectedChatAgent, setSelectedChatAgent] = useState<string | null>(null);

  // Load agents when configured
  useEffect(() => {
    if (isConfigured) {
      loadAgents();
    }
  }, [isConfigured, loadAgents]);

  const handleStartChat = (agentId: string) => {
    setSelectedChatAgent(agentId);
    setIsChatting(true);
  };

  const handleCloseChat = () => {
    setIsChatting(false);
    setSelectedChatAgent(null);
  };

  if (!isConfigured) {
    return (
      <div className={`max-w-4xl mx-auto ${className}`}>
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight">ElevenLabs Conversational AI</h1>
          <p className="text-muted-foreground mt-2">
            Create real-time voice AI agents with advanced conversation capabilities
          </p>
        </div>
        
        <ApiKeySetup />
      </div>
    );
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Conversational AI</h1>
          <p className="text-muted-foreground mt-2">
            Manage your real-time voice AI agents
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={() => setIsCreatingAgent(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Agent
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Agents</CardTitle>
            <Bot className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{agents.length}</div>
            <p className="text-xs text-muted-foreground">
              {agents.filter(a => a.is_public).length} public
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
            <PhoneCall className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-xs text-muted-foreground">
              currently running
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Conversations</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-xs text-muted-foreground">
              this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0m</div>
            <p className="text-xs text-muted-foreground">
              per conversation
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="agents">Agents</TabsTrigger>
          <TabsTrigger value="chat">Live Chat</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Agents */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Agents</CardTitle>
                <CardDescription>
                  Your latest conversational AI agents
                </CardDescription>
              </CardHeader>
              <CardContent>
                {agents.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No agents created yet</p>
                    <p className="text-sm">Create your first conversational AI agent</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {agents.slice(0, 5).map((agent) => (
                      <div key={agent.agent_id} className="flex items-center gap-3 p-3 rounded-lg border">
                        <div className="flex-shrink-0">
                          <Bot className="h-8 w-8 text-primary" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium">{agent.name}</p>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <span>{agent.language}</span>
                            <span>•</span>
                            <span>{agent.model_id}</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">{agent.is_public ? 'Public' : 'Private'}</Badge>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleStartChat(agent.agent_id)}
                          >
                            <Play className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Get started with conversational AI
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  onClick={() => setIsCreatingAgent(true)}
                  className="w-full justify-start"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Agent
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => setActiveTab('agents')}
                  className="w-full justify-start"
                >
                  <Bot className="h-4 w-4 mr-2" />
                  Manage Agents
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => setActiveTab('chat')}
                  className="w-full justify-start"
                  disabled={agents.length === 0}
                >
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Start Conversation
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Features Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Conversational AI Features</CardTitle>
              <CardDescription>
                Advanced capabilities for natural voice interactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-yellow-500" />
                    <h4 className="font-semibold">Real-time Voice</h4>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Low-latency voice synthesis and recognition for natural conversations
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-green-500" />
                    <h4 className="font-semibold">Turn Taking</h4>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Advanced turn-taking models for natural conversation flow
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Mic className="h-5 w-5 text-blue-500" />
                    <h4 className="font-semibold">Voice Activity</h4>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Intelligent voice activity detection and silence handling
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Volume2 className="h-5 w-5 text-purple-500" />
                    <h4 className="font-semibold">Interruption Handling</h4>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Natural interruption detection and response management
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Bot className="h-5 w-5 text-orange-500" />
                    <h4 className="font-semibold">Custom Models</h4>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Integration with GPT, Claude, Gemini, or your own LLMs
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Settings className="h-5 w-5 text-gray-500" />
                    <h4 className="font-semibold">Fine-tuning</h4>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Detailed configuration for voice, conversation, and behavior
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Agents Tab */}
        <TabsContent value="agents">
          <ConversationalAgentBuilder
            apiKey={apiKey!}
            onAgentCreated={() => loadAgents()}
            onAgentUpdated={() => loadAgents()}
          />
        </TabsContent>

        {/* Live Chat Tab */}
        <TabsContent value="chat" className="space-y-6">
          {agents.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-semibold mb-2">No Agents Available</h3>
                <p className="text-muted-foreground mb-4">
                  Create your first conversational AI agent to start chatting
                </p>
                <Button onClick={() => setIsCreatingAgent(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Agent
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {/* Agent Selection */}
              <Card>
                <CardHeader>
                  <CardTitle>Select an Agent</CardTitle>
                  <CardDescription>
                    Choose a conversational AI agent to start a live chat session
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {agents.map(agent => (
                      <Card 
                        key={agent.agent_id}
                        className="cursor-pointer hover:bg-accent transition-colors"
                        onClick={() => handleStartChat(agent.agent_id)}
                      >
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm flex items-center gap-2">
                            <Bot className="h-4 w-4" />
                            {agent.name}
                          </CardTitle>
                          {agent.description && (
                            <CardDescription className="text-xs">
                              {agent.description}
                            </CardDescription>
                          )}
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="flex items-center gap-2 text-xs">
                            <Badge variant="secondary">{agent.language}</Badge>
                            <Badge variant="outline">{agent.model_id}</Badge>
                          </div>
                          <Button size="sm" className="w-full mt-3">
                            <Play className="h-4 w-4 mr-2" />
                            Start Chat
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Analytics
              </CardTitle>
              <CardDescription>
                Conversation insights and performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-muted-foreground">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-semibold mb-2">Analytics Coming Soon</h3>
                <p>
                  Detailed conversation analytics and performance metrics will be available here
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Agent Builder Modal */}
      {isCreatingAgent && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="max-w-6xl w-full max-h-[90vh] overflow-auto">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Create New Agent</CardTitle>
                  <Button variant="outline" onClick={() => setIsCreatingAgent(false)}>
                    Close
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <ConversationalAgentBuilder
                  apiKey={apiKey!}
                  onAgentCreated={(agent) => {
                    loadAgents();
                    setIsCreatingAgent(false);
                  }}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Chat Modal */}
      {isChatting && selectedChatAgent && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="max-w-4xl w-full max-h-[90vh] overflow-auto">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Live Chat Session</CardTitle>
                  <Button variant="outline" onClick={handleCloseChat}>
                    Close Chat
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <ConversationalAIChat
                  apiKey={apiKey!}
                  agentId={selectedChatAgent}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              onClick={clearError}
              className="ml-2"
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}