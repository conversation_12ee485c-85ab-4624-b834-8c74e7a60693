'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RotateCcw, Settings } from 'lucide-react';
import { VOICE_SETTINGS_DEFAULTS } from '@/lib/elevenlabs/constants';
import type { ElevenLabsVoice } from '@/lib/elevenlabs/types';

interface VoiceSettingsProps {
  settings: {
    stability: number;
    similarity_boost: number;
    style: number;
    use_speaker_boost: boolean;
  };
  onChange: (settings: VoiceSettingsProps['settings']) => void;
  voice?: ElevenLabsVoice | null;
  className?: string;
}

export function VoiceSettings({ 
  settings, 
  onChange, 
  voice, 
  className = '' 
}: VoiceSettingsProps) {
  const handleSettingChange = (key: keyof typeof settings, value: number | boolean) => {
    onChange({
      ...settings,
      [key]: value,
    });
  };

  const handleReset = () => {
    onChange({
      stability: voice?.settings?.stability || VOICE_SETTINGS_DEFAULTS.STABILITY,
      similarity_boost: voice?.settings?.similarity_boost || VOICE_SETTINGS_DEFAULTS.SIMILARITY_BOOST,
      style: voice?.settings?.style || VOICE_SETTINGS_DEFAULTS.STYLE,
      use_speaker_boost: voice?.settings?.use_speaker_boost || VOICE_SETTINGS_DEFAULTS.USE_SPEAKER_BOOST,
    });
  };

  const handleResetToDefaults = () => {
    onChange({
      stability: VOICE_SETTINGS_DEFAULTS.STABILITY,
      similarity_boost: VOICE_SETTINGS_DEFAULTS.SIMILARITY_BOOST,
      style: VOICE_SETTINGS_DEFAULTS.STYLE,
      use_speaker_boost: VOICE_SETTINGS_DEFAULTS.USE_SPEAKER_BOOST,
    });
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Voice Settings
            </CardTitle>
            <CardDescription>
              Fine-tune the voice characteristics for optimal results
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={!voice}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset to Voice Default
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleResetToDefaults}
            >
              Reset to System Default
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Stability */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="stability">Stability</Label>
            <Badge variant="secondary">{settings.stability.toFixed(2)}</Badge>
          </div>
          <Slider
            id="stability"
            min={0}
            max={1}
            step={0.01}
            value={[settings.stability]}
            onValueChange={([value]) => handleSettingChange('stability', value)}
            className="w-full"
          />
          <p className="text-sm text-muted-foreground">
            Lower values create more variable speech, higher values create more consistent speech. 
            Recommended: 0.5-0.8 for most voices.
          </p>
        </div>

        {/* Similarity Boost */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="similarity-boost">Similarity Boost</Label>
            <Badge variant="secondary">{settings.similarity_boost.toFixed(2)}</Badge>
          </div>
          <Slider
            id="similarity-boost"
            min={0}
            max={1}
            step={0.01}
            value={[settings.similarity_boost]}
            onValueChange={([value]) => handleSettingChange('similarity_boost', value)}
            className="w-full"
          />
          <p className="text-sm text-muted-foreground">
            Enhances similarity to the original voice. Higher values make the voice more similar to the original, 
            but may reduce audio quality.
          </p>
        </div>

        {/* Style */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="style">Style</Label>
            <Badge variant="secondary">{settings.style.toFixed(2)}</Badge>
          </div>
          <Slider
            id="style"
            min={0}
            max={1}
            step={0.01}
            value={[settings.style]}
            onValueChange={([value]) => handleSettingChange('style', value)}
            className="w-full"
          />
          <p className="text-sm text-muted-foreground">
            Amplifies the style of the original speaker. Higher values create more expressive and emotional speech.
            Use with caution as very high values can reduce quality.
          </p>
        </div>

        {/* Speaker Boost */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="speaker-boost">Speaker Boost</Label>
            <Switch
              id="speaker-boost"
              checked={settings.use_speaker_boost}
              onCheckedChange={(checked) => handleSettingChange('use_speaker_boost', checked)}
            />
          </div>
          <p className="text-sm text-muted-foreground">
            Boosts the similarity to the original speaker and reduces background noise. 
            Recommended for most use cases.
          </p>
        </div>

        {/* Voice-specific recommendations */}
        {voice && (
          <div className="p-4 bg-muted rounded-lg space-y-2">
            <h4 className="font-semibold text-sm">Voice-Specific Recommendations</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Default Stability:</span>
                <span className="ml-2 font-medium">{voice.settings?.stability?.toFixed(2) || 'N/A'}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Default Similarity:</span>
                <span className="ml-2 font-medium">{voice.settings?.similarity_boost?.toFixed(2) || 'N/A'}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Default Style:</span>
                <span className="ml-2 font-medium">{voice.settings?.style?.toFixed(2) || 'N/A'}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Speaker Boost:</span>
                <span className="ml-2 font-medium">{voice.settings?.use_speaker_boost ? 'On' : 'Off'}</span>
              </div>
            </div>
          </div>
        )}

        {/* Quick Presets */}
        <div className="space-y-3">
          <Label>Quick Presets</Label>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onChange({
                stability: 0.8,
                similarity_boost: 0.3,
                style: 0.0,
                use_speaker_boost: true,
              })}
            >
              🎙️ Podcast
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onChange({
                stability: 0.6,
                similarity_boost: 0.7,
                style: 0.2,
                use_speaker_boost: true,
              })}
            >
              📚 Audiobook
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onChange({
                stability: 0.4,
                similarity_boost: 0.5,
                style: 0.6,
                use_speaker_boost: true,
              })}
            >
              🎭 Storytelling
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onChange({
                stability: 0.7,
                similarity_boost: 0.4,
                style: 0.1,
                use_speaker_boost: true,
              })}
            >
              📢 Commercial
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}