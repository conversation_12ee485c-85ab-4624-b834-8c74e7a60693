import React, { useState } from "react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Co<PERSON>, Sparkles } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface GeneratedContent {
  instagram: string;
  twitter: string;
  tiktok: string;
  youtube: string;
  facebook: string;
}

export function MarketingAI() {
  const [trackName, setTrackName] = useState("");
  const [artist, setArtist] = useState("");
  const [genre, setGenre] = useState("");
  const [tone, setTone] = useState("exciting");
  const [additionalContext, setAdditionalContext] = useState("");
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent>({
    instagram: "",
    twitter: "",
    tiktok: "",
    youtube: "",
    facebook: "",
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState("instagram");
  const { toast } = useToast();

  const platforms = [
    { id: "instagram", name: "Instagram", icon: "📸" },
    { id: "twitter", name: "Twitter", icon: "🐦" },
    { id: "tiktok", name: "TikTok", icon: "🎵" },
    { id: "youtube", name: "YouTube", icon: "📺" },
    { id: "facebook", name: "Facebook", icon: "👥" },
  ];

  const generateContent = async () => {
    if (!trackName.trim() || !artist.trim()) {
      toast({
        title: "Missing Information",
        description: "Please provide both track name and artist name.",
        variant: "destructive",
      });
      return;
    }
    
    setIsGenerating(true);
    
    try {
      // Generate content for all platforms
      const contentPromises = platforms.map(async (platform) => {
        const response = await fetch('/api/ai/generate-content', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            trackName,
            artist,
            genre,
            platform: platform.id,
            contentType: 'caption',
            tone,
            additionalContext,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to generate content for ${platform.name}`);
        }

        const data = await response.json();
        return { platform: platform.id, content: data.content };
      });

      const results = await Promise.all(contentPromises);
      
      const newContent: GeneratedContent = {
        instagram: "",
        twitter: "",
        tiktok: "",
        youtube: "",
        facebook: "",
      };

      results.forEach((result) => {
        newContent[result.platform as keyof GeneratedContent] = result.content;
      });

      setGeneratedContent(newContent);
      
      toast({
        title: "Content Generated Successfully",
        description: "AI-powered marketing content is ready for all platforms!",
      });

    } catch (error) {
      console.error('Error generating content:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate content. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = async (content: string, platform: string) => {
    try {
      await navigator.clipboard.writeText(content);
      toast({
        title: "Copied!",
        description: `${platform} content copied to clipboard.`,
      });
    } catch (error) {
      console.error('Failed to copy:', error);
      toast({
        title: "Copy Failed",
        description: "Failed to copy content to clipboard.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container mx-auto px-4">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-blue-600" />
                <CardTitle>AI Marketing Assistant</CardTitle>
              </div>
              <CardDescription>Generate platform-specific marketing content powered by AI</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="trackName">Track Name</Label>
                  <Input 
                    id="trackName"
                    placeholder="Enter your track name..."
                    value={trackName}
                    onChange={(e) => setTrackName(e.target.value)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="artist">Artist Name</Label>
                  <Input 
                    id="artist"
                    placeholder="Enter artist name..."
                    value={artist}
                    onChange={(e) => setArtist(e.target.value)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="genre">Genre</Label>
                  <Input 
                    id="genre"
                    placeholder="e.g., Pop, Hip-Hop, Rock..."
                    value={genre}
                    onChange={(e) => setGenre(e.target.value)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="tone">Tone</Label>
                  <Select value={tone} onValueChange={setTone}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select tone" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="exciting">Exciting</SelectItem>
                      <SelectItem value="professional">Professional</SelectItem>
                      <SelectItem value="casual">Casual</SelectItem>
                      <SelectItem value="mysterious">Mysterious</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="context">Additional Context (Optional)</Label>
                  <Textarea 
                    id="context"
                    placeholder="Any additional details about your release..."
                    value={additionalContext}
                    onChange={(e) => setAdditionalContext(e.target.value)}
                    className="min-h-20"
                  />
                </div>
                
                <Button 
                  className="w-full" 
                  onClick={generateContent} 
                  disabled={!trackName.trim() || !artist.trim() || isGenerating}
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-4 w-4" />
                      Generate Content
                    </>
                  )}
                </Button>
                
                <div className="text-center">
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                    GPT-4 Powered
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="lg:col-span-2">
          <Card className="h-full">
            <CardHeader>
              <CardTitle>Generated Content</CardTitle>
              <CardDescription>Platform-optimized marketing content</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-4 grid grid-cols-5 w-full">
                  {platforms.map((platform) => (
                    <TabsTrigger key={platform.id} value={platform.id} className="text-xs">
                      {platform.icon} {platform.name}
                    </TabsTrigger>
                  ))}
                </TabsList>
                
                {platforms.map((platform) => (
                  <TabsContent key={platform.id} value={platform.id} className="h-64 overflow-auto">
                    {generatedContent[platform.id as keyof GeneratedContent] ? (
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">{platform.name} Content</h4>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(generatedContent[platform.id as keyof GeneratedContent], platform.name)}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                        </div>
                        <div className="border rounded-md p-4 bg-background">
                          <p className="whitespace-pre-wrap text-sm">
                            {generatedContent[platform.id as keyof GeneratedContent]}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-full text-muted-foreground">
                        <div className="text-center">
                          <p className="mb-2">No content generated yet</p>
                          <p className="text-sm">Fill in the details and click &quot;Generate Content&quot; to see {platform.name}-optimized content</p>
                        </div>
                      </div>
                    )}
                  </TabsContent>
                ))}
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 