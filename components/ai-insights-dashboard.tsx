'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';

import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Target, 
  Lightbulb, 
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Loader2,
  RefreshCw,
  Eye,
  Brain
} from 'lucide-react';

interface Insight {
  type: 'audience_growth' | 'engagement' | 'revenue' | 'content_performance';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  actionable: boolean;
  metric: {
    value: number;
    unit: string;
    trend: 'increasing' | 'decreasing' | 'stable';
    comparison: string;
  };
}

interface Recommendation {
  category: 'content' | 'marketing' | 'release' | 'audience';
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  estimatedImpact: string;
  timeframe: string;
  steps: string[];
}

interface Prediction {
  metric: string;
  timeframe: string;
  prediction: string;
  confidence: number;
  factors: string[];
}

interface Opportunity {
  title: string;
  description: string;
  marketSize: string;
  difficulty: 'easy' | 'medium' | 'hard';
  timeline: string;
}

interface AnalyticsData {
  insights: Insight[];
  recommendations: Recommendation[];
  predictions: Prediction[];
  opportunities: Opportunity[];
  metadata: {
    model: string;
    timestamp: string;
    dataPoints: number;
  };
}

export function AIInsightsDashboard() {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeframe] = useState('30d');
  const [focusAreas] = useState(['audience_growth', 'engagement']);

  const fetchInsights = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/ai/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          timeframe,
          focusAreas,
          genre: 'pop', // This could be dynamic based on user profile
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch insights');
      }

      const result = await response.json();
      setData(result);
    } catch (err) {
      console.error('Error fetching insights:', err);
      setError('Failed to load AI insights. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchInsights();
  }, [timeframe, focusAreas, fetchInsights]);

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'hard': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'decreasing': return <TrendingDown className="h-4 w-4 text-red-600" />;
      case 'stable': return <BarChart3 className="h-4 w-4 text-blue-600" />;
      default: return null;
    }
  };

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
            <p className="text-sm text-muted-foreground mb-4">{error}</p>
            <Button onClick={fetchInsights} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-blue-600" />
              <CardTitle>AI-Powered Insights</CardTitle>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-blue-50 text-blue-700">
                {data ? 'Updated' : 'Loading...'}
              </Badge>
              <Button 
                onClick={fetchInsights} 
                disabled={isLoading}
                variant="outline"
                size="sm"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </CardHeader>

        {isLoading && !data ? (
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">Analyzing your data...</p>
            </div>
          </CardContent>
        ) : data ? (
          <CardContent>
            <Tabs defaultValue="insights" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="insights">Insights</TabsTrigger>
                <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
                <TabsTrigger value="predictions">Predictions</TabsTrigger>
                <TabsTrigger value="opportunities">Opportunities</TabsTrigger>
              </TabsList>

              <TabsContent value="insights" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {data.insights.map((insight, index) => (
                    <Card key={index} className="border-l-4 border-l-blue-500">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            {insight.type === 'audience_growth' && <Users className="h-4 w-4 text-blue-600" />}
                            {insight.type === 'engagement' && <Eye className="h-4 w-4 text-green-600" />}
                            {insight.type === 'revenue' && <TrendingUp className="h-4 w-4 text-purple-600" />}
                            {insight.type === 'content_performance' && <BarChart3 className="h-4 w-4 text-orange-600" />}
                            <h4 className="font-medium text-sm">{insight.title}</h4>
                          </div>
                          <Badge variant="outline" className={getImpactColor(insight.impact)}>
                            {insight.impact}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">{insight.description}</p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getTrendIcon(insight.metric.trend)}
                            <span className="text-lg font-bold">{insight.metric.value}</span>
                            <span className="text-sm text-muted-foreground">{insight.metric.unit}</span>
                          </div>
                          <span className="text-xs text-muted-foreground">{insight.metric.comparison}</span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="recommendations" className="space-y-4">
                {data.recommendations.map((rec, index) => (
                  <Card key={index} className={`border-l-4 ${
                    rec.priority === 'high' ? 'border-l-red-500' :
                    rec.priority === 'medium' ? 'border-l-yellow-500' : 'border-l-green-500'
                  }`}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Target className="h-4 w-4 text-blue-600" />
                          <h4 className="font-medium">{rec.title}</h4>
                        </div>
                        <Badge variant="outline" className={getPriorityColor(rec.priority)}>
                          {rec.priority} priority
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">{rec.description}</p>
                      <div className="grid grid-cols-2 gap-4 mb-3">
                        <div>
                          <span className="text-xs font-medium text-muted-foreground">Estimated Impact</span>
                          <p className="text-sm">{rec.estimatedImpact}</p>
                        </div>
                        <div>
                          <span className="text-xs font-medium text-muted-foreground">Timeframe</span>
                          <p className="text-sm">{rec.timeframe}</p>
                        </div>
                      </div>
                      <div>
                        <span className="text-xs font-medium text-muted-foreground">Action Steps</span>
                        <ul className="mt-1 space-y-1">
                          {rec.steps.map((step, stepIndex) => (
                            <li key={stepIndex} className="text-sm text-muted-foreground flex items-start gap-2">
                              <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                              {step}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>

              <TabsContent value="predictions" className="space-y-4">
                {data.predictions.map((pred, index) => (
                  <Card key={index} className="border-l-4 border-l-purple-500">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-purple-600" />
                          <h4 className="font-medium">{pred.metric}</h4>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">Confidence</span>
                          <Progress value={pred.confidence} className="w-16" />
                          <span className="text-sm font-medium">{pred.confidence}%</span>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{pred.timeframe}</p>
                      <p className="text-sm font-medium mb-3">{pred.prediction}</p>
                      <div>
                        <span className="text-xs font-medium text-muted-foreground">Key Factors</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {pred.factors.map((factor, factorIndex) => (
                            <Badge key={factorIndex} variant="secondary" className="text-xs">
                              {factor}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>

              <TabsContent value="opportunities" className="space-y-4">
                {data.opportunities.map((opp, index) => (
                  <Card key={index} className="border-l-4 border-l-green-500">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Lightbulb className="h-4 w-4 text-green-600" />
                          <h4 className="font-medium">{opp.title}</h4>
                        </div>
                        <Badge variant="outline" className={getDifficultyColor(opp.difficulty)}>
                          {opp.difficulty}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">{opp.description}</p>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <span className="text-xs font-medium text-muted-foreground">Market Size</span>
                          <p className="text-sm">{opp.marketSize}</p>
                        </div>
                        <div>
                          <span className="text-xs font-medium text-muted-foreground">Timeline</span>
                          <p className="text-sm">{opp.timeline}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>
            </Tabs>
          </CardContent>
        ) : null}
      </Card>

      {data && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>Analysis powered by {data.metadata.model}</span>
              <span>Based on {data.metadata.dataPoints} data points</span>
              <span>Last updated: {new Date(data.metadata.timestamp).toLocaleString()}</span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}