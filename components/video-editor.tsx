'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { VideoSystem } from '@/lib/video';
import { 
  VideoSystemConfig, 
  VideoClip, 
  Timeline as TimelineType, 
  RenderSettings,
  AIVideoGenerationRequest,
  TTSRequest 
} from '@/lib/video/types/video-types';
import { EffectsLibrary, EffectPreset } from '@/lib/video/editor/effects';
import { TransitionsLibrary, TransitionPreset } from '@/lib/video/editor/transitions';
import { VideoUtils } from '@/lib/video/utils/video-utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Play, 
  Pause, 
  Square, 
  SkipBack, 
  SkipForward,
  Upload,
  Download,
  Scissors,
  Copy,
  Trash2,
  Wand2,
  Mic,
  Video,
  Image,
  Type,
  Layers,
  Settings,
  Undo,
  Redo,
  ZoomIn,
  ZoomOut
} from 'lucide-react';
import { toast } from 'sonner';

interface VideoEditorProps {
  config?: Partial<VideoSystemConfig>;
  onProjectSave?: (projectData: string) => void;
  onVideoExport?: (videoUrl: string) => void;
}

export function VideoEditor({ config = {}, onProjectSave, onVideoExport }: VideoEditorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoSystemRef = useRef<VideoSystem | null>(null);
  
  const [isInitialized, setIsInitialized] = useState(false);
  const [timeline, setTimeline] = useState<TimelineType | null>(null);
  const [selectedClips, setSelectedClips] = useState<string[]>([]);
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [zoom, setZoom] = useState(1);
  const [renderProgress, setRenderProgress] = useState(0);
  const [isRendering, setIsRendering] = useState(false);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);

  // AI Generation States
  const [aiPrompt, setAiPrompt] = useState('');
  const [aiModel, setAiModel] = useState('runway');
  const [aiProgress, setAiProgress] = useState(0);
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);

  // TTS States
  const [ttsText, setTtsText] = useState('');
  const [ttsVoice, setTtsVoice] = useState('alloy');
  const [ttsProvider, setTtsProvider] = useState<'elevenlabs' | 'openai'>('openai');
  const [isGeneratingTTS, setIsGeneratingTTS] = useState(false);

  // Effects and Transitions
  const [effectPresets] = useState<EffectPreset[]>(EffectsLibrary.getPresets());
  const [transitionPresets] = useState<TransitionPreset[]>(TransitionsLibrary.getPresets());

  // Initialize Video System
  useEffect(() => {
    const initializeVideoSystem = async () => {
      try {
        const defaultConfig: VideoSystemConfig = {
          tempDirectory: '/tmp/video-editor',
          maxFileSize: 1024 * 1024 * 1024, // 1GB
          supportedFormats: ['mp4', 'webm', 'mov'],
          webcodecs: {
            enabled: true,
            hardwareAcceleration: true,
          },
          ...config,
        };

        const videoSystem = new VideoSystem(defaultConfig);
        videoSystemRef.current = videoSystem;

        // Initialize canvas
        if (canvasRef.current) {
          await videoSystem.initializeCanvas(canvasRef.current);
        }

        // Set up event listeners
        videoSystem.on('timeline:created', ({ timeline }) => {
          setTimeline(timeline);
        });

        videoSystem.on('clip:added', ({ clip }) => {
          if (timeline) {
            setTimeline({ ...timeline, clips: [...timeline.clips, clip] });
          }
        });

        videoSystem.on('playback:timeupdate', ({ currentTime }) => {
          setCurrentTime(currentTime);
        });

        videoSystem.on('playback:started', () => {
          setIsPlaying(true);
        });

        videoSystem.on('playback:paused', () => {
          setIsPlaying(false);
        });

        videoSystem.on('playback:stopped', () => {
          setIsPlaying(false);
          setCurrentTime(0);
        });

        videoSystem.on('render:progress', ({ progress }) => {
          setRenderProgress(progress);
        });

        videoSystem.on('render:started', () => {
          setIsRendering(true);
        });

        videoSystem.on('render:completed', ({ output }) => {
          setIsRendering(false);
          setRenderProgress(0);
          toast.success('Video rendered successfully!');
          if (onVideoExport) {
            onVideoExport(output);
          }
        });

        videoSystem.on('render:failed', ({ error }) => {
          setIsRendering(false);
          setRenderProgress(0);
          toast.error(`Render failed: ${error}`);
        });

        videoSystem.on('ai:generation:progress', ({ progress }) => {
          setAiProgress(progress);
        });

        videoSystem.on('ai:generation:completed', ({ result }) => {
          setIsGeneratingAI(false);
          setAiProgress(0);
          toast.success('AI video generated successfully!');
          
          // Add generated video as clip
          if (result.videoUrl) {
            handleAddClip({
              type: 'ai-generated',
              source: result.videoUrl,
              startTime: 0,
              endTime: result.duration || 5,
              duration: result.duration || 5,
              track: 0,
              position: currentTime,
            });
          }
        });

        videoSystem.on('ai:generation:failed', ({ error }) => {
          setIsGeneratingAI(false);
          setAiProgress(0);
          toast.error(`AI generation failed: ${error}`);
        });

        videoSystem.on('history:changed', ({ canUndo, canRedo }) => {
          setCanUndo(canUndo);
          setCanRedo(canRedo);
        });

        // Create initial timeline
        const initialTimeline = await videoSystem.createTimeline('New Project', 60, 30);
        setTimeline(initialTimeline);
        setIsInitialized(true);

      } catch (error) {
        console.error('Failed to initialize video system:', error);
        toast.error('Failed to initialize video editor');
      }
    };

    initializeVideoSystem();

    return () => {
      if (videoSystemRef.current) {
        videoSystemRef.current.dispose();
      }
    };
  }, [config]);

  // Playback Controls
  const handlePlay = useCallback(async () => {
    if (videoSystemRef.current) {
      await videoSystemRef.current.play();
    }
  }, []);

  const handlePause = useCallback(async () => {
    if (videoSystemRef.current) {
      await videoSystemRef.current.pause();
    }
  }, []);

  const handleStop = useCallback(async () => {
    if (videoSystemRef.current) {
      await videoSystemRef.current.stop();
    }
  }, []);

  const handleSeek = useCallback(async (time: number) => {
    if (videoSystemRef.current) {
      await videoSystemRef.current.seek(time);
    }
  }, []);

  // File Handling
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    Array.from(files).forEach(async (file) => {
      try {
        const fileUrl = URL.createObjectURL(file);
        let clipType: VideoClip['type'] = 'video';

        if (VideoUtils.isValidVideoFormat(file.name)) {
          clipType = 'video';
        } else if (VideoUtils.isValidAudioFormat(file.name)) {
          clipType = 'audio';
        } else if (VideoUtils.isValidImageFormat(file.name)) {
          clipType = 'image';
        } else {
          toast.error(`Unsupported file format: ${file.name}`);
          return;
        }

        await handleAddClip({
          type: clipType,
          source: fileUrl,
          startTime: 0,
          endTime: clipType === 'image' ? 5 : 10, // Default durations
          duration: clipType === 'image' ? 5 : 10,
          track: 0,
          position: currentTime,
        });

        toast.success(`Added ${file.name} to timeline`);
      } catch (error) {
        toast.error(`Failed to add ${file.name}: ${error}`);
      }
    });
  }, [currentTime]);

  const handleAddClip = useCallback(async (clipData: Omit<VideoClip, 'id'>) => {
    if (videoSystemRef.current) {
      await videoSystemRef.current.addClip(clipData);
    }
  }, []);

  // AI Video Generation
  const handleGenerateAIVideo = useCallback(async () => {
    if (!aiPrompt.trim() || !videoSystemRef.current) return;

    setIsGeneratingAI(true);
    setAiProgress(0);

    try {
      const request: AIVideoGenerationRequest = {
        prompt: aiPrompt,
        duration: 5,
        resolution: { width: 1920, height: 1080, aspectRatio: '16:9' },
        fps: 30,
        model: aiModel as any,
      };

      await videoSystemRef.current.generateVideo(request);
    } catch (error) {
      setIsGeneratingAI(false);
      setAiProgress(0);
      toast.error(`AI generation failed: ${error}`);
    }
  }, [aiPrompt, aiModel]);

  // TTS Generation
  const handleGenerateTTS = useCallback(async () => {
    if (!ttsText.trim() || !videoSystemRef.current) return;

    setIsGeneratingTTS(true);

    try {
      const request: TTSRequest = {
        text: ttsText,
        voice: ttsVoice,
        language: 'en-US',
        speed: 1.0,
        pitch: 0,
        volume: 1.0,
        provider: ttsProvider,
      };

      const audioUrl = await videoSystemRef.current.generateTTS(request);
      
      // Add TTS audio as clip
      await handleAddClip({
        type: 'audio',
        source: audioUrl,
        startTime: 0,
        endTime: 10, // Estimate duration
        duration: 10,
        track: 1, // Audio track
        position: currentTime,
      });

      toast.success('TTS audio generated and added to timeline');
    } catch (error) {
      toast.error(`TTS generation failed: ${error}`);
    } finally {
      setIsGeneratingTTS(false);
    }
  }, [ttsText, ttsVoice, ttsProvider, currentTime]);

  // Render Video
  const handleRender = useCallback(async () => {
    if (!videoSystemRef.current || !timeline) return;

    const renderSettings: RenderSettings = {
      resolution: { width: 1920, height: 1080, aspectRatio: '16:9' },
      fps: 30,
      bitrate: 5000000,
      codec: 'h264',
      format: 'mp4',
      quality: 'high',
      hardwareAcceleration: true,
    };

    try {
      await videoSystemRef.current.renderVideo(renderSettings);
    } catch (error) {
      toast.error(`Render failed: ${error}`);
    }
  }, [timeline]);

  // Undo/Redo
  const handleUndo = useCallback(() => {
    if (videoSystemRef.current) {
      videoSystemRef.current.undo();
    }
  }, []);

  const handleRedo = useCallback(() => {
    if (videoSystemRef.current) {
      videoSystemRef.current.redo();
    }
  }, []);

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Initializing Video Editor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="video-editor h-screen flex flex-col">
      {/* Header */}
      <div className="border-b p-4 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-bold">Video Editor</h1>
          <Badge variant="secondary">
            {timeline?.name || 'Untitled Project'}
          </Badge>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleUndo}
            disabled={!canUndo}
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRedo}
            disabled={!canRedo}
          >
            <Redo className="h-4 w-4" />
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button
            onClick={handleRender}
            disabled={isRendering}
            size="sm"
          >
            <Download className="h-4 w-4 mr-2" />
            {isRendering ? 'Rendering...' : 'Export'}
          </Button>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Left Sidebar - Tools */}
        <div className="w-80 border-r bg-muted/30">
          <Tabs defaultValue="media" className="h-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="media">Media</TabsTrigger>
              <TabsTrigger value="ai">AI</TabsTrigger>
              <TabsTrigger value="effects">Effects</TabsTrigger>
              <TabsTrigger value="audio">Audio</TabsTrigger>
            </TabsList>

            <TabsContent value="media" className="p-4 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Import Media</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Files
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="video/*,audio/*,image/*"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Add Elements</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button variant="outline" className="w-full justify-start">
                    <Type className="h-4 w-4 mr-2" />
                    Text
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Image className="h-4 w-4 mr-2" />
                    Image
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Layers className="h-4 w-4 mr-2" />
                    Shape
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="ai" className="p-4 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">AI Video Generation</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="ai-prompt">Prompt</Label>
                    <Textarea
                      id="ai-prompt"
                      placeholder="Describe the video you want to generate..."
                      value={aiPrompt}
                      onChange={(e) => setAiPrompt(e.target.value)}
                      rows={3}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="ai-model">Model</Label>
                    <Select value={aiModel} onValueChange={setAiModel}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="runway">Runway ML</SelectItem>
                        <SelectItem value="pika">Pika Labs</SelectItem>
                        <SelectItem value="stability">Stability AI</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Button
                    onClick={handleGenerateAIVideo}
                    disabled={isGeneratingAI || !aiPrompt.trim()}
                    className="w-full"
                  >
                    <Wand2 className="h-4 w-4 mr-2" />
                    {isGeneratingAI ? 'Generating...' : 'Generate Video'}
                  </Button>

                  {isGeneratingAI && (
                    <Progress value={aiProgress} className="w-full" />
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="effects" className="p-4 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Video Effects</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-2">
                    {effectPresets.slice(0, 8).map((preset) => (
                      <Button
                        key={preset.name}
                        variant="outline"
                        size="sm"
                        className="text-xs"
                        onClick={() => {
                          // Add effect to selected clip
                          toast.info(`Applied ${preset.name} effect`);
                        }}
                      >
                        {preset.name}
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Transitions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-2">
                    {transitionPresets.slice(0, 8).map((preset) => (
                      <Button
                        key={preset.name}
                        variant="outline"
                        size="sm"
                        className="text-xs"
                        onClick={() => {
                          toast.info(`Applied ${preset.name} transition`);
                        }}
                      >
                        {preset.name}
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="audio" className="p-4 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Text-to-Speech</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="tts-text">Text</Label>
                    <Textarea
                      id="tts-text"
                      placeholder="Enter text to convert to speech..."
                      value={ttsText}
                      onChange={(e) => setTtsText(e.target.value)}
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor="tts-provider">Provider</Label>
                    <Select value={ttsProvider} onValueChange={(value: any) => setTtsProvider(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="openai">OpenAI</SelectItem>
                        <SelectItem value="elevenlabs">ElevenLabs</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="tts-voice">Voice</Label>
                    <Select value={ttsVoice} onValueChange={setTtsVoice}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="alloy">Alloy</SelectItem>
                        <SelectItem value="echo">Echo</SelectItem>
                        <SelectItem value="fable">Fable</SelectItem>
                        <SelectItem value="onyx">Onyx</SelectItem>
                        <SelectItem value="nova">Nova</SelectItem>
                        <SelectItem value="shimmer">Shimmer</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Button
                    onClick={handleGenerateTTS}
                    disabled={isGeneratingTTS || !ttsText.trim()}
                    className="w-full"
                  >
                    <Mic className="h-4 w-4 mr-2" />
                    {isGeneratingTTS ? 'Generating...' : 'Generate Speech'}
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Preview */}
          <div className="flex-1 bg-black flex items-center justify-center p-4">
            <canvas
              ref={canvasRef}
              width={1920}
              height={1080}
              className="max-w-full max-h-full border border-gray-600"
              style={{ aspectRatio: '16/9' }}
            />
          </div>

          {/* Controls */}
          <div className="border-t p-4 space-y-4">
            {/* Playback Controls */}
            <div className="flex items-center justify-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSeek(Math.max(0, currentTime - 10))}
              >
                <SkipBack className="h-4 w-4" />
              </Button>
              
              <Button
                onClick={isPlaying ? handlePause : handlePlay}
                size="sm"
              >
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleStop}
              >
                <Square className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSeek(Math.min(timeline?.duration || 60, currentTime + 10))}
              >
                <SkipForward className="h-4 w-4" />
              </Button>
            </div>

            {/* Timeline Scrubber */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <span>{VideoUtils.formatDuration(currentTime)}</span>
                <span>{VideoUtils.formatDuration(timeline?.duration || 0)}</span>
              </div>
              
              <Slider
                value={[currentTime]}
                onValueChange={([value]) => handleSeek(value)}
                max={timeline?.duration || 60}
                step={0.1}
                className="w-full"
              />
            </div>

            {/* Zoom Controls */}
            <div className="flex items-center justify-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setZoom(Math.max(0.1, zoom - 0.1))}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              
              <span className="text-sm min-w-16 text-center">
                {Math.round(zoom * 100)}%
              </span>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setZoom(Math.min(5, zoom + 0.1))}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </div>

            {/* Render Progress */}
            {isRendering && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Rendering video...</span>
                  <span>{Math.round(renderProgress)}%</span>
                </div>
                <Progress value={renderProgress} className="w-full" />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}