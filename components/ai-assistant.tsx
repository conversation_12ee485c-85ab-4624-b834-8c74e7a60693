'use client';

import { useChat } from '@ai-sdk/react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';

import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Send, Bot, Loader2, Music, TrendingUp, MessageCircle } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { UserAvatar } from '@/components/user-avatar';
import { GeneratedContent } from './ai-generated-content';

export function AIAssistant() {
  const { data: session } = useSession();
  
  const { messages, input, handleInputChange, handleSubmit, isLoading, stop } = useChat({
    api: '/api/ai/chat',
    maxSteps: 5,
  });

  const quickPrompts = [
    "Generate a song concept for a pop track",
    "Analyze current hip-hop trends",
    "Create Instagram content for my new release",
    "Help me plan a release timeline",
    "Suggest chord progressions for a ballad",
    "What's the best time to release music?",
  ];

  return (
    <div className="flex flex-col h-full">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-blue-600" />
            <CardTitle className="text-lg">AI Music Assistant</CardTitle>
          </div>
          <Badge variant="secondary" className="bg-blue-50 text-blue-700">
            GPT-4 Powered
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="flex flex-col h-full p-0">
        {messages.length === 0 ? (
          <div className="flex-1 flex flex-col items-center justify-center p-6 text-center">
            <Bot className="h-12 w-12 text-blue-500 mb-4" />
            <h3 className="text-lg font-medium mb-2">Welcome to your AI Music Assistant</h3>
            <p className="text-sm text-muted-foreground mb-6 max-w-md">
              Get help with song concepts, marketing strategies, release planning, and industry insights. 
              Ask me anything about music production and promotion!
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 w-full max-w-2xl">
              {quickPrompts.map((prompt, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="h-auto p-3 text-left justify-start whitespace-normal"
                  onClick={() => {
                    const syntheticEvent = {
                      preventDefault: () => {},
                    };
                    handleSubmit(syntheticEvent, { 
                      data: { prompt } 
                    });
                  }}
                >
                  <div className="flex items-center gap-2">
                    {index < 2 && <Music className="h-4 w-4 flex-shrink-0" />}
                    {index >= 2 && index < 4 && <TrendingUp className="h-4 w-4 flex-shrink-0" />}
                    {index >= 4 && <MessageCircle className="h-4 w-4 flex-shrink-0" />}
                    <span className="text-sm">{prompt}</span>
                  </div>
                </Button>
              ))}
            </div>
          </div>
        ) : (
          <ScrollArea className="flex-1 px-4">
            <div className="space-y-4 py-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex gap-3 ${
                    message.role === 'user' ? 'justify-end' : 'justify-start'
                  }`}
                >
                  {message.role === 'assistant' && (
                    <Avatar className="h-8 w-8 bg-blue-100">
                      <AvatarFallback>
                        <Bot className="h-4 w-4 text-blue-600" />
                      </AvatarFallback>
                    </Avatar>
                  )}
                  
                  <div className={`max-w-[80%] ${message.role === 'user' ? 'order-2' : ''}`}>
                    <div
                      className={`rounded-lg p-3 ${
                        message.role === 'user'
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                    </div>
                    
                    {/* Render tool invocations */}
                    {message.toolInvocations && message.toolInvocations.length > 0 && (
                      <div className="mt-2 space-y-2">
                        {message.toolInvocations.map((toolInvocation) => (
                          <div key={toolInvocation.toolCallId}>
                            {toolInvocation.state === 'result' && (
                              <GeneratedContent toolInvocation={toolInvocation} />
                            )}
                            {toolInvocation.state === 'call' && (
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <Loader2 className="h-4 w-4 animate-spin" />
                                <span>
                                  {toolInvocation.toolName === 'generateSongConcept' && 'Generating song concept...'}
                                  {toolInvocation.toolName === 'analyzeMarketTrends' && 'Analyzing market trends...'}
                                  {toolInvocation.toolName === 'generateMarketingContent' && 'Creating marketing content...'}
                                  {toolInvocation.toolName === 'generateReleasePlan' && 'Planning release strategy...'}
                                </span>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  
                  {message.role === 'user' && (
                    <UserAvatar user={session?.user} size="md" />
                  )}
                </div>
              ))}
              
              {isLoading && (
                <div className="flex gap-3 justify-start">
                  <Avatar className="h-8 w-8 bg-blue-100">
                    <AvatarFallback>
                      <Bot className="h-4 w-4 text-blue-600" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="bg-gray-100 rounded-lg p-3">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Thinking...</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>
        )}

        <div className="border-t p-4">
          <form onSubmit={handleSubmit} className="flex gap-2">
            <Input
              value={input}
              onChange={handleInputChange}
              placeholder="Ask about music production, marketing, or release strategies..."
              className="flex-1"
              disabled={isLoading}
            />
            <Button type="submit" disabled={isLoading || !input.trim()}>
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
            {isLoading && (
              <Button type="button" variant="outline" onClick={stop}>
                Stop
              </Button>
            )}
          </form>
        </div>
      </CardContent>
    </div>
  );
}