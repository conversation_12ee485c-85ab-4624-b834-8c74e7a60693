import Image from "next/image"
import Link from "next/link"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ChevronRight, Music, Video, Image as ImageIcon, FileText } from "lucide-react"

interface ContentRelatedProps {
  contentId: string
}

export function ContentRelated({ contentId }: ContentRelatedProps) {
  // Dummy related content items
  const relatedItems = [
    {
      id: "item1",
      title: "Summer Remix",
      type: "audio",
      thumbnail: null
    },
    {
      id: "item2",
      title: "Behind the Scenes",
      type: "video",
      thumbnail: null
    },
    {
      id: "item3",
      title: "Album Cover Art",
      type: "image",
      thumbnail: null
    }
  ]
  
  const getTypeIcon = (type: string) => {
    switch(type) {
      case "audio": return <Music className="h-4 w-4 text-primary" />
      case "video": return <Video className="h-4 w-4 text-primary" />
      case "image": return <ImageIcon className="h-4 w-4 text-primary" />
      case "document": return <FileText className="h-4 w-4 text-primary" />
      default: return <FileText className="h-4 w-4 text-primary" />
    }
  }
  
  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>Related Content</CardTitle>
        <CardDescription>
          Other content related to this item
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {relatedItems.length === 0 ? (
          <div className="rounded-md border border-dashed p-6 text-center text-sm text-muted-foreground">
            No related content found.
          </div>
        ) : (
          <div className="space-y-4">
            {relatedItems.map((item) => (
              <Link
                key={item.id}
                href={`/dashboard/content-hub/${item.id}`}
                className="block"
              >
                <div className="flex items-center gap-4 rounded-lg border p-3 transition-colors hover:bg-muted/50">
                  <div className="flex h-10 w-10 items-center justify-center rounded-md bg-primary/10">
                    {getTypeIcon(item.type)}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">{item.title}</p>
                    <p className="text-xs text-muted-foreground capitalize">
                      {item.type}
                    </p>
                  </div>
                  <ChevronRight className="h-4 w-4 text-muted-foreground" />
                </div>
              </Link>
            ))}
          </div>
        )}
        
        <Button variant="outline" className="w-full">
          View All Related
        </Button>
      </CardContent>
    </Card>
  )
} 