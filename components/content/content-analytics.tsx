"use client"

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/tabs"
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts"

interface ContentAnalyticsProps {
  contentId: string
}

export function ContentAnalytics({ contentId }: ContentAnalyticsProps) {
  // Dummy data
  const viewsData = [
    { date: "Mon", views: 45 },
    { date: "Tue", views: 52 },
    { date: "Wed", views: 49 },
    { date: "Thu", views: 63 },
    { date: "Fri", views: 58 },
    { date: "Sat", views: 78 },
    { date: "Sun", views: 71 },
  ]
  
  const engagementData = [
    { name: "Plays", value: 65 },
    { name: "Like<PERSON>", value: 15 },
    { name: "Share<PERSON>", value: 10 },
    { name: "Comments", value: 10 },
  ]
  
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042']
  
  const overviewStats = [
    { label: "Total Plays", value: "1,245" },
    { label: "Avg. Time", value: "2:15" },
    { label: "Completion Rate", value: "76%" },
    { label: "Engagement", value: "23%" },
  ]
  
  // Check if Recharts components are available
  const hasRecharts = typeof ResponsiveContainer !== 'undefined'
  
  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>Analytics</CardTitle>
        <CardDescription>
          Performance metrics for this content
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="views">Views</TabsTrigger>
            <TabsTrigger value="engagement">Engagement</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4 pt-4">
            <div className="grid grid-cols-2 gap-4">
              {overviewStats.map((stat, i) => (
                <div key={i} className="rounded-lg border p-3">
                  <div className="text-sm font-medium text-muted-foreground">
                    {stat.label}
                  </div>
                  <div className="text-2xl font-bold">{stat.value}</div>
                </div>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="views" className="space-y-4 pt-4">
            <div className="h-[200px] w-full">
              {hasRecharts ? (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={viewsData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis allowDecimals={false} />
                    <Tooltip />
                    <Bar dataKey="views" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex h-full items-center justify-center rounded-md border border-dashed p-4 text-center text-sm text-muted-foreground">
                  Chart visualization would appear here.
                </div>
              )}
            </div>
            <div className="text-xs text-muted-foreground">
              Views over the last 7 days
            </div>
          </TabsContent>
          
          <TabsContent value="engagement" className="space-y-4 pt-4">
            <div className="h-[200px] w-full">
              {hasRecharts ? (
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={engagementData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={80}
                      fill="#8884d8"
                      paddingAngle={5}
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {engagementData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex h-full items-center justify-center rounded-md border border-dashed p-4 text-center text-sm text-muted-foreground">
                  Chart visualization would appear here.
                </div>
              )}
            </div>
            <div className="text-xs text-muted-foreground">
              Engagement breakdown by type
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
} 