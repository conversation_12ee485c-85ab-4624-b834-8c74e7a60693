import Link from "next/link"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { 
  Download, 
  FileUp, 
  Folder, 
  Import, 
  Music, 
  Plus, 
  Video 
} from "lucide-react"

export function ContentActions() {
  return (
    <div className="flex items-center gap-2">
      <Link href="/dashboard/content-hub/new">
        <Button className="hidden sm:flex">
          <Plus className="mr-2 h-4 w-4" />
          New Content
        </Button>
      </Link>
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="sm:hidden">
            <Plus className="mr-2 h-4 w-4" />
            New
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem asChild>
            <Link href="/dashboard/content-hub/new" className="flex w-full cursor-pointer">
              <Plus className="mr-2 h-4 w-4" />
              New Content
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Music className="mr-2 h-4 w-4" />
            Upload Audio
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Video className="mr-2 h-4 w-4" />
            Upload Video
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Folder className="mr-2 h-4 w-4" />
            Create Collection
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline">
            <Import className="mr-2 h-4 w-4" />
            Import
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem>
            <FileUp className="mr-2 h-4 w-4" />
            Import from Files
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Download className="mr-2 h-4 w-4" />
            Import from URL
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <Music className="mr-2 h-4 w-4" />
            Import from Spotify
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Music className="mr-2 h-4 w-4" />
            Import from Apple Music
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Video className="mr-2 h-4 w-4" />
            Import from YouTube
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
} 