import Image from "next/image"
import Link from "next/link"
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardHeader 
} from "@/components/ui/card"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { MoreHorizontal, Music, Video, Image as ImageIcon, FileText } from "lucide-react"

export interface ContentItem {
  id: string
  title: string
  type: "audio" | "video" | "image" | "document"
  thumbnail?: string
  status: "draft" | "published" | "scheduled" | "archived"
}

interface ContentCardProps {
  content: ContentItem
}

export function ContentCard({ content }: ContentCardProps) {
  const { id, title, type, thumbnail, status } = content
  
  const getStatusColor = (status: string) => {
    switch(status) {
      case "published": return "success"
      case "draft": return "secondary"
      case "scheduled": return "warning"
      case "archived": return "destructive"
      default: return "secondary"
    }
  }
  
  const getTypeIcon = (type: string) => {
    switch(type) {
      case "audio": return <Music className="h-5 w-5" />
      case "video": return <Video className="h-5 w-5" />
      case "image": return <ImageIcon className="h-5 w-5" />
      case "document": return <FileText className="h-5 w-5" />
      default: return <FileText className="h-5 w-5" />
    }
  }
  
  return (
    <Card className="overflow-hidden transition-all hover:shadow-md">
      <Link href={`/dashboard/content-hub/${id}`}>
        <CardHeader className="p-0">
          <div className="relative aspect-video w-full">
            {thumbnail ? (
              <Image
                src={thumbnail}
                alt={title}
                fill
                className="object-cover"
              />
            ) : (
              <div className="flex h-full w-full items-center justify-center bg-muted">
                {getTypeIcon(type)}
              </div>
            )}
            <Badge 
              variant="outline"
              className={`absolute right-2 top-2 bg-${getStatusColor(status)}/10 text-${getStatusColor(status)}`}
            >
              {status}
            </Badge>
          </div>
        </CardHeader>
      </Link>
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div>
            <Badge variant="secondary" className="mb-2">
              {type}
            </Badge>
            <h3 className="font-medium">{title}</h3>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Options</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>Edit</DropdownMenuItem>
              <DropdownMenuItem>Duplicate</DropdownMenuItem>
              <DropdownMenuItem>Share</DropdownMenuItem>
              <DropdownMenuItem className="text-destructive">
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  )
} 