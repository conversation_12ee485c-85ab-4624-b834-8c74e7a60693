"use client"

import { useContent } from "@/hooks/use-content"
import { ContentCard, ContentItem } from "./content-card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"

export function ContentGrid() {
  const { content, isLoading, error } = useContent()
  
  if (isLoading) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <Skeleton key={i} className="h-[220px] rounded-lg" />
        ))}
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="rounded-md bg-destructive/15 p-4 text-center">
        <p className="text-destructive">Failed to load content. Please try again.</p>
      </div>
    )
  }
  
  // This is a placeholder until real data is available
  const contentItems = content || [
    { id: "1", title: "Summer Release", type: "audio", thumbnail: "/placeholder.jpg", status: "published" },
    { id: "2", title: "New Music Video", type: "video", thumbnail: "/placeholder.jpg", status: "draft" },
    { id: "3", title: "Album Artwork", type: "image", thumbnail: "/placeholder.jpg", status: "published" },
    { id: "4", title: "Tour Promo", type: "document", thumbnail: "/placeholder.jpg", status: "scheduled" },
    { id: "5", title: "Remix Package", type: "audio", thumbnail: "/placeholder.jpg", status: "published" },
    { id: "6", title: "Behind the Scenes", type: "video", thumbnail: "/placeholder.jpg", status: "published" },
  ]
  
  return (
    <div className="space-y-6">
      <Tabs defaultValue="all">
        <TabsList>
          <TabsTrigger value="all">All Content</TabsTrigger>
          <TabsTrigger value="audio">Audio</TabsTrigger>
          <TabsTrigger value="video">Video</TabsTrigger>
          <TabsTrigger value="image">Images</TabsTrigger>
          <TabsTrigger value="document">Documents</TabsTrigger>
        </TabsList>
        <TabsContent value="all" className="mt-6">
          <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {contentItems.map((item) => (
              <ContentCard key={item.id} content={item as ContentItem} />
            ))}
          </div>
        </TabsContent>
        <TabsContent value="audio" className="mt-6">
          <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {contentItems.filter(item => item.type === 'audio').map((item) => (
              <ContentCard key={item.id} content={item as ContentItem} />
            ))}
          </div>
        </TabsContent>
        <TabsContent value="video" className="mt-6">
          <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {contentItems.filter(item => item.type === 'video').map((item) => (
              <ContentCard key={item.id} content={item as ContentItem} />
            ))}
          </div>
        </TabsContent>
        <TabsContent value="image" className="mt-6">
          <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {contentItems.filter(item => item.type === 'image').map((item) => (
              <ContentCard key={item.id} content={item as ContentItem} />
            ))}
          </div>
        </TabsContent>
        <TabsContent value="document" className="mt-6">
          <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {contentItems.filter(item => item.type === 'document').map((item) => (
              <ContentCard key={item.id} content={item as ContentItem} />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
} 