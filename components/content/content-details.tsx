"use client"

import { useState } from "react"
import Image from "next/image"
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Copy, 
  Download, 
  Edit, 
  ExternalLink, 
  Music, 
  Play, 
  Share2, 
  Video as VideoIcon
} from "lucide-react"

interface ContentDetailsProps {
  content: {
    id: string
    title: string
    type: string
    status: string
    [key: string]: any
  }
}

export function ContentDetails({ content }: ContentDetailsProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  
  // Dummy metadata for display purposes
  const metadata = {
    title: content.title,
    artist: "Artist Name",
    album: "Album Title",
    releaseDate: "2023-09-15",
    duration: "3:45",
    genre: "Pop",
    tags: ["summer", "upbeat", "vocal"],
    description: "This is a sample description for the content item. It provides some context about the track or video and what it's about.",
    fileFormat: content.type === "audio" ? "MP3" : content.type === "video" ? "MP4" : "JPEG",
    fileSize: "14.2 MB",
    bitrate: content.type === "audio" ? "320 kbps" : content.type === "video" ? "1080p" : "N/A",
    license: "All Rights Reserved",
  }
  
  const renderContentPreview = () => {
    switch(content.type) {
      case "audio":
        return (
          <div className="flex h-[240px] flex-col items-center justify-center rounded-lg bg-muted p-6">
            <Music className="mb-4 h-16 w-16 text-primary" />
            <Button 
              size="lg" 
              className="gap-2"
              onClick={() => setIsPlaying(!isPlaying)}
            >
              {isPlaying ? "Pause" : "Play"}
              {isPlaying ? null : <Play className="h-4 w-4" />}
            </Button>
          </div>
        )
      case "video":
        return (
          <div className="relative aspect-video w-full overflow-hidden rounded-lg bg-muted">
            <div className="flex h-full items-center justify-center">
              <VideoIcon className="h-16 w-16 text-primary" />
              <Button 
                size="icon" 
                className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full"
                onClick={() => setIsPlaying(!isPlaying)}
              >
                <Play className="h-6 w-6" />
              </Button>
            </div>
          </div>
        )
      case "image":
        return (
          <div className="relative aspect-square w-full overflow-hidden rounded-lg bg-muted">
            <Image 
              src="/placeholder.jpg" 
              alt={content.title} 
              fill 
              className="object-cover" 
            />
          </div>
        )
      default:
        return (
          <div className="flex h-[240px] flex-col items-center justify-center rounded-lg bg-muted p-6">
            <div className="text-center">
              <p className="mb-2 text-lg font-medium">{content.title}</p>
              <p className="text-sm text-muted-foreground">{metadata.fileFormat} Document</p>
            </div>
          </div>
        )
    }
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Content Details</CardTitle>
        <CardDescription>
          View and manage this content item
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2">
          <div>
            {renderContentPreview()}
          </div>
          <div className="space-y-4">
            <div className="flex justify-between">
              <h3 className="text-lg font-medium">{metadata.title}</h3>
              <div className="flex gap-2">
                <Button variant="outline" size="icon">
                  <Edit className="h-4 w-4" />
                  <span className="sr-only">Edit</span>
                </Button>
                <Button variant="outline" size="icon">
                  <Share2 className="h-4 w-4" />
                  <span className="sr-only">Share</span>
                </Button>
                <Button variant="outline" size="icon">
                  <Download className="h-4 w-4" />
                  <span className="sr-only">Download</span>
                </Button>
              </div>
            </div>
            
            <Tabs defaultValue="info">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="info">Info</TabsTrigger>
                <TabsTrigger value="metadata">Metadata</TabsTrigger>
                <TabsTrigger value="activity">Activity</TabsTrigger>
              </TabsList>
              <TabsContent value="info" className="space-y-4 py-4">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">Artist</h4>
                  <p>{metadata.artist}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">Album</h4>
                  <p>{metadata.album}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">Release Date</h4>
                  <p>{metadata.releaseDate}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">Description</h4>
                  <p className="text-sm">{metadata.description}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">Tags</h4>
                  <div className="flex flex-wrap gap-1 pt-1">
                    {metadata.tags.map((tag, i) => (
                      <Badge key={i} variant="secondary">{tag}</Badge>
                    ))}
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="metadata" className="space-y-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Format</h4>
                    <p>{metadata.fileFormat}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Size</h4>
                    <p>{metadata.fileSize}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Duration</h4>
                    <p>{metadata.duration}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Bitrate</h4>
                    <p>{metadata.bitrate}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Genre</h4>
                    <p>{metadata.genre}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">License</h4>
                    <p>{metadata.license}</p>
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="activity" className="space-y-4 py-4">
                <div className="space-y-4">
                  <div className="flex items-start gap-2">
                    <div className="h-2 w-2 translate-y-1.5 rounded-full bg-blue-500" />
                    <div>
                      <p className="text-sm">
                        <span className="font-medium">You</span> uploaded this file
                      </p>
                      <p className="text-xs text-muted-foreground">
                        2 days ago
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="h-2 w-2 translate-y-1.5 rounded-full bg-blue-500" />
                    <div>
                      <p className="text-sm">
                        <span className="font-medium">John Doe</span> commented on this file
                      </p>
                      <p className="text-xs text-muted-foreground">
                        1 day ago
                      </p>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
            
            <div className="pt-2">
              <Button variant="outline" className="w-full gap-2">
                <ExternalLink className="h-4 w-4" />
                View Public URL
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 