"use client"

import { useState } from "react"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Search, SlidersHorizontal, X } from "lucide-react"

export function ContentFilters() {
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false)
  
  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-4 sm:flex-row">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search content..."
            className="pl-8"
          />
        </div>
        <div className="flex gap-2">
          <Select defaultValue="newest">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest first</SelectItem>
              <SelectItem value="oldest">Oldest first</SelectItem>
              <SelectItem value="a-z">Alphabetical (A-Z)</SelectItem>
              <SelectItem value="z-a">Alphabetical (Z-A)</SelectItem>
            </SelectContent>
          </Select>
          <Button 
            variant="outline" 
            size="icon"
            onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
            className={isAdvancedOpen ? "bg-muted" : ""}
          >
            {isAdvancedOpen ? (
              <X className="h-4 w-4" />
            ) : (
              <SlidersHorizontal className="h-4 w-4" />
            )}
            <span className="sr-only">
              {isAdvancedOpen ? "Close" : "Open"} advanced filters
            </span>
          </Button>
        </div>
      </div>
      
      {isAdvancedOpen && (
        <div className="grid gap-4 rounded-lg border p-4 sm:grid-cols-2 md:grid-cols-4">
          <div>
            <label className="mb-2 block text-sm font-medium">Status</label>
            <Select defaultValue="all">
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <label className="mb-2 block text-sm font-medium">Type</label>
            <Select defaultValue="all">
              <SelectTrigger>
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="audio">Audio</SelectItem>
                <SelectItem value="video">Video</SelectItem>
                <SelectItem value="image">Image</SelectItem>
                <SelectItem value="document">Document</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <label className="mb-2 block text-sm font-medium">Date from</label>
            <Input type="date" />
          </div>
          
          <div>
            <label className="mb-2 block text-sm font-medium">Date to</label>
            <Input type="date" />
          </div>
          
          <div className="flex items-end gap-2 sm:col-span-2 md:col-span-4">
            <Button className="flex-1 sm:flex-none">Apply Filters</Button>
            <Button variant="outline">Reset</Button>
          </div>
        </div>
      )}
    </div>
  )
} 