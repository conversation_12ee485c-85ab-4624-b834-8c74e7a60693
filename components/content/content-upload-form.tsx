"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import {
  <PERSON><PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Upload, Music, Video, Image as ImageIcon, FileText } from "lucide-react"

export function ContentUploadForm() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setSelectedFiles(Array.from(e.target.files))
    }
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false)
      router.push("/dashboard/content-hub")
    }, 1500)
  }
  
  return (
    <div>
      <Tabs defaultValue="upload">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="upload">Upload Files</TabsTrigger>
          <TabsTrigger value="create">Create New</TabsTrigger>
        </TabsList>
        
        <TabsContent value="upload" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Upload Content Files</CardTitle>
              <CardDescription>
                Drag and drop your files or click to browse
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div
                className="relative flex cursor-pointer flex-col items-center justify-center rounded-lg border border-dashed border-muted-foreground/25 p-12 text-center transition-colors hover:bg-muted/50"
                onClick={() => document.getElementById("file-upload")?.click()}
              >
                <Upload className="mb-4 h-8 w-8 text-muted-foreground" />
                <div className="space-y-1">
                  <p className="text-sm font-medium">
                    Drag and drop your files or click to browse
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Supports audio, video, images, and documents
                  </p>
                </div>
                <input
                  id="file-upload"
                  type="file"
                  multiple
                  className="sr-only"
                  onChange={handleFileChange}
                />
              </div>
              
              {selectedFiles.length > 0 && (
                <div className="mt-4">
                  <h4 className="mb-2 text-sm font-medium">Selected files:</h4>
                  <ul className="space-y-2">
                    {selectedFiles.map((file, index) => (
                      <li
                        key={index}
                        className="flex items-center gap-2 rounded-lg border p-2 text-sm"
                      >
                        {file.type.includes("audio") ? (
                          <Music className="h-4 w-4" />
                        ) : file.type.includes("video") ? (
                          <Video className="h-4 w-4" />
                        ) : file.type.includes("image") ? (
                          <ImageIcon className="h-4 w-4" />
                        ) : (
                          <FileText className="h-4 w-4" />
                        )}
                        <span>{file.name}</span>
                        <span className="ml-auto text-xs text-muted-foreground">
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={() => setSelectedFiles([])}>
                Clear
              </Button>
              <Button 
                disabled={selectedFiles.length === 0 || isSubmitting}
                onClick={handleSubmit}
              >
                {isSubmitting ? "Uploading..." : "Upload Files"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="create" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Create New Content</CardTitle>
              <CardDescription>
                Enter the details for your new content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form className="space-y-6" onSubmit={handleSubmit}>
                <div className="space-y-4">
                  <FormItem>
                    <FormLabel>Content Type</FormLabel>
                    <Select defaultValue="audio">
                      <SelectTrigger>
                        <SelectValue placeholder="Select content type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="audio">Audio Track</SelectItem>
                        <SelectItem value="video">Video</SelectItem>
                        <SelectItem value="image">Artwork/Image</SelectItem>
                        <SelectItem value="document">Document</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                  
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <Input placeholder="Enter title" />
                  </FormItem>
                  
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <Textarea placeholder="Enter description" />
                  </FormItem>
                  
                  <Separator />
                  
                  <FormItem>
                    <FormLabel>Release Date</FormLabel>
                    <Input type="date" />
                  </FormItem>
                  
                  <FormItem>
                    <FormLabel>Tags</FormLabel>
                    <Input placeholder="Enter tags separated by commas" />
                  </FormItem>
                  
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select defaultValue="draft">
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="published">Published</SelectItem>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="scheduled">Scheduled</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button variant="outline" type="button">
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? "Creating..." : "Create Content"}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 