"use client"

import * as React from "react"
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Line,
  LineChart,
  Pie,
  PieChart,
  Radar,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Scatter,
  ScatterChart,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
  ReferenceLine,
  ReferenceArea,
} from "recharts"

import { useIsMobile } from "@/hooks/use-mobile"
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ToggleGroup,
  ToggleGroupItem,
} from "@/components/ui/toggle-group"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import { Download, RefreshC<PERSON>, Filter } from "lucide-react"

interface EnhancedChartProps {
  data: any[]
  title: string
  description?: string
  type?: 'area' | 'bar' | 'line' | 'pie' | 'radar' | 'scatter'
  timeRange?: '7d' | '30d' | '90d' | '1y'
  onTimeRangeChange?: (range: string) => void
  onChartTypeChange?: (type: string) => void
  onRefresh?: () => void
  onExport?: () => void
  metrics?: {
    label: string
    value: number
    change: number
  }[]
  showMetrics?: boolean
  showFilters?: boolean
  customColors?: string[]
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8']

export function EnhancedChart({
  data,
  title,
  description,
  type = 'area',
  timeRange = '30d',
  onTimeRangeChange,
  onChartTypeChange,
  onRefresh,
  onExport,
  metrics,
  showMetrics = true,
  showFilters = true,
  customColors,
}: EnhancedChartProps) {
  const isMobile = useIsMobile()
  const [dateRange, setDateRange] = React.useState<{ from: Date; to: Date }>({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    to: new Date(),
  })
  const [filteredData, setFilteredData] = React.useState(data)

  React.useEffect(() => {
    if (isMobile) {
      onTimeRangeChange?.('7d')
    }
  }, [isMobile, onTimeRangeChange])

  const renderChart = () => {
    switch (type) {
      case "area":
        return (
          <AreaChart data={filteredData}>
            <defs>
              {Object.keys(data[0] || {})
                .filter(key => key !== 'date')
                .map((key, index) => (
                  <linearGradient
                    key={key}
                    id={`fill${key}`}
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop
                      offset="5%"
                      stopColor={customColors?.[index] || COLORS[index % COLORS.length]}
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="95%"
                      stopColor={customColors?.[index] || COLORS[index % COLORS.length]}
                      stopOpacity={0}
                    />
                  </linearGradient>
                ))}
            </defs>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="date"
              tickFormatter={(value) => new Date(value).toLocaleDateString()}
            />
            <YAxis />
            <Tooltip
              labelFormatter={(value) => new Date(value).toLocaleDateString()}
            />
            <Legend />
            {Object.keys(data[0] || {})
              .filter(key => key !== 'date')
              .map((key, index) => (
                <Area
                  key={key}
                  type="monotone"
                  dataKey={key}
                  stroke={customColors?.[index] || COLORS[index % COLORS.length]}
                  fillOpacity={1}
                  fill={`url(#fill${key})`}
                />
              ))}
          </AreaChart>
        )

      case "bar":
        return (
          <BarChart data={filteredData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="date"
              tickFormatter={(value) => new Date(value).toLocaleDateString()}
            />
            <YAxis />
            <Tooltip
              labelFormatter={(value) => new Date(value).toLocaleDateString()}
            />
            <Legend />
            {Object.keys(data[0] || {})
              .filter(key => key !== 'date')
              .map((key, index) => (
                <Bar
                  key={key}
                  dataKey={key}
                  fill={customColors?.[index] || COLORS[index % COLORS.length]}
                />
              ))}
          </BarChart>
        )

      case "line":
        return (
          <LineChart data={filteredData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="date"
              tickFormatter={(value) => new Date(value).toLocaleDateString()}
            />
            <YAxis />
            <Tooltip
              labelFormatter={(value) => new Date(value).toLocaleDateString()}
            />
            <Legend />
            {Object.keys(data[0] || {})
              .filter(key => key !== 'date')
              .map((key, index) => (
                <Line
                  key={key}
                  type="monotone"
                  dataKey={key}
                  stroke={customColors?.[index] || COLORS[index % COLORS.length]}
                />
              ))}
          </LineChart>
        )

      case "pie":
        return (
          <PieChart>
            <Pie
              data={filteredData}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {filteredData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={customColors?.[index] || COLORS[index % COLORS.length]}
                />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        )

      case "radar":
        return (
          <RadarChart outerRadius={90} width={500} height={250} data={filteredData}>
            <PolarGrid />
            <PolarAngleAxis dataKey="metric" />
            <PolarRadiusAxis />
            <Radar
              name="Performance"
              dataKey="value"
              stroke={customColors?.[0] || COLORS[0]}
              fill={customColors?.[0] || COLORS[0]}
              fillOpacity={0.6}
            />
            <Tooltip />
            <Legend />
          </RadarChart>
        )

      case "scatter":
        return (
          <ScatterChart>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" dataKey="x" name="X" />
            <YAxis type="number" dataKey="y" name="Y" />
            <Tooltip cursor={{ strokeDasharray: '3 3' }} />
            <Legend />
            <Scatter
              name="Data Points"
              data={filteredData}
              fill={customColors?.[0] || COLORS[0]}
            />
          </ScatterChart>
        )

      default:
        return null
    }
  }

  return (
    <Card className="@container/card">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
        <CardAction className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          {showFilters && (
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
              <ToggleGroup
                type="single"
                value={timeRange}
                onValueChange={onTimeRangeChange}
                variant="outline"
                className="hidden *:data-[slot=toggle-group-item]:!px-4 @[767px]/card:flex"
              >
                <ToggleGroupItem value="7d">Last 7 days</ToggleGroupItem>
                <ToggleGroupItem value="30d">Last 30 days</ToggleGroupItem>
                <ToggleGroupItem value="90d">Last 90 days</ToggleGroupItem>
                <ToggleGroupItem value="1y">Last year</ToggleGroupItem>
              </ToggleGroup>
              <Select value={timeRange} onValueChange={onTimeRangeChange}>
                <SelectTrigger
                  className="flex w-40 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate @[767px]/card:hidden"
                  size="sm"
                >
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
              <Select value={type} onValueChange={onChartTypeChange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select chart type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="area">Area Chart</SelectItem>
                  <SelectItem value="bar">Bar Chart</SelectItem>
                  <SelectItem value="line">Line Chart</SelectItem>
                  <SelectItem value="pie">Pie Chart</SelectItem>
                  <SelectItem value="radar">Radar Chart</SelectItem>
                  <SelectItem value="scatter">Scatter Plot</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
          <div className="flex items-center gap-2">
            {onRefresh && (
              <Button
                variant="outline"
                size="icon"
                onClick={onRefresh}
                className="h-8 w-8"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            )}
            {onExport && (
              <Button
                variant="outline"
                size="icon"
                onClick={onExport}
                className="h-8 w-8"
              >
                <Download className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardAction>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        {showMetrics && metrics && (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-4">
            {metrics.map((metric, index) => (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {metric.label}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metric.value}</div>
                  <p className={`text-xs ${metric.change >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {metric.change >= 0 ? '+' : ''}{metric.change}% from last period
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
        <div className="aspect-auto h-[400px] w-full">
          {renderChart()}
        </div>
      </CardContent>
    </Card>
  )
} 