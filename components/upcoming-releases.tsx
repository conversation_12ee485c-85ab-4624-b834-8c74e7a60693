import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export function UpcomingReleases() {
  const releases = [
    {
      title: "Ethereal Journeys",
      type: "Album",
      date: "2023-11-15",
      optimized: true,
      tracks: 12,
      readiness: 85,
    },
    {
      title: "Urban Echoes",
      type: "Single",
      date: "2023-10-22",
      optimized: true,
      tracks: 1,
      readiness: 100,
    },
    {
      title: "Nostalgia Remix ft. DJ Zeta",
      type: "Remix",
      date: "2023-12-05",
      optimized: false,
      tracks: 1,
      readiness: 60,
    },
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    }).format(date);
  };

  return (
    <Card className="border-none shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">Upcoming Releases</CardTitle>
          <button className="text-sm text-primary font-medium">Optimize All</button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {releases.map((release, index) => (
            <div key={index} className="flex items-center justify-between p-3 border rounded-md">
              <div className="flex flex-col">
                <div className="flex items-center gap-2">
                  <h3 className="font-medium">{release.title}</h3>
                  {release.optimized && (
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                      AI Optimized
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>{release.type}</span>
                  <span>•</span>
                  <span>{release.tracks} track{release.tracks > 1 ? 's' : ''}</span>
                </div>
              </div>
              <div className="flex flex-col items-end gap-1">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Ready: {release.readiness}%</span>
                  <div className="w-16 h-2 bg-muted rounded-full">
                    <div 
                      className={`h-full rounded-full ${release.readiness > 80 ? 'bg-green-500' : release.readiness > 50 ? 'bg-amber-500' : 'bg-red-500'}`}
                      style={{ width: `${release.readiness}%` }}
                    />
                  </div>
                </div>
                <span className="text-sm">{formatDate(release.date)}</span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
} 