"use client"

import { useSession, signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { UserAvatar } from "@/components/user-avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { 
  IconUserCircle, 
  IconCreditCard, 
  IconBell, 
  IconSettings, 
  IconLogout,
  IconChevronDown
} from "@tabler/icons-react"

interface UserMenuProps {
  variant?: "sidebar" | "header"
  showName?: boolean
  className?: string
}

export function UserMenu({ variant = "header", showName = true, className }: UserMenuProps) {
  const { data: session, status } = useSession()
  const router = useRouter()

  if (status === "loading") {
    return (
      <div className="flex items-center gap-2">
        <div className="h-8 w-8 rounded-full bg-muted animate-pulse" />
        {showName && <div className="h-4 w-24 bg-muted rounded animate-pulse" />}
      </div>
    )
  }

  if (!session?.user) {
    return null
  }

  const user = session.user

  const handleSignOut = async () => {
    await signOut({ 
      callbackUrl: '/login',
      redirect: true 
    })
  }

  const handleAccountClick = () => {
    router.push('/dashboard/settings')
  }

  const handleBillingClick = () => {
    router.push('/dashboard/billing')
  }

  const handleNotificationsClick = () => {
    router.push('/dashboard/notifications')
  }

  const handleSettingsClick = () => {
    router.push('/dashboard/settings')
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          className={`flex items-center gap-2 ${className}`}
          size={variant === "sidebar" ? "lg" : "sm"}
        >
          <UserAvatar user={user} size="sm" />
          {showName && (
            <span className="hidden sm:inline-block truncate">
              {user.name || user.email}
            </span>
          )}
          <IconChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent 
        className="w-56" 
        align="end" 
        forceMount
      >
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">
              {user.name || 'User'}
            </p>
            <p className="text-xs leading-none text-muted-foreground">
              {user.email}
            </p>
          </div>
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuGroup>
          <DropdownMenuItem onClick={handleAccountClick} className="cursor-pointer">
            <IconUserCircle className="mr-2 h-4 w-4" />
            <span>Profile</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={handleBillingClick} className="cursor-pointer">
            <IconCreditCard className="mr-2 h-4 w-4" />
            <span>Billing</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={handleNotificationsClick} className="cursor-pointer">
            <IconBell className="mr-2 h-4 w-4" />
            <span>Notifications</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={handleSettingsClick} className="cursor-pointer">
            <IconSettings className="mr-2 h-4 w-4" />
            <span>Settings</span>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={handleSignOut} 
          className="cursor-pointer text-red-600 focus:text-red-600"
        >
          <IconLogout className="mr-2 h-4 w-4" />
          <span>Sign out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}