"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { type Icon } from "@tabler/icons-react"

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { useUIStore } from "@/lib/stores/ui-store"
import { cn } from "@/lib/utils"

export function NavSecondary({
  items,
  ...props
}: {
  items: {
    title: string
    url: string
    icon: Icon
  }[]
} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {
  const router = useRouter()
  const { activeItem, setActiveItem } = useUIStore()

  const handleNavigation = (url: string) => {
    setActiveItem(url)
    router.push(url)
  }

  return (
    <SidebarGroup {...props}>
      <SidebarGroupContent>
        <SidebarMenu>
          {items.map((item) => (
            <SidebarMenuItem 
              key={item.title}
              className={cn(activeItem === item.url && "bg-primary/10")}
            >
              <SidebarMenuButton 
                tooltip={item.title}
                onClick={() => handleNavigation(item.url)}
                isActive={activeItem === item.url}
              >
                <item.icon />
                <span>{item.title}</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
