"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { useTheme } from "next-themes";
import { useRouter } from "next/navigation";

interface TopLoaderConfig {
  color: string;
  height: number;
  showSpinner: boolean;
  easing: string;
  speed: number;
  shadow: string;
  zIndex: number;
  showAtBottom: boolean;
  template: string;
  initialPosition: number;
  crawl: boolean;
  crawlSpeed: number;
}

interface TopLoaderContextType {
  config: TopLoaderConfig;
  isLoading: boolean;
  progress: number;
  updateConfig: (newConfig: Partial<TopLoaderConfig>) => void;
  resetConfig: () => void;
  setLoading: (loading: boolean) => void;
  setProgress: (progress: number) => void;
  getThemeConfig: () => Partial<TopLoaderConfig>;
}

const TopLoaderContext = createContext<TopLoaderContextType | undefined>(undefined);

export const useTopLoaderContext = () => {
  const context = useContext(TopLoaderContext);
  if (!context) {
    throw new Error("useTopLoaderContext must be used within a TopLoaderProvider");
  }
  return context;
};

interface TopLoaderProviderProps {
  children: React.ReactNode;
  defaultConfig?: Partial<TopLoaderConfig>;
}

export const TopLoaderProvider = ({ 
  children, 
  defaultConfig = {} 
}: TopLoaderProviderProps) => {
  const { theme, resolvedTheme } = useTheme();
  const router = useRouter();
  
  const [config, setConfig] = useState<TopLoaderConfig>({
    color: "oklch(0.208 0.042 265.755)",
    height: 3,
    showSpinner: false,
    easing: "ease",
    speed: 200,
    shadow: "0 0 10px var(--color-primary), 0 0 5px var(--color-primary)",
    zIndex: 1600,
    showAtBottom: false,
    template: '<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>',
    initialPosition: 0.08,
    crawl: true,
    crawlSpeed: 200,
    ...defaultConfig,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgressState] = useState(0);

  // Update configuration based on theme changes
  useEffect(() => {
    const currentTheme = resolvedTheme || theme;
    
    const themeColors = {
      dark: "oklch(0.488 0.243 264.376)",
      light: "oklch(0.208 0.042 265.755)",
    };

    const color = themeColors[currentTheme as keyof typeof themeColors] || themeColors.light;
    
    setConfig(prev => ({
      ...prev,
      color,
      shadow: `0 0 10px ${color}, 0 0 5px ${color}`,
    }));
  }, [theme, resolvedTheme]);

  const updateConfig = (newConfig: Partial<TopLoaderConfig>) => {
    setConfig(prev => ({
      ...prev,
      ...newConfig,
    }));
  };

  const resetConfig = () => {
    setConfig({
      color: "oklch(0.208 0.042 265.755)",
      height: 3,
      showSpinner: false,
      easing: "ease",
      speed: 200,
      shadow: "0 0 10px var(--color-primary), 0 0 5px var(--color-primary)",
      zIndex: 1600,
      showAtBottom: false,
      template: '<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>',
      initialPosition: 0.08,
      crawl: true,
      crawlSpeed: 200,
      ...defaultConfig,
    });
  };

  const setLoading = (loading: boolean) => {
    setIsLoading(loading);
  };

  const setProgress = (progress: number) => {
    setProgressState(Math.min(100, Math.max(0, progress)));
  };

  const getThemeConfig = () => {
    const currentTheme = resolvedTheme || theme;
    
    const configs = {
      dark: {
        color: "oklch(0.488 0.243 264.376)",
        shadow: "0 0 10px oklch(0.488 0.243 264.376), 0 0 5px oklch(0.488 0.243 264.376)",
      },
      light: {
        color: "oklch(0.208 0.042 265.755)",
        shadow: "0 0 10px oklch(0.208 0.042 265.755), 0 0 5px oklch(0.208 0.042 265.755)",
      },
    };

    return configs[currentTheme as keyof typeof configs] || configs.light;
  };

  const contextValue: TopLoaderContextType = {
    config,
    isLoading,
    progress,
    updateConfig,
    resetConfig,
    setLoading,
    setProgress,
    getThemeConfig,
  };

  return (
    <TopLoaderContext.Provider value={contextValue}>
      {children}
    </TopLoaderContext.Provider>
  );
};