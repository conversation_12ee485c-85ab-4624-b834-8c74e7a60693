"use client"

import { createContext, useContext, useEffect, useState } from "react"
import { useSession } from "next-auth/react"

interface UserContextType {
  user: {
    id?: string
    name?: string | null
    email?: string | null
    image?: string | null
  } | null
  isLoading: boolean
  isAuthenticated: boolean
  preferences: {
    theme: 'light' | 'dark' | 'system'
    notifications: {
      email: boolean
      push: boolean
      marketing: boolean
    }
    privacy: {
      publicProfile: boolean
      showOnlineStatus: boolean
      analyticsSharing: boolean
    }
  }
  updatePreferences: (preferences: Partial<UserContextType['preferences']>) => void
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export function UserProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession()
  const [preferences, setPreferences] = useState<UserContextType['preferences']>({
    theme: 'system',
    notifications: {
      email: true,
      push: true,
      marketing: false
    },
    privacy: {
      publicProfile: false,
      showOnlineStatus: false,
      analyticsSharing: true
    }
  })

  const isLoading = status === "loading"
  const isAuthenticated = !!session?.user
  const user = session?.user || null

  // Load user preferences from localStorage or API
  useEffect(() => {
    if (isAuthenticated && user?.email) {
      const savedPreferences = localStorage.getItem(`user-preferences-${user.email}`)
      if (savedPreferences) {
        try {
          const parsed = JSON.parse(savedPreferences)
          setPreferences(prev => ({ ...prev, ...parsed }))
        } catch (error) {
          console.error('Failed to parse user preferences:', error)
        }
      }
    }
  }, [isAuthenticated, user?.email])

  const updatePreferences = (newPreferences: Partial<UserContextType['preferences']>) => {
    setPreferences(prev => {
      const updated = { ...prev, ...newPreferences }
      
      // Save to localStorage
      if (user?.email) {
        localStorage.setItem(`user-preferences-${user.email}`, JSON.stringify(updated))
      }
      
      return updated
    })
  }

  const value: UserContextType = {
    user,
    isLoading,
    isAuthenticated,
    preferences,
    updatePreferences
  }

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}