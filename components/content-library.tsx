import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

// Define types for our content items
type ContentType = "audio" | "video" | "image";

interface ContentItem {
  id: number;
  title: string;
  type: ContentType;
  format: string;
  duration?: string;
  dimensions?: string;
  lastModified: string;
  tags: string[];
  aiTags: string[];
  thumbnail: string;
}

export function ContentLibrary() {
  const [activeFilter, setActiveFilter] = useState<"all" | ContentType>("all");
  
  // Sample content library data
  const contentItems: ContentItem[] = [
    {
      id: 1,
      title: "Summer Vibes - Main Track",
      type: "audio",
      format: "wav",
      duration: "3:45",
      lastModified: "2023-08-15",
      tags: ["summer", "electronic", "commercial"],
      aiTags: ["upbeat", "positive", "trending"],
      thumbnail: "https://placehold.co/300x300/4F46E5/FFFFFF?text=SV"
    },
    {
      id: 2,
      title: "Album Cover Design - Final",
      type: "image",
      format: "png",
      dimensions: "3000x3000",
      lastModified: "2023-08-10",
      tags: ["cover", "artwork", "album"],
      aiTags: ["vibrant", "professional", "attention-grabbing"],
      thumbnail: "https://placehold.co/300x300/EF4444/FFFFFF?text=AC"
    },
    {
      id: 3,
      title: "Studio Session Highlights",
      type: "video",
      format: "mp4",
      duration: "15:20",
      lastModified: "2023-07-28",
      tags: ["studio", "behind-scenes", "recording"],
      aiTags: ["authentic", "engaging", "personal"],
      thumbnail: "https://placehold.co/300x300/10B981/FFFFFF?text=SS"
    },
    {
      id: 4,
      title: "Acoustic Version - Draft 2",
      type: "audio",
      format: "mp3",
      duration: "4:12",
      lastModified: "2023-08-02",
      tags: ["acoustic", "draft", "remix"],
      aiTags: ["emotional", "calm", "intimate"],
      thumbnail: "https://placehold.co/300x300/F59E0B/FFFFFF?text=AV"
    },
    {
      id: 5,
      title: "Tour Promotion Video",
      type: "video",
      format: "mp4",
      duration: "1:30",
      lastModified: "2023-08-18",
      tags: ["promo", "tour", "advertising"],
      aiTags: ["energetic", "shareable", "impactful"],
      thumbnail: "https://placehold.co/300x300/8B5CF6/FFFFFF?text=TP"
    },
    {
      id: 6,
      title: "Social Media Banner",
      type: "image",
      format: "jpg",
      dimensions: "1920x1080",
      lastModified: "2023-08-05",
      tags: ["social", "banner", "promotional"],
      aiTags: ["eye-catching", "branded", "memorable"],
      thumbnail: "https://placehold.co/300x300/EC4899/FFFFFF?text=SMB"
    }
  ];
  
  const filteredContent = activeFilter === "all" 
    ? contentItems 
    : contentItems.filter(item => item.type === activeFilter);
    
  const typeIcons: Record<ContentType, string> = {
    audio: "🎵",
    video: "🎬",
    image: "🖼️"
  };
  
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    }).format(date);
  };

  return (
    <div className="container mx-auto px-4">
      <div className="flex flex-col gap-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">Content Library</h1>
            <p className="text-muted-foreground">Manage your creative assets</p>
          </div>
          <Button>Upload New</Button>
        </div>
        
        <Card>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle>Assets</CardTitle>
              <div className="flex gap-2">
                <Button 
                  variant={activeFilter === "all" ? "default" : "outline"} 
                  size="sm"
                  onClick={() => setActiveFilter("all")}
                >
                  All
                </Button>
                <Button 
                  variant={activeFilter === "audio" ? "default" : "outline"} 
                  size="sm"
                  onClick={() => setActiveFilter("audio")}
                >
                  Audio
                </Button>
                <Button 
                  variant={activeFilter === "video" ? "default" : "outline"} 
                  size="sm"
                  onClick={() => setActiveFilter("video")}
                >
                  Video
                </Button>
                <Button 
                  variant={activeFilter === "image" ? "default" : "outline"} 
                  size="sm"
                  onClick={() => setActiveFilter("image")}
                >
                  Images
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredContent.map((item) => (
                <Card key={item.id} className="overflow-hidden border">
                  <div className="aspect-square bg-secondary relative">
                    <img 
                      src={item.thumbnail} 
                      alt={item.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-2 right-2">
                      <Badge className="bg-black/70 text-white border-none">
                        {typeIcons[item.type]} {item.type}
                      </Badge>
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <div className="flex flex-col gap-2">
                      <h3 className="font-semibold truncate">{item.title}</h3>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <span className="flex-1">
                          {item.type === 'audio' || item.type === 'video' 
                            ? `${item.format} • ${item.duration}` 
                            : `${item.format} • ${item.dimensions}`}
                        </span>
                        <span>{formatDate(item.lastModified)}</span>
                      </div>
                      <div className="mt-2">
                        <p className="text-xs font-medium mb-1">AI-Generated Tags:</p>
                        <div className="flex flex-wrap gap-1">
                          {item.aiTags.map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 