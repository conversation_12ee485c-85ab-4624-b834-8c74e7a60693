"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useTopLoaderStore } from "@/lib/stores/top-loader-store";
import { 
  IconSettings, 
  IconPalette, 
  IconChart, 
  IconDeviceDesktop,
  IconReload,
  IconCheck,
  IconActivity
} from "@tabler/icons-react";

export function TopLoaderSettings() {
  const {
    config,
    preferences,
    analytics,
    updateConfig,
    updatePreferences,
    resetConfig,
    updateThemeConfig,
    getThemeConfig,
  } = useTopLoaderStore();

  const [activeTab, setActiveTab] = useState("general");

  const handleConfigChange = (key: string, value: any) => {
    updateConfig({ [key]: value });
  };

  const handlePreferencesChange = (key: string, value: any) => {
    updatePreferences({ [key]: value });
  };

  const handleThemeConfigChange = (theme: 'dark' | 'light', key: string, value: any) => {
    updateThemeConfig(theme, { [key]: value });
  };

  const easingOptions = [
    { value: "ease", label: "Ease" },
    { value: "ease-in", label: "Ease In" },
    { value: "ease-out", label: "Ease Out" },
    { value: "ease-in-out", label: "Ease In-Out" },
    { value: "linear", label: "Linear" },
  ];

  const positionOptions = [
    { value: "top", label: "Top" },
    { value: "bottom", label: "Bottom" },
  ];

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">TopLoader Configuration</h2>
          <p className="text-muted-foreground">Customize your loading experience</p>
        </div>
        <Button 
          onClick={resetConfig}
          variant="outline"
          size="sm"
          className="gap-2"
        >
          <IconReload className="h-4 w-4" />
          Reset to Default
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general" className="gap-2">
            <IconSettings className="h-4 w-4" />
            General
          </TabsTrigger>
          <TabsTrigger value="appearance" className="gap-2">
            <IconPalette className="h-4 w-4" />
            Appearance
          </TabsTrigger>
          <TabsTrigger value="performance" className="gap-2">
            <IconDeviceDesktop className="h-4 w-4" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="analytics" className="gap-2">
            <IconChart className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Basic Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="height">Height: {config.height}px</Label>
                <Slider
                  id="height"
                  min={1}
                  max={10}
                  step={1}
                  value={[config.height]}
                  onValueChange={(value) => handleConfigChange("height", value[0])}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="speed">Speed: {config.speed}ms</Label>
                <Slider
                  id="speed"
                  min={50}
                  max={1000}
                  step={50}
                  value={[config.speed]}
                  onValueChange={(value) => handleConfigChange("speed", value[0])}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="initialPosition">Initial Position: {config.initialPosition}</Label>
                <Slider
                  id="initialPosition"
                  min={0}
                  max={1}
                  step={0.01}
                  value={[config.initialPosition]}
                  onValueChange={(value) => handleConfigChange("initialPosition", value[0])}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="easing">Easing</Label>
                <Select 
                  value={config.easing} 
                  onValueChange={(value) => handleConfigChange("easing", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {easingOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="showSpinner"
                  checked={config.showSpinner}
                  onCheckedChange={(checked) => handleConfigChange("showSpinner", checked)}
                />
                <Label htmlFor="showSpinner">Show Spinner</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="showAtBottom"
                  checked={config.showAtBottom}
                  onCheckedChange={(checked) => handleConfigChange("showAtBottom", checked)}
                />
                <Label htmlFor="showAtBottom">Show at Bottom</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="appearance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Visual Customization</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="color">Color</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="color"
                    type="color"
                    value={config.color}
                    onChange={(e) => handleConfigChange("color", e.target.value)}
                    className="w-12 h-12 p-1"
                  />
                  <Input
                    value={config.color}
                    onChange={(e) => handleConfigChange("color", e.target.value)}
                    placeholder="Enter color value"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="shadow">Shadow</Label>
                <Input
                  id="shadow"
                  value={config.shadow}
                  onChange={(e) => handleConfigChange("shadow", e.target.value)}
                  placeholder="CSS shadow value"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="zIndex">Z-Index: {config.zIndex}</Label>
                <Slider
                  id="zIndex"
                  min={1000}
                  max={9999}
                  step={1}
                  value={[config.zIndex]}
                  onValueChange={(value) => handleConfigChange("zIndex", value[0])}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Light Theme</Label>
                  <div className="p-3 border rounded-lg space-y-2">
                    <Input
                      placeholder="Light theme color"
                      value={getThemeConfig('light').color || ''}
                      onChange={(e) => handleThemeConfigChange('light', 'color', e.target.value)}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Dark Theme</Label>
                  <div className="p-3 border rounded-lg space-y-2">
                    <Input
                      placeholder="Dark theme color"
                      value={getThemeConfig('dark').color || ''}
                      onChange={(e) => handleThemeConfigChange('dark', 'color', e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Performance Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center space-x-2">
                <Switch
                  id="enableAnimations"
                  checked={preferences.enableAnimations}
                  onCheckedChange={(checked) => handlePreferencesChange("enableAnimations", checked)}
                />
                <Label htmlFor="enableAnimations">Enable Animations</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="showProgressText"
                  checked={preferences.showProgressText}
                  onCheckedChange={(checked) => handlePreferencesChange("showProgressText", checked)}
                />
                <Label htmlFor="showProgressText">Show Progress Text</Label>
              </div>

              <div className="space-y-2">
                <Label htmlFor="autoHideDelay">Auto Hide Delay: {preferences.autoHideDelay}ms</Label>
                <Slider
                  id="autoHideDelay"
                  min={0}
                  max={1000}
                  step={50}
                  value={[preferences.autoHideDelay]}
                  onValueChange={(value) => handlePreferencesChange("autoHideDelay", value[0])}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="crawlSpeed">Crawl Speed: {config.crawlSpeed}ms</Label>
                <Slider
                  id="crawlSpeed"
                  min={50}
                  max={1000}
                  step={50}
                  value={[config.crawlSpeed]}
                  onValueChange={(value) => handleConfigChange("crawlSpeed", value[0])}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <IconActivity className="h-5 w-5" />
                Loading Analytics
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Total Loads</Label>
                  <div className="text-2xl font-bold">{analytics.totalLoads}</div>
                </div>
                <div className="space-y-2">
                  <Label>Average Load Time</Label>
                  <div className="text-2xl font-bold">
                    {analytics.averageLoadTime.toFixed(0)}ms
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Recent Loads</Label>
                  <div className="text-2xl font-bold">{analytics.loadingTimes.length}</div>
                </div>
              </div>

              {analytics.loadingTimes.length > 0 && (
                <div className="space-y-2">
                  <Label>Recent Loading Times</Label>
                  <div className="space-y-1">
                    {analytics.loadingTimes.slice(-5).map((time, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm">Load {analytics.loadingTimes.length - 4 + index}</span>
                        <Badge variant="outline">{time}ms</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Label>Performance Score</Label>
                <div className="space-y-1">
                  <Progress 
                    value={Math.min(100, Math.max(0, 100 - (analytics.averageLoadTime / 10)))} 
                    className="w-full"
                  />
                  <div className="text-sm text-muted-foreground">
                    {analytics.averageLoadTime < 500 ? "Excellent" : 
                     analytics.averageLoadTime < 1000 ? "Good" : 
                     analytics.averageLoadTime < 2000 ? "Average" : "Needs Improvement"}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}