"use client";

import { useTheme } from "next-themes";
import { useEffect, useState, useCallback } from "react";
import NextTopLoader from "nextjs-toploader";
import { useTopLoaderStore } from "@/lib/stores/top-loader-store";

interface TopLoaderProps {
  height?: number;
  showSpinner?: boolean;
  easing?: string;
  speed?: number;
  shadow?: string;
  template?: string;
  zIndex?: number;
  showAtBottom?: boolean;
  className?: string;
  onStart?: () => void;
  onComplete?: () => void;
  customTemplate?: boolean;
}

const TopLoader = ({
  height,
  showSpinner,
  easing,
  speed,
  shadow,
  template,
  zIndex,
  showAtBottom,
  className = "",
  onStart,
  onComplete,
  customTemplate = false,
  ...props
}: TopLoaderProps) => {
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [startTime, setStartTime] = useState<number | null>(null);

  const {
    config,
    preferences,
    applyTheme,
    recordLoadTime,
    setLoading,
    setProgress,
  } = useTopLoaderStore();

  useEffect(() => {
    setMounted(true);
  }, []);

  // Apply theme-based configuration
  useEffect(() => {
    if (!mounted) return;

    const currentTheme = resolvedTheme || theme;
    if (currentTheme === 'dark' || currentTheme === 'light') {
      applyTheme(currentTheme);
    }
  }, [theme, resolvedTheme, mounted, applyTheme]);

  // Handle loading start
  const handleStart = useCallback(() => {
    setStartTime(Date.now());
    setLoading(true);
    onStart?.();
  }, [onStart, setLoading]);

  // Handle loading complete
  const handleComplete = useCallback(() => {
    if (startTime) {
      const loadTime = Date.now() - startTime;
      recordLoadTime(loadTime);
    }
    setLoading(false);
    setProgress(100);
    onComplete?.();
  }, [startTime, onComplete, recordLoadTime, setLoading, setProgress]);

  if (!mounted) {
    return null;
  }

  // Enhanced template with progress text
  const getTemplate = () => {
    if (template) return template;
    if (customTemplate) {
      return `
        <div class="bar" role="bar">
          <div class="peg"></div>
        </div>
        ${showSpinner ? '<div class="spinner" role="spinner"><div class="spinner-icon"></div></div>' : ''}
        ${preferences.showProgressText ? '<div class="progress-text" role="status"></div>' : ''}
      `;
    }
    return '<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>';
  };

  // Get theme-based color
  const getThemeColor = () => {
    const currentTheme = resolvedTheme || theme;
    
    switch (currentTheme) {
      case "dark":
        return "oklch(0.488 0.243 264.376)"; // Dark theme primary
      case "light":
        return "oklch(0.208 0.042 265.755)"; // Light theme primary
      default:
        return "oklch(0.208 0.042 265.755)"; // Default primary
    }
  };

  const themeColor = getThemeColor();

  // Merge props with store configuration and theme
  const finalConfig = {
    color: themeColor,
    initialPosition: config.initialPosition,
    crawlSpeed: config.crawlSpeed,
    height: height ?? config.height,
    crawl: config.crawl,
    showSpinner: showSpinner ?? config.showSpinner,
    easing: easing ?? config.easing,
    speed: speed ?? config.speed,
    shadow: shadow ?? `0 0 10px ${themeColor}, 0 0 5px ${themeColor}`,
    template: getTemplate(),
    zIndex: zIndex ?? config.zIndex,
    showAtBottom: showAtBottom ?? config.showAtBottom,
    ...props,
  };

  return (
    <>
      <NextTopLoader {...finalConfig} />
      {preferences.enableAnimations && (
        <style jsx global>{`
          #nprogress .bar {
            transition: all 0.3s ease;
          }
          
          #nprogress .peg {
            animation: pulse 2s ease-in-out infinite;
          }
          
          @keyframes pulse {
            0% {
              opacity: 1;
              transform: scale(1);
            }
            50% {
              opacity: 0.7;
              transform: scale(1.05);
            }
            100% {
              opacity: 1;
              transform: scale(1);
            }
          }
          
          .progress-text {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: var(--color-foreground);
            font-size: 0.875rem;
            font-weight: 500;
            z-index: ${finalConfig.zIndex + 1};
            pointer-events: none;
          }
        `}</style>
      )}
    </>
  );
};

export default TopLoader;