"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { useTopLoaderStore } from "@/lib/stores/top-loader-store";
import { useRouter } from "next/navigation";
import { 
  IconPlayerPlay, 
  IconPlayerStop, 
  IconRefresh,
  IconSettings,
  IconChartBar,
  IconPalette
} from "@tabler/icons-react";

export function TopLoaderDemo() {
  const router = useRouter();
  const [isSimulating, setIsSimulating] = useState(false);
  const [simulationProgress, setSimulationProgress] = useState(0);
  
  const {
    config,
    preferences,
    analytics,
    setLoading,
    setProgress,
    updateConfig,
    updatePreferences,
  } = useTopLoaderStore();

  const simulateLoading = async () => {
    setIsSimulating(true);
    setLoading(true);
    setProgress(0);
    
    // Simulate loading progress
    for (let i = 0; i <= 100; i += 2) {
      setProgress(i);
      setSimulationProgress(i);
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    setLoading(false);
    setIsSimulating(false);
    setSimulationProgress(0);
  };

  const presetConfigs = [
    {
      name: "Fast & Minimal",
      config: {
        height: 2,
        speed: 100,
        showSpinner: false,
        easing: "ease-out",
      },
      description: "Quick and clean loading bar"
    },
    {
      name: "Smooth & Elegant",
      config: {
        height: 4,
        speed: 300,
        showSpinner: true,
        easing: "ease-in-out",
      },
      description: "Smooth transitions with spinner"
    },
    {
      name: "Bold & Dynamic",
      config: {
        height: 6,
        speed: 150,
        showSpinner: true,
        easing: "ease",
      },
      description: "Thick progress bar with animations"
    }
  ];

  const applyPreset = (preset: typeof presetConfigs[0]) => {
    updateConfig(preset.config);
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold">TopLoader Demo</h2>
        <p className="text-muted-foreground">
          Test and customize your loading experience
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Control Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <IconSettings className="h-5 w-5" />
              Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={simulateLoading}
              disabled={isSimulating}
              className="w-full"
              size="lg"
            >
              {isSimulating ? (
                <>
                  <IconPlayerStop className="h-4 w-4 mr-2" />
                  Simulating...
                </>
              ) : (
                <>
                  <IconPlayerPlay className="h-4 w-4 mr-2" />
                  Test Loading
                </>
              )}
            </Button>

            {isSimulating && (
              <div className="space-y-2">
                <Progress value={simulationProgress} className="w-full" />
                <div className="text-sm text-center text-muted-foreground">
                  {simulationProgress}% Complete
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => router.push('/dashboard')}
              >
                <IconRefresh className="h-4 w-4 mr-1" />
                Navigate
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => router.refresh()}
              >
                <IconRefresh className="h-4 w-4 mr-1" />
                Refresh
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Current Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <IconSettings className="h-5 w-5" />
              Current Config
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Height</span>
                <Badge variant="outline">{config.height}px</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Speed</span>
                <Badge variant="outline">{config.speed}ms</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Easing</span>
                <Badge variant="outline">{config.easing}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Spinner</span>
                <Badge variant={config.showSpinner ? "default" : "secondary"}>
                  {config.showSpinner ? "Yes" : "No"}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Animations</span>
                <Badge variant={preferences.enableAnimations ? "default" : "secondary"}>
                  {preferences.enableAnimations ? "Yes" : "No"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <IconChartBar className="h-5 w-5" />
              Analytics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Total Loads</span>
                <Badge variant="outline">{analytics.totalLoads}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Avg Time</span>
                <Badge variant="outline">{analytics.averageLoadTime.toFixed(0)}ms</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Recent Loads</span>
                <Badge variant="outline">{analytics.loadingTimes.length}</Badge>
              </div>
            </div>
            
            {analytics.loadingTimes.length > 0 && (
              <div className="space-y-1">
                <div className="text-xs font-medium text-muted-foreground">
                  Recent Times
                </div>
                <div className="flex flex-wrap gap-1">
                  {analytics.loadingTimes.slice(-3).map((time, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {time}ms
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Presets */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconPalette className="h-5 w-5" />
            Quick Presets
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {presetConfigs.map((preset, index) => (
              <Card key={index} className="cursor-pointer transition-all hover:shadow-md">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">{preset.name}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <p className="text-sm text-muted-foreground">
                    {preset.description}
                  </p>
                  <div className="space-y-1">
                    {Object.entries(preset.config).map(([key, value]) => (
                      <div key={key} className="flex justify-between text-xs">
                        <span className="capitalize">{key}</span>
                        <span className="font-mono">{String(value)}</span>
                      </div>
                    ))}
                  </div>
                  <Button 
                    onClick={() => applyPreset(preset)}
                    size="sm"
                    className="w-full"
                  >
                    Apply Preset
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Live Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Live Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative w-full h-8 bg-muted rounded-lg overflow-hidden">
            <div 
              className="absolute top-0 left-0 h-full bg-primary transition-all duration-300"
              style={{ 
                width: `${simulationProgress}%`,
                height: `${config.height}px`,
                boxShadow: config.shadow,
              }}
            />
          </div>
          <div className="mt-4 text-sm text-muted-foreground text-center">
            This simulates how your TopLoader will look
          </div>
        </CardContent>
      </Card>
    </div>
  );
}