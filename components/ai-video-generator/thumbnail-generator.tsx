'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Image, 
  Camera, 
  Download, 
  Upload,
  Trash2,
  CheckCircle,
  Loader2,
  RefreshCw,
  Scissors,
  Palette,
  Type,
  Plus
} from 'lucide-react';
import { ThumbnailData, GeneratedVideo } from '@/lib/video/types/video-types';
import { toast } from 'sonner';

interface ThumbnailGeneratorProps {
  video: GeneratedVideo;
  thumbnails: ThumbnailData[];
  selectedThumbnailId?: string;
  onThumbnailsGenerated: (thumbnails: ThumbnailData[]) => void;
  onThumbnailSelected: (thumbnailId: string) => void;
  onThumbnailDeleted: (thumbnailId: string) => void;
  onCustomThumbnailUploaded: (thumbnail: ThumbnailData) => void;
  className?: string;
}

interface ThumbnailGenerationSettings {
  count: number;
  quality: 'low' | 'medium' | 'high';
  format: 'jpg' | 'png' | 'webp';
  timestamps: number[];
}

export function ThumbnailGenerator({
  video,
  thumbnails,
  selectedThumbnailId,
  onThumbnailsGenerated,
  onThumbnailSelected,
  onThumbnailDeleted,
  onCustomThumbnailUploaded,
  className = ''
}: ThumbnailGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [settings, setSettings] = useState<ThumbnailGenerationSettings>({
    count: 6,
    quality: 'medium',
    format: 'jpg',
    timestamps: []
  });
  const [customTimestamp, setCustomTimestamp] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Initialize timestamps based on video duration
  useEffect(() => {
    if (video.duration && settings.timestamps.length === 0) {
      const timestamps = Array.from({ length: settings.count }, (_, i) => 
        (video.duration! / (settings.count + 1)) * (i + 1)
      );
      setSettings(prev => ({ ...prev, timestamps }));
    }
  }, [video.duration, settings.count, settings.timestamps.length]);

  const updateSettings = useCallback((updates: Partial<ThumbnailGenerationSettings>) => {
    setSettings(prev => ({ ...prev, ...updates }));
  }, []);

  const generateThumbnailsFromVideo = useCallback(async (videoUrl: string, timestamps: number[]): Promise<ThumbnailData[]> => {
    return new Promise((resolve) => {
      const video = document.createElement('video');
      const canvas = canvasRef.current || document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const generatedThumbnails: ThumbnailData[] = [];

      video.addEventListener('loadedmetadata', () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        
        let currentIndex = 0;
        
        const captureFrame = () => {
          if (currentIndex >= timestamps.length) {
            resolve(generatedThumbnails);
            return;
          }
          
          const timestamp = timestamps[currentIndex];
          video.currentTime = timestamp;
        };
        
        video.addEventListener('seeked', () => {
          if (ctx) {
            ctx.drawImage(video, 0, 0);
            const thumbnailUrl = canvas.toDataURL(`image/${settings.format}`, 
              settings.quality === 'high' ? 0.9 : settings.quality === 'medium' ? 0.7 : 0.5
            );
            
            const thumbnail: ThumbnailData = {
              id: `thumb-${Date.now()}-${currentIndex}`,
              url: thumbnailUrl,
              timestamp: timestamps[currentIndex],
              isSelected: false,
              isCustom: false
            };
            
            generatedThumbnails.push(thumbnail);
            currentIndex++;
            
            // Update progress
            setGenerationProgress((currentIndex / timestamps.length) * 100);
            
            // Capture next frame
            setTimeout(captureFrame, 100);
          }
        });
        
        captureFrame();
      });

      video.src = videoUrl;
      video.load();
    });
  }, [settings.format, settings.quality]);

  const generateThumbnails = useCallback(async () => {
    if (!video.videoUrl) {
      toast.error('Video URL not available');
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      const newThumbnails = await generateThumbnailsFromVideo(video.videoUrl, settings.timestamps);
      onThumbnailsGenerated(newThumbnails);
      toast.success(`Generated ${newThumbnails.length} thumbnails`);
    } catch (error) {
      toast.error('Failed to generate thumbnails');
      console.error('Thumbnail generation error:', error);
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  }, [video.videoUrl, settings.timestamps, generateThumbnailsFromVideo, onThumbnailsGenerated]);

  const generateCustomThumbnail = useCallback(async () => {
    if (!video.videoUrl) {
      toast.error('Video URL not available');
      return;
    }

    try {
      const thumbnails = await generateThumbnailsFromVideo(video.videoUrl, [customTimestamp]);
      if (thumbnails.length > 0) {
        const customThumbnail = { ...thumbnails[0], isCustom: true };
        onCustomThumbnailUploaded(customThumbnail);
        toast.success('Custom thumbnail generated');
      }
    } catch (error) {
      toast.error('Failed to generate custom thumbnail');
    }
  }, [video.videoUrl, customTimestamp, generateThumbnailsFromVideo, onCustomThumbnailUploaded]);

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const url = e.target?.result as string;
      const customThumbnail: ThumbnailData = {
        id: `custom-${Date.now()}`,
        url,
        timestamp: 0,
        isSelected: false,
        isCustom: true
      };
      
      onCustomThumbnailUploaded(customThumbnail);
      toast.success('Custom thumbnail uploaded');
    };
    
    reader.readAsDataURL(file);
  }, [onCustomThumbnailUploaded]);

  const updateTimestampCount = useCallback((count: number) => {
    const timestamps = Array.from({ length: count }, (_, i) => 
      (video.duration! / (count + 1)) * (i + 1)
    );
    updateSettings({ count, timestamps });
  }, [video.duration, updateSettings]);

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Generation Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Camera className="h-5 w-5" />
            Thumbnail Generation
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label>Number of Thumbnails: {settings.count}</Label>
              <Slider
                value={[settings.count]}
                onValueChange={([value]) => updateTimestampCount(value)}
                min={3}
                max={12}
                step={1}
                className="mt-2"
              />
            </div>
            
            <div>
              <Label>Quality</Label>
              <select 
                value={settings.quality}
                onChange={(e) => updateSettings({ quality: e.target.value as any })}
                className="w-full mt-2 p-2 border rounded"
              >
                <option value="low">Low (Fast)</option>
                <option value="medium">Medium</option>
                <option value="high">High (Slow)</option>
              </select>
            </div>
            
            <div>
              <Label>Format</Label>
              <select 
                value={settings.format}
                onChange={(e) => updateSettings({ format: e.target.value as any })}
                className="w-full mt-2 p-2 border rounded"
              >
                <option value="jpg">JPEG</option>
                <option value="png">PNG</option>
                <option value="webp">WebP</option>
              </select>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button 
              onClick={generateThumbnails}
              disabled={isGenerating}
              className="flex-1"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Generate Thumbnails
                </>
              )}
            </Button>
          </div>
          
          {isGenerating && (
            <div className="space-y-2">
              <Progress value={generationProgress} className="w-full" />
              <p className="text-sm text-muted-foreground text-center">
                {Math.round(generationProgress)}% complete
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Custom Thumbnail Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Scissors className="h-5 w-5" />
            Custom Thumbnail
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Capture at specific time: {formatTime(customTimestamp)}</Label>
            <Slider
              value={[customTimestamp]}
              onValueChange={([value]) => setCustomTimestamp(value)}
              min={0}
              max={video.duration || 5}
              step={0.1}
              className="mt-2"
            />
          </div>
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={generateCustomThumbnail}
              className="flex-1"
            >
              <Camera className="h-4 w-4 mr-2" />
              Capture Frame
            </Button>
            
            <Button 
              variant="outline" 
              onClick={() => fileInputRef.current?.click()}
              className="flex-1"
            >
              <Upload className="h-4 w-4 mr-2" />
              Upload Image
            </Button>
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            className="hidden"
          />
        </CardContent>
      </Card>

      {/* Thumbnail Gallery */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Image className="h-5 w-5" />
              Generated Thumbnails ({thumbnails.length})
            </CardTitle>
            
            {selectedThumbnailId && (
              <Badge variant="secondary">
                1 selected
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {thumbnails.length === 0 ? (
            <div className="text-center py-8">
              <Image className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="font-medium mb-2">No thumbnails generated</h3>
              <p className="text-sm text-muted-foreground">
                Generate thumbnails to see them here
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {thumbnails.map((thumbnail) => (
                <div
                  key={thumbnail.id}
                  className={`
                    relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all
                    ${thumbnail.id === selectedThumbnailId 
                      ? 'border-primary ring-2 ring-primary/20' 
                      : 'border-muted hover:border-primary/50'
                    }
                  `}
                  onClick={() => onThumbnailSelected(thumbnail.id)}
                >
                  <div className="aspect-video">
                    <img
                      src={thumbnail.url}
                      alt={`Thumbnail at ${formatTime(thumbnail.timestamp)}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors">
                    <div className="absolute top-2 left-2">
                      {thumbnail.isCustom && (
                        <Badge variant="secondary" className="text-xs">
                          Custom
                        </Badge>
                      )}
                    </div>
                    
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onThumbnailDeleted(thumbnail.id);
                        }}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                    
                    <div className="absolute bottom-2 left-2 right-2">
                      <div className="flex items-center justify-between">
                        <Badge variant="outline" className="text-xs bg-black/50 text-white border-white/20">
                          {formatTime(thumbnail.timestamp)}
                        </Badge>
                        
                        {thumbnail.id === selectedThumbnailId && (
                          <CheckCircle className="h-4 w-4 text-primary" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Hidden canvas for thumbnail generation */}
      <canvas ref={canvasRef} className="hidden" />
    </div>
  );
}
