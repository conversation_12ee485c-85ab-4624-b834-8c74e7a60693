/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AIPromptInput } from '../ai-prompt-input';
import { AIPromptData, AIGenerationSettings } from '@/lib/video/types/video-types';

// Mock the toast function
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn()
  }
}));

describe('AIPromptInput', () => {
  const mockOnPromptChange = jest.fn();
  const mockOnSettingsChange = jest.fn();

  const defaultProps = {
    onPromptChange: mockOnPromptChange,
    onSettingsChange: mockOnSettingsChange
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the prompt input interface', () => {
    render(<AIPromptInput {...defaultProps} />);
    
    expect(screen.getByText('AI Video Prompt')).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/describe the video/i)).toBeInTheDocument();
    expect(screen.getByText('Video Style')).toBeInTheDocument();
    expect(screen.getByText('Generation Settings')).toBeInTheDocument();
  });

  it('updates prompt text when user types', async () => {
    const user = userEvent.setup();
    render(<AIPromptInput {...defaultProps} />);
    
    const textArea = screen.getByPlaceholderText(/describe the video/i);
    await user.type(textArea, 'A beautiful sunset over mountains');
    
    await waitFor(() => {
      expect(mockOnPromptChange).toHaveBeenCalledWith(
        expect.objectContaining({
          text: 'A beautiful sunset over mountains'
        })
      );
    });
  });

  it('shows character count', async () => {
    const user = userEvent.setup();
    render(<AIPromptInput {...defaultProps} />);
    
    const textArea = screen.getByPlaceholderText(/describe the video/i);
    await user.type(textArea, 'Test prompt');
    
    expect(screen.getByText('11/500 characters')).toBeInTheDocument();
  });

  it('allows style selection', async () => {
    const user = userEvent.setup();
    render(<AIPromptInput {...defaultProps} />);
    
    // Find and click on a style option (assuming "Cinematic" is available)
    const cinematicStyle = screen.getByText('Cinematic');
    await user.click(cinematicStyle);
    
    await waitFor(() => {
      expect(mockOnPromptChange).toHaveBeenCalledWith(
        expect.objectContaining({
          style: expect.objectContaining({
            name: 'Cinematic'
          })
        })
      );
    });
  });

  it('updates duration setting', async () => {
    render(<AIPromptInput {...defaultProps} />);
    
    // Find the duration slider
    const durationSlider = screen.getByRole('slider', { name: /duration/i });
    
    // Simulate changing the slider value
    fireEvent.change(durationSlider, { target: { value: '10' } });
    
    await waitFor(() => {
      expect(mockOnPromptChange).toHaveBeenCalledWith(
        expect.objectContaining({
          duration: 10
        })
      );
    });
  });

  it('adds and removes tags', async () => {
    const user = userEvent.setup();
    render(<AIPromptInput {...defaultProps} />);
    
    // Add a tag
    const tagInput = screen.getByPlaceholderText(/add a tag/i);
    await user.type(tagInput, 'nature');
    
    const addButton = screen.getByRole('button', { name: /add/i });
    await user.click(addButton);
    
    await waitFor(() => {
      expect(mockOnPromptChange).toHaveBeenCalledWith(
        expect.objectContaining({
          tags: ['nature']
        })
      );
    });
    
    // Remove the tag
    const removeTagButton = screen.getByRole('button', { name: /×/ });
    await user.click(removeTagButton);
    
    await waitFor(() => {
      expect(mockOnPromptChange).toHaveBeenCalledWith(
        expect.objectContaining({
          tags: []
        })
      );
    });
  });

  it('updates aspect ratio and resolution', async () => {
    const user = userEvent.setup();
    render(<AIPromptInput {...defaultProps} />);
    
    // Find and click the aspect ratio select
    const aspectRatioSelect = screen.getByRole('combobox', { name: /aspect ratio/i });
    await user.click(aspectRatioSelect);
    
    // Select 9:16 option
    const portraitOption = screen.getByText('9:16 (Portrait)');
    await user.click(portraitOption);
    
    await waitFor(() => {
      expect(mockOnPromptChange).toHaveBeenCalledWith(
        expect.objectContaining({
          aspectRatio: '9:16'
        })
      );
      
      expect(mockOnSettingsChange).toHaveBeenCalledWith(
        expect.objectContaining({
          resolution: expect.objectContaining({
            width: 1080,
            height: 1920,
            aspectRatio: '9:16'
          })
        })
      );
    });
  });

  it('generates random seed', async () => {
    const user = userEvent.setup();
    render(<AIPromptInput {...defaultProps} />);
    
    // Find the random seed button
    const randomSeedButton = screen.getByRole('button', { name: /shuffle/i });
    await user.click(randomSeedButton);
    
    await waitFor(() => {
      expect(mockOnPromptChange).toHaveBeenCalledWith(
        expect.objectContaining({
          seed: expect.any(Number)
        })
      );
      
      expect(mockOnSettingsChange).toHaveBeenCalledWith(
        expect.objectContaining({
          seed: expect.any(Number)
        })
      );
    });
  });

  it('uses prompt suggestions', async () => {
    const user = userEvent.setup();
    render(<AIPromptInput {...defaultProps} />);
    
    // Find and click a suggestion
    const suggestion = screen.getByText('A serene mountain landscape at sunset');
    await user.click(suggestion);
    
    await waitFor(() => {
      expect(mockOnPromptChange).toHaveBeenCalledWith(
        expect.objectContaining({
          text: 'A serene mountain landscape at sunset'
        })
      );
    });
  });

  it('updates AI model setting', async () => {
    const user = userEvent.setup();
    render(<AIPromptInput {...defaultProps} />);
    
    // Find and click the AI model select
    const modelSelect = screen.getByRole('combobox', { name: /ai model/i });
    await user.click(modelSelect);
    
    // Select Pika Labs option
    const pikaOption = screen.getByText('Pika Labs');
    await user.click(pikaOption);
    
    await waitFor(() => {
      expect(mockOnSettingsChange).toHaveBeenCalledWith(
        expect.objectContaining({
          model: 'pika'
        })
      );
    });
  });

  it('updates guidance scale', async () => {
    render(<AIPromptInput {...defaultProps} />);
    
    // Find the guidance scale slider
    const guidanceSlider = screen.getByRole('slider', { name: /guidance scale/i });
    
    // Simulate changing the slider value
    fireEvent.change(guidanceSlider, { target: { value: '10' } });
    
    await waitFor(() => {
      expect(mockOnSettingsChange).toHaveBeenCalledWith(
        expect.objectContaining({
          guidanceScale: 10
        })
      );
    });
  });

  it('accepts initial prompt and settings', () => {
    const initialPrompt: Partial<AIPromptData> = {
      text: 'Initial prompt text',
      duration: 10,
      tags: ['test', 'initial']
    };
    
    const initialSettings: Partial<AIGenerationSettings> = {
      model: 'stability',
      quality: 'ultra'
    };
    
    render(
      <AIPromptInput 
        {...defaultProps}
        initialPrompt={initialPrompt}
        initialSettings={initialSettings}
      />
    );
    
    expect(screen.getByDisplayValue('Initial prompt text')).toBeInTheDocument();
    expect(screen.getByText('test')).toBeInTheDocument();
    expect(screen.getByText('initial')).toBeInTheDocument();
  });

  it('resets prompt when reset button is clicked', async () => {
    const user = userEvent.setup();
    render(<AIPromptInput {...defaultProps} />);
    
    // First, add some content
    const textArea = screen.getByPlaceholderText(/describe the video/i);
    await user.type(textArea, 'Some content to reset');
    
    // Click reset button
    const resetButton = screen.getByRole('button', { name: /reset/i });
    await user.click(resetButton);
    
    await waitFor(() => {
      expect(mockOnPromptChange).toHaveBeenCalledWith(
        expect.objectContaining({
          text: '',
          tags: [],
          negativePrompt: '',
          seed: undefined
        })
      );
    });
  });

  it('copies prompt to clipboard', async () => {
    const user = userEvent.setup();
    
    // Mock clipboard API
    Object.assign(navigator, {
      clipboard: {
        writeText: jest.fn().mockImplementation(() => Promise.resolve())
      }
    });
    
    render(<AIPromptInput {...defaultProps} />);
    
    // Add some text first
    const textArea = screen.getByPlaceholderText(/describe the video/i);
    await user.type(textArea, 'Text to copy');
    
    // Click copy button
    const copyButton = screen.getByRole('button', { name: /copy/i });
    await user.click(copyButton);
    
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith('Text to copy');
  });
});
