'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Image, 
  Video, 
  Camera, 
  Music, 
  Type, 
  Sparkles, 
  Layers, 
  Palette,
  Upload,
  Wand2,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SidebarItem {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  badge?: string;
}

interface VideoEditorSidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  className?: string;
}

const SIDEBAR_ITEMS: SidebarItem[] = [
  { id: 'media', icon: Image, label: 'Media' },
  { id: 'video', icon: Video, label: 'Video' },
  { id: 'photo', icon: Camera, label: 'Photo' },
  { id: 'audio', icon: Music, label: 'Audio' },
  { id: 'text', icon: Type, label: 'Text' },
  { id: 'ai-generate', icon: Wand2, label: 'AI Generate', badge: 'New' },
  { id: 'effects', icon: Sparkles, label: 'Effects' },
  { id: 'transitions', icon: Layers, label: 'Transitions' },
  { id: 'stickers', icon: Palette, label: 'Stickers' }
];

export function VideoEditorSidebar({ 
  activeTab, 
  onTabChange, 
  className 
}: VideoEditorSidebarProps) {
  return (
    <div className={cn(
      "w-16 border-r bg-background flex flex-col",
      className
    )}>
      <div className="p-2">
        <Button
          variant="ghost"
          size="icon"
          className="w-12 h-12 rounded-lg"
        >
          <Settings className="h-5 w-5" />
        </Button>
      </div>
      
      <Separator />
      
      <ScrollArea className="flex-1">
        <div className="p-2 space-y-2">
          {SIDEBAR_ITEMS.map((item) => {
            const Icon = item.icon;
            const isActive = activeTab === item.id;
            
            return (
              <div key={item.id} className="relative">
                <Button
                  variant={isActive ? "default" : "ghost"}
                  size="icon"
                  onClick={() => onTabChange(item.id)}
                  className={cn(
                    "w-12 h-12 rounded-lg relative",
                    isActive && "bg-primary text-primary-foreground"
                  )}
                  title={item.label}
                >
                  <Icon className="h-5 w-5" />
                </Button>
                
                {item.badge && (
                  <Badge 
                    variant="secondary" 
                    className="absolute -top-1 -right-1 text-xs px-1 py-0 h-4"
                  >
                    {item.badge}
                  </Badge>
                )}
              </div>
            );
          })}
        </div>
      </ScrollArea>
      
      <Separator />
      
      <div className="p-2">
        <Button
          variant="ghost"
          size="icon"
          className="w-12 h-12 rounded-lg"
        >
          <Upload className="h-5 w-5" />
        </Button>
      </div>
    </div>
  );
}
