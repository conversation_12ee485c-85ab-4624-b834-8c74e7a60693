'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Upload, 
  Wand2, 
  Play, 
  Download,
  Settings,
  History,
  Layers,
  Eye
} from 'lucide-react';
import { VideoUploadInterface } from './video-upload-interface';
import { AIPromptInput } from './ai-prompt-input';
import { VideoPreviewPlayer } from './video-preview-player';
import { ProgressTracking } from './progress-tracking';
import { 
  AIVideoGeneratorState,
  UploadedFile,
  AIPromptData,
  AIGenerationSettings,
  GeneratedVideo,
  ProcessingQueueItem
} from '@/lib/video/types/video-types';
import { toast } from 'sonner';

interface AIVideoGeneratorProps {
  onVideoGenerated?: (video: GeneratedVideo) => void;
  onVideoExported?: (videoUrl: string) => void;
  className?: string;
}

export function AIVideoGenerator({
  onVideoGenerated,
  onVideoExported,
  className = ''
}: AIVideoGeneratorProps) {
  const [state, setState] = useState<AIVideoGeneratorState>({
    currentStep: 'upload',
    uploadedFiles: [],
    prompt: {
      text: '',
      style: {
        id: 'realistic',
        name: 'Realistic',
        description: 'Photorealistic video generation',
        thumbnail: '/styles/realistic.jpg',
        category: 'realistic',
        parameters: { style_strength: 0.8 }
      },
      mood: '',
      duration: 5,
      aspectRatio: '16:9',
      tags: [],
      negativePrompt: '',
      seed: undefined,
      strength: 0.8
    },
    generationSettings: {
      model: 'runway',
      resolution: { width: 1920, height: 1080, aspectRatio: '16:9' },
      fps: 30,
      duration: 5,
      quality: 'high',
      iterations: 1,
      guidanceScale: 7.5,
      seed: undefined
    },
    generatedVideos: [],
    processingQueue: [],
    exportSettings: {
      format: 'mp4',
      resolution: { width: 1920, height: 1080, aspectRatio: '16:9' },
      fps: 30,
      bitrate: 5000000,
      codec: 'h264',
      quality: 'high',
      includeAudio: true
    }
  });

  const [activeTab, setActiveTab] = useState('upload');
  const [selectedVideo, setSelectedVideo] = useState<GeneratedVideo | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  const updateState = useCallback((updates: Partial<AIVideoGeneratorState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const handleFilesUploaded = useCallback((files: UploadedFile[]) => {
    updateState({ uploadedFiles: files });
    if (files.length > 0 && state.currentStep === 'upload') {
      updateState({ currentStep: 'prompt' });
      setActiveTab('prompt');
    }
  }, [state.currentStep, updateState]);

  const handlePromptChange = useCallback((prompt: AIPromptData) => {
    updateState({ prompt });
  }, [updateState]);

  const handleSettingsChange = useCallback((settings: AIGenerationSettings) => {
    updateState({ generationSettings: settings });
  }, [updateState]);

  const generateVideo = useCallback(async () => {
    if (!state.prompt.text.trim()) {
      toast.error('Please enter a prompt for video generation');
      return;
    }

    setIsGenerating(true);
    updateState({ currentStep: 'generate' });
    setActiveTab('progress');

    // Create processing queue item
    const queueItem: ProcessingQueueItem = {
      id: `gen-${Date.now()}`,
      type: 'ai-generation',
      title: `Generating: ${state.prompt.text.slice(0, 50)}...`,
      status: 'processing',
      progress: 0,
      startTime: new Date(),
      priority: 'normal',
      data: { prompt: state.prompt, settings: state.generationSettings }
    };

    updateState({ 
      processingQueue: [...state.processingQueue, queueItem]
    });

    try {
      // Simulate AI video generation process
      await simulateVideoGeneration(queueItem.id);
      
      // Create generated video
      const generatedVideo: GeneratedVideo = {
        id: `video-${Date.now()}`,
        prompt: state.prompt.text,
        style: state.prompt.style,
        settings: state.generationSettings,
        status: 'completed',
        progress: 100,
        videoUrl: '/demo-video.mp4', // This would be the actual generated video URL
        thumbnailUrl: '/demo-thumbnail.jpg',
        duration: state.generationSettings.duration,
        createdAt: new Date(),
        completedAt: new Date()
      };

      updateState({
        generatedVideos: [...state.generatedVideos, generatedVideo],
        currentStep: 'preview',
        processingQueue: state.processingQueue.map(item => 
          item.id === queueItem.id 
            ? { ...item, status: 'completed', progress: 100, endTime: new Date() }
            : item
        )
      });

      setSelectedVideo(generatedVideo);
      setActiveTab('preview');
      onVideoGenerated?.(generatedVideo);
      toast.success('Video generated successfully!');

    } catch (error) {
      updateState({
        processingQueue: state.processingQueue.map(item => 
          item.id === queueItem.id 
            ? { ...item, status: 'failed', error: 'Generation failed', endTime: new Date() }
            : item
        )
      });
      toast.error('Failed to generate video');
    } finally {
      setIsGenerating(false);
    }
  }, [state.prompt, state.generationSettings, state.processingQueue, state.generatedVideos, updateState, onVideoGenerated]);

  const simulateVideoGeneration = useCallback(async (queueItemId: string) => {
    // Simulate progress updates
    for (let progress = 0; progress <= 100; progress += 10) {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      updateState({
        processingQueue: state.processingQueue.map(item => 
          item.id === queueItemId 
            ? { ...item, progress }
            : item
        )
      });
    }
  }, [state.processingQueue, updateState]);

  const handleQueueItemCancel = useCallback((itemId: string) => {
    updateState({
      processingQueue: state.processingQueue.map(item => 
        item.id === itemId 
          ? { ...item, status: 'cancelled' }
          : item
      )
    });
    toast.info('Process cancelled');
  }, [state.processingQueue, updateState]);

  const handleQueueItemRetry = useCallback((itemId: string) => {
    const item = state.processingQueue.find(i => i.id === itemId);
    if (item?.type === 'ai-generation') {
      generateVideo();
    }
  }, [state.processingQueue, generateVideo]);

  const handleQueueItemView = useCallback((itemId: string) => {
    const video = state.generatedVideos.find(v => v.id === itemId);
    if (video) {
      setSelectedVideo(video);
      setActiveTab('preview');
    }
  }, [state.generatedVideos]);

  const canGenerate = state.prompt.text.trim().length > 0;
  const hasGeneratedVideos = state.generatedVideos.length > 0;
  const hasActiveProcesses = state.processingQueue.some(item => 
    item.status === 'processing' || item.status === 'queued'
  );

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Wand2 className="h-6 w-6" />
                AI Video Generator
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Create stunning videos with artificial intelligence
              </p>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="flex items-center gap-1">
                <Layers className="h-3 w-3" />
                {state.generatedVideos.length} videos
              </Badge>
              
              {hasActiveProcesses && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                  Processing
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="upload" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Upload
          </TabsTrigger>
          <TabsTrigger value="prompt" className="flex items-center gap-2">
            <Wand2 className="h-4 w-4" />
            Prompt
          </TabsTrigger>
          <TabsTrigger value="progress" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Progress
          </TabsTrigger>
          <TabsTrigger value="preview" className="flex items-center gap-2">
            <Play className="h-4 w-4" />
            Preview
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <History className="h-4 w-4" />
            History
          </TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-4">
          <VideoUploadInterface
            onFilesUploaded={handleFilesUploaded}
            maxFileSize={1024 * 1024 * 1024} // 1GB
            maxFiles={10}
          />
          
          {state.uploadedFiles.length > 0 && (
            <div className="flex justify-end">
              <Button onClick={() => setActiveTab('prompt')}>
                Continue to Prompt
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="prompt" className="space-y-4">
          <AIPromptInput
            onPromptChange={handlePromptChange}
            onSettingsChange={handleSettingsChange}
            initialPrompt={state.prompt}
            initialSettings={state.generationSettings}
          />
          
          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setActiveTab('upload')}>
              Back to Upload
            </Button>
            <Button 
              onClick={generateVideo}
              disabled={!canGenerate || isGenerating}
            >
              {isGenerating ? 'Generating...' : 'Generate Video'}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="progress" className="space-y-4">
          <ProgressTracking
            queueItems={state.processingQueue}
            onItemCancel={handleQueueItemCancel}
            onItemRetry={handleQueueItemRetry}
            onItemView={handleQueueItemView}
          />
        </TabsContent>

        <TabsContent value="preview" className="space-y-4">
          {selectedVideo ? (
            <div className="space-y-4">
              <VideoPreviewPlayer
                videoUrl={selectedVideo.videoUrl}
                thumbnailUrl={selectedVideo.thumbnailUrl}
                title={`Generated: ${selectedVideo.prompt.slice(0, 50)}...`}
                duration={selectedVideo.duration}
              />
              
              <Card>
                <CardHeader>
                  <CardTitle>Video Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <h4 className="font-medium mb-1">Prompt</h4>
                    <p className="text-sm text-muted-foreground">{selectedVideo.prompt}</p>
                  </div>
                  
                  <Separator />
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Style:</span> {selectedVideo.style.name}
                    </div>
                    <div>
                      <span className="font-medium">Duration:</span> {selectedVideo.duration}s
                    </div>
                    <div>
                      <span className="font-medium">Model:</span> {selectedVideo.settings.model}
                    </div>
                    <div>
                      <span className="font-medium">Quality:</span> {selectedVideo.settings.quality}
                    </div>
                  </div>
                  
                  <div className="flex gap-2 pt-2">
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <Play className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="font-medium mb-2">No video selected</h3>
                <p className="text-sm text-muted-foreground">
                  Generate a video or select one from your history to preview
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          {hasGeneratedVideos ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {state.generatedVideos.map((video) => (
                <Card key={video.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="aspect-video bg-muted rounded mb-3 flex items-center justify-center">
                      {video.thumbnailUrl ? (
                        <img 
                          src={video.thumbnailUrl} 
                          alt={video.prompt}
                          className="w-full h-full object-cover rounded"
                        />
                      ) : (
                        <Play className="h-8 w-8 text-muted-foreground" />
                      )}
                    </div>
                    
                    <h4 className="font-medium mb-1 line-clamp-2">
                      {video.prompt.slice(0, 60)}...
                    </h4>
                    
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span>{video.style.name}</span>
                      <span>{video.duration}s</span>
                    </div>
                    
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full mt-3"
                      onClick={() => {
                        setSelectedVideo(video);
                        setActiveTab('preview');
                      }}
                    >
                      View
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <History className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="font-medium mb-2">No videos generated yet</h3>
                <p className="text-sm text-muted-foreground">
                  Your generated videos will appear here
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
