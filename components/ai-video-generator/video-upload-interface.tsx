'use client';

import React, { useCallback, useState, useRef } from 'react';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Upload, 
  X, 
  Play, 
  Pause, 
  FileVideo, 
  FileAudio, 
  FileImage,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { UploadedFile } from '@/lib/video/types/video-types';
import { toast } from 'sonner';

interface VideoUploadInterfaceProps {
  onFilesUploaded: (files: UploadedFile[]) => void;
  maxFileSize?: number;
  maxFiles?: number;
  acceptedFormats?: string[];
  className?: string;
}

export function VideoUploadInterface({
  onFilesUploaded,
  maxFileSize = 1024 * 1024 * 1024, // 1GB
  maxFiles = 10,
  acceptedFormats = ['video/*', 'audio/*', 'image/*'],
  className = ''
}: VideoUploadInterfaceProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragActive, setIsDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const processFile = useCallback(async (file: File): Promise<UploadedFile> => {
    const id = `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const url = URL.createObjectURL(file);
    
    // Determine file type
    let type: 'video' | 'audio' | 'image' = 'video';
    if (file.type.startsWith('audio/')) {
      type = 'audio';
    } else if (file.type.startsWith('image/')) {
      type = 'image';
    }

    // Create thumbnail for video files
    let thumbnail: string | undefined;
    if (type === 'video') {
      thumbnail = await generateVideoThumbnail(url);
    } else if (type === 'image') {
      thumbnail = url;
    }

    const uploadedFile: UploadedFile = {
      id,
      file,
      name: file.name,
      size: file.size,
      type,
      url,
      thumbnail,
      uploadProgress: 0,
      status: 'uploading'
    };

    // Simulate upload progress
    simulateUploadProgress(uploadedFile);

    return uploadedFile;
  }, []);

  const generateVideoThumbnail = useCallback((videoUrl: string): Promise<string> => {
    return new Promise((resolve) => {
      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      video.addEventListener('loadedmetadata', () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        video.currentTime = Math.min(1, video.duration / 2); // Seek to middle or 1 second
      });

      video.addEventListener('seeked', () => {
        if (ctx) {
          ctx.drawImage(video, 0, 0);
          const thumbnail = canvas.toDataURL('image/jpeg', 0.8);
          resolve(thumbnail);
        }
      });

      video.src = videoUrl;
      video.load();
    });
  }, []);

  const simulateUploadProgress = useCallback((file: UploadedFile) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 15;
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);
        
        setUploadedFiles(prev => 
          prev.map(f => 
            f.id === file.id 
              ? { ...f, uploadProgress: 100, status: 'ready' }
              : f
          )
        );
      } else {
        setUploadedFiles(prev => 
          prev.map(f => 
            f.id === file.id 
              ? { ...f, uploadProgress: progress }
              : f
          )
        );
      }
    }, 200);
  }, []);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (uploadedFiles.length + acceptedFiles.length > maxFiles) {
      toast.error(`Maximum ${maxFiles} files allowed`);
      return;
    }

    const validFiles = acceptedFiles.filter(file => {
      if (file.size > maxFileSize) {
        toast.error(`File ${file.name} is too large. Maximum size is ${formatFileSize(maxFileSize)}`);
        return false;
      }
      return true;
    });

    const newFiles: UploadedFile[] = [];
    for (const file of validFiles) {
      try {
        const uploadedFile = await processFile(file);
        newFiles.push(uploadedFile);
      } catch (error) {
        toast.error(`Failed to process ${file.name}`);
      }
    }

    setUploadedFiles(prev => [...prev, ...newFiles]);
    onFilesUploaded([...uploadedFiles, ...newFiles]);
  }, [uploadedFiles, maxFiles, maxFileSize, processFile, onFilesUploaded]);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: acceptedFormats.reduce((acc, format) => ({ ...acc, [format]: [] }), {}),
    maxFiles,
    onDragEnter: () => setIsDragActive(true),
    onDragLeave: () => setIsDragActive(false),
    onDropAccepted: () => setIsDragActive(false),
    onDropRejected: () => setIsDragActive(false)
  });

  const removeFile = useCallback((fileId: string) => {
    setUploadedFiles(prev => {
      const updated = prev.filter(f => f.id !== fileId);
      onFilesUploaded(updated);
      return updated;
    });
  }, [onFilesUploaded]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'video': return <FileVideo className="h-5 w-5" />;
      case 'audio': return <FileAudio className="h-5 w-5" />;
      case 'image': return <FileImage className="h-5 w-5" />;
      default: return <FileVideo className="h-5 w-5" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'uploading': return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'ready': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error': return <AlertCircle className="h-4 w-4 text-red-500" />;
      default: return null;
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload Media Files
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${isDragActive 
                ? 'border-primary bg-primary/5' 
                : 'border-muted-foreground/25 hover:border-primary/50'
              }
            `}
          >
            <input {...getInputProps()} ref={fileInputRef} />
            <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">
              {isDragActive ? 'Drop files here' : 'Drag & drop files here'}
            </h3>
            <p className="text-muted-foreground mb-4">
              or click to browse your files
            </p>
            <Button variant="outline">
              Choose Files
            </Button>
            <div className="mt-4 text-sm text-muted-foreground">
              <p>Supported formats: Video, Audio, Images</p>
              <p>Maximum file size: {formatFileSize(maxFileSize)}</p>
              <p>Maximum files: {maxFiles}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Uploaded Files ({uploadedFiles.length})</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {uploadedFiles.map((file, index) => (
              <div key={file.id}>
                <div className="flex items-center gap-3 p-3 rounded-lg border">
                  {/* File Icon/Thumbnail */}
                  <div className="flex-shrink-0">
                    {file.thumbnail ? (
                      <img 
                        src={file.thumbnail} 
                        alt={file.name}
                        className="w-12 h-12 object-cover rounded"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-muted rounded flex items-center justify-center">
                        {getFileIcon(file.type)}
                      </div>
                    )}
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <p className="font-medium truncate">{file.name}</p>
                      <Badge variant="secondary" className="text-xs">
                        {file.type}
                      </Badge>
                      {getStatusIcon(file.status)}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {formatFileSize(file.size)}
                    </p>
                    
                    {/* Upload Progress */}
                    {file.status === 'uploading' && (
                      <div className="mt-2">
                        <Progress value={file.uploadProgress} className="h-2" />
                        <p className="text-xs text-muted-foreground mt-1">
                          {Math.round(file.uploadProgress)}% uploaded
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(file.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                {index < uploadedFiles.length - 1 && <Separator />}
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
