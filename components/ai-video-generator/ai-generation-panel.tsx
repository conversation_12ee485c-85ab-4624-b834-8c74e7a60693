'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Wand2, 
  <PERSON>rkles, 
  Clock, 
  Ratio, 
  Palette,
  Shuffle,
  Copy,
  RotateCcw,
  Play,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { AIPromptData, AIGenerationSettings } from '@/lib/video/types/video-types';
import { toast } from 'sonner';

interface AIGenerationPanelProps {
  onGenerate: (prompt: AIPromptData, settings: AIGenerationSettings) => void;
  isGenerating?: boolean;
  className?: string;
}

const VIDEO_STYLES = [
  { id: 'realistic', name: 'Realistic', description: 'Photorealistic video' },
  { id: 'cinematic', name: 'Cinematic', description: 'Movie-like style' },
  { id: 'animated', name: 'Animated', description: '3D animation style' },
  { id: 'artistic', name: 'Artistic', description: 'Artistic and stylized' }
];

const PROMPT_SUGGESTIONS = [
  'A serene mountain landscape at sunset',
  'Bustling city street with neon lights',
  'Ocean waves crashing on rocky shore',
  'Forest path with dappled sunlight',
  'Futuristic cityscape with flying cars',
  'Cozy cabin in snowy winter scene'
];

const ASPECT_RATIOS = [
  { label: '16:9 (Landscape)', value: '16:9' },
  { label: '9:16 (Portrait)', value: '9:16' },
  { label: '1:1 (Square)', value: '1:1' },
  { label: '4:3 (Classic)', value: '4:3' }
];

export function AIGenerationPanel({
  onGenerate,
  isGenerating = false,
  className
}: AIGenerationPanelProps) {
  const [prompt, setPrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState(VIDEO_STYLES[0]);
  const [duration, setDuration] = useState(5);
  const [aspectRatio, setAspectRatio] = useState('16:9');
  const [model, setModel] = useState('runway');
  const [quality, setQuality] = useState('high');
  const [guidanceScale, setGuidanceScale] = useState(7.5);
  const [seed, setSeed] = useState<number | undefined>(undefined);
  const [negativePrompt, setNegativePrompt] = useState('');

  const characterCount = prompt.length;
  const maxCharacters = 500;

  const handleGenerate = useCallback(() => {
    if (!prompt.trim()) {
      toast.error('Please enter a prompt for video generation');
      return;
    }

    const promptData: AIPromptData = {
      text: prompt,
      style: {
        id: selectedStyle.id,
        name: selectedStyle.name,
        description: selectedStyle.description,
        thumbnail: `/styles/${selectedStyle.id}.jpg`,
        category: selectedStyle.id as any,
        parameters: {}
      },
      mood: '',
      duration,
      aspectRatio,
      tags: [],
      negativePrompt,
      seed,
      strength: 0.8
    };

    const settings: AIGenerationSettings = {
      model: model as any,
      resolution: getResolutionFromAspectRatio(aspectRatio),
      fps: 30,
      duration,
      quality: quality as any,
      iterations: 1,
      guidanceScale,
      seed
    };

    onGenerate(promptData, settings);
  }, [prompt, selectedStyle, duration, aspectRatio, model, quality, guidanceScale, seed, negativePrompt, onGenerate]);

  const getResolutionFromAspectRatio = (ratio: string) => {
    switch (ratio) {
      case '16:9':
        return { width: 1920, height: 1080, aspectRatio: ratio };
      case '9:16':
        return { width: 1080, height: 1920, aspectRatio: ratio };
      case '1:1':
        return { width: 1080, height: 1080, aspectRatio: ratio };
      case '4:3':
        return { width: 1440, height: 1080, aspectRatio: ratio };
      default:
        return { width: 1920, height: 1080, aspectRatio: '16:9' };
    }
  };

  const generateRandomSeed = () => {
    setSeed(Math.floor(Math.random() * 1000000));
  };

  const copyPrompt = () => {
    navigator.clipboard.writeText(prompt);
    toast.success('Prompt copied to clipboard');
  };

  const resetForm = () => {
    setPrompt('');
    setSelectedStyle(VIDEO_STYLES[0]);
    setDuration(5);
    setAspectRatio('16:9');
    setModel('runway');
    setQuality('high');
    setGuidanceScale(7.5);
    setSeed(undefined);
    setNegativePrompt('');
  };

  return (
    <Card className={cn("w-80 flex flex-col", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <Wand2 className="h-5 w-5" />
          AI Video Generator
        </CardTitle>
      </CardHeader>
      
      <CardContent className="flex-1">
        <ScrollArea className="h-full pr-4">
          <div className="space-y-6">
            {/* Main Prompt */}
            <div className="space-y-2">
              <Label htmlFor="prompt">Describe your video</Label>
              <Textarea
                id="prompt"
                placeholder="A beautiful sunset over mountains..."
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                rows={4}
                className="resize-none"
              />
              <div className="flex justify-between items-center">
                <span className={cn(
                  "text-xs",
                  characterCount > maxCharacters ? "text-destructive" : "text-muted-foreground"
                )}>
                  {characterCount}/{maxCharacters}
                </span>
                <div className="flex gap-1">
                  <Button variant="ghost" size="sm" onClick={copyPrompt}>
                    <Copy className="h-3 w-3" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={resetForm}>
                    <RotateCcw className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Quick Suggestions */}
            <div className="space-y-2">
              <Label>Quick suggestions</Label>
              <div className="grid grid-cols-1 gap-1">
                {PROMPT_SUGGESTIONS.slice(0, 3).map((suggestion, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => setPrompt(suggestion)}
                    className="text-xs h-8 justify-start"
                  >
                    {suggestion}
                  </Button>
                ))}
              </div>
            </div>

            <Separator />

            {/* Style Selection */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Style
              </Label>
              <div className="grid grid-cols-2 gap-2">
                {VIDEO_STYLES.map((style) => (
                  <div
                    key={style.id}
                    className={cn(
                      "cursor-pointer rounded-lg border-2 p-2 transition-all text-center",
                      selectedStyle.id === style.id 
                        ? "border-primary bg-primary/5" 
                        : "border-muted hover:border-primary/50"
                    )}
                    onClick={() => setSelectedStyle(style)}
                  >
                    <div className="aspect-video bg-muted rounded mb-1 flex items-center justify-center">
                      <Sparkles className="h-4 w-4 text-muted-foreground" />
                    </div>
                    <p className="text-xs font-medium">{style.name}</p>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Settings */}
            <div className="space-y-4">
              <Label className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Settings
              </Label>

              {/* Duration */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2 text-sm">
                  <Clock className="h-3 w-3" />
                  Duration: {duration}s
                </Label>
                <Slider
                  value={[duration]}
                  onValueChange={([value]) => setDuration(value)}
                  min={1}
                  max={30}
                  step={1}
                />
              </div>

              {/* Aspect Ratio */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2 text-sm">
                  <Ratio className="h-3 w-3" />
                  Aspect Ratio
                </Label>
                <Select value={aspectRatio} onValueChange={setAspectRatio}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {ASPECT_RATIOS.map((ratio) => (
                      <SelectItem key={ratio.value} value={ratio.value}>
                        {ratio.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Model */}
              <div className="space-y-2">
                <Label className="text-sm">AI Model</Label>
                <Select value={model} onValueChange={setModel}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="runway">Runway ML</SelectItem>
                    <SelectItem value="pika">Pika Labs</SelectItem>
                    <SelectItem value="stability">Stability AI</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Quality */}
              <div className="space-y-2">
                <Label className="text-sm">Quality</Label>
                <Select value={quality} onValueChange={setQuality}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="standard">Standard</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Separator />

            {/* Generate Button */}
            <Button 
              onClick={handleGenerate}
              disabled={!prompt.trim() || isGenerating}
              className="w-full"
              size="lg"
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                  Generating...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Generate Video
                </>
              )}
            </Button>
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
