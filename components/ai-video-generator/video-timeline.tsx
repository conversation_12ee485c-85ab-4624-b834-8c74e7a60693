'use client';

import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Pause, 
  SkipBack, 
  Ski<PERSON>Forward,
  Scissors,
  Copy,
  Trash2,
  Volume2,
  VolumeX,
  Eye,
  EyeOff,
  Lock,
  Unlock
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { VideoClip } from '@/lib/video/types/video-types';
import { formatTime } from '@/lib/video/utils/video-utils';

interface VideoTimelineProps {
  clips: VideoClip[];
  currentTime: number;
  duration: number;
  isPlaying: boolean;
  onTimeChange: (time: number) => void;
  onPlayPause: () => void;
  onClipSelect: (clip: VideoClip) => void;
  onClipDelete: (clipId: string) => void;
  onClipDuplicate: (clip: VideoClip) => void;
  selectedClipId?: string;
  className?: string;
}

interface TimelineTrack {
  id: number;
  name: string;
  type: 'video' | 'audio' | 'text' | 'effects';
  visible: boolean;
  locked: boolean;
  muted: boolean;
}

const DEFAULT_TRACKS: TimelineTrack[] = [
  { id: 0, name: 'Video 1', type: 'video', visible: true, locked: false, muted: false },
  { id: 1, name: 'Video 2', type: 'video', visible: true, locked: false, muted: false },
  { id: 2, name: 'Audio 1', type: 'audio', visible: true, locked: false, muted: false },
  { id: 3, name: 'Text', type: 'text', visible: true, locked: false, muted: false }
];

export function VideoTimeline({
  clips,
  currentTime,
  duration,
  isPlaying,
  onTimeChange,
  onPlayPause,
  onClipSelect,
  onClipDelete,
  onClipDuplicate,
  selectedClipId,
  className
}: VideoTimelineProps) {
  const timelineRef = useRef<HTMLDivElement>(null);
  const [tracks, setTracks] = useState<TimelineTrack[]>(DEFAULT_TRACKS);
  const [zoom, setZoom] = useState(1);
  const [isDragging, setIsDragging] = useState(false);

  // Timeline dimensions
  const pixelsPerSecond = 50 * zoom;
  const timelineWidth = duration * pixelsPerSecond;
  const playheadPosition = currentTime * pixelsPerSecond;

  const handleTimelineClick = (e: React.MouseEvent) => {
    if (!timelineRef.current) return;
    
    const rect = timelineRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const time = x / pixelsPerSecond;
    onTimeChange(Math.max(0, Math.min(duration, time)));
  };

  const toggleTrackVisibility = (trackId: number) => {
    setTracks(prev => prev.map(track => 
      track.id === trackId 
        ? { ...track, visible: !track.visible }
        : track
    ));
  };

  const toggleTrackLock = (trackId: number) => {
    setTracks(prev => prev.map(track => 
      track.id === trackId 
        ? { ...track, locked: !track.locked }
        : track
    ));
  };

  const toggleTrackMute = (trackId: number) => {
    setTracks(prev => prev.map(track => 
      track.id === trackId 
        ? { ...track, muted: !track.muted }
        : track
    ));
  };

  const getClipColor = (clip: VideoClip) => {
    switch (clip.type) {
      case 'video':
        return 'bg-blue-500';
      case 'audio':
        return 'bg-green-500';
      case 'image':
        return 'bg-purple-500';
      case 'text':
        return 'bg-orange-500';
      case 'ai-generated':
        return 'bg-pink-500';
      default:
        return 'bg-gray-500';
    }
  };

  const renderTimeMarkers = () => {
    const markers = [];
    const interval = Math.max(1, Math.floor(10 / zoom)); // Adjust interval based on zoom
    
    for (let i = 0; i <= duration; i += interval) {
      markers.push(
        <div
          key={i}
          className="absolute top-0 bottom-0 border-l border-muted-foreground/20"
          style={{ left: i * pixelsPerSecond }}
        >
          <span className="absolute -top-5 -translate-x-1/2 text-xs text-muted-foreground">
            {formatTime(i)}
          </span>
        </div>
      );
    }
    
    return markers;
  };

  return (
    <Card className={cn("flex flex-col", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm">Timeline</CardTitle>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" onClick={onPlayPause}>
              {isPlaying ? (
                <Pause className="h-4 w-4" />
              ) : (
                <Play className="h-4 w-4" />
              )}
            </Button>
            <Button variant="ghost" size="sm">
              <SkipBack className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <SkipForward className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 p-0">
        <div className="flex h-64">
          {/* Track Headers */}
          <div className="w-32 border-r bg-muted/30">
            <div className="h-8 border-b bg-background flex items-center px-2">
              <span className="text-xs font-medium">Tracks</span>
            </div>
            {tracks.map((track) => (
              <div
                key={track.id}
                className="h-12 border-b flex items-center px-2 space-x-1"
              >
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleTrackVisibility(track.id)}
                  className="h-6 w-6 p-0"
                >
                  {track.visible ? (
                    <Eye className="h-3 w-3" />
                  ) : (
                    <EyeOff className="h-3 w-3" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleTrackLock(track.id)}
                  className="h-6 w-6 p-0"
                >
                  {track.locked ? (
                    <Lock className="h-3 w-3" />
                  ) : (
                    <Unlock className="h-3 w-3" />
                  )}
                </Button>
                {track.type === 'audio' && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleTrackMute(track.id)}
                    className="h-6 w-6 p-0"
                  >
                    {track.muted ? (
                      <VolumeX className="h-3 w-3" />
                    ) : (
                      <Volume2 className="h-3 w-3" />
                    )}
                  </Button>
                )}
                <span className="text-xs flex-1 truncate">{track.name}</span>
              </div>
            ))}
          </div>

          {/* Timeline Area */}
          <div className="flex-1 relative">
            <ScrollArea className="h-full">
              <div
                ref={timelineRef}
                className="relative h-full cursor-pointer"
                style={{ width: Math.max(timelineWidth, 800) }}
                onClick={handleTimelineClick}
              >
                {/* Time Markers */}
                <div className="h-8 border-b bg-background relative">
                  {renderTimeMarkers()}
                </div>

                {/* Tracks */}
                {tracks.map((track) => (
                  <div
                    key={track.id}
                    className={cn(
                      "h-12 border-b relative",
                      !track.visible && "opacity-50"
                    )}
                  >
                    {/* Track Clips */}
                    {clips
                      .filter(clip => clip.track === track.id)
                      .map((clip) => (
                        <div
                          key={clip.id}
                          className={cn(
                            "absolute top-1 bottom-1 rounded cursor-pointer border-2 transition-all",
                            getClipColor(clip),
                            selectedClipId === clip.id 
                              ? "border-primary ring-2 ring-primary/20" 
                              : "border-transparent hover:border-primary/50"
                          )}
                          style={{
                            left: clip.position * pixelsPerSecond,
                            width: clip.duration * pixelsPerSecond
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            onClipSelect(clip);
                          }}
                        >
                          <div className="h-full flex items-center px-2">
                            <span className="text-xs text-white font-medium truncate">
                              {clip.type === 'ai-generated' ? 'AI Video' : `${clip.type}`}
                            </span>
                          </div>
                          
                          {/* Clip Actions */}
                          {selectedClipId === clip.id && (
                            <div className="absolute -top-8 left-0 flex space-x-1">
                              <Button
                                variant="secondary"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onClipDuplicate(clip);
                                }}
                                className="h-6 w-6 p-0"
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="secondary"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onClipDelete(clip.id);
                                }}
                                className="h-6 w-6 p-0"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </div>
                      ))}
                  </div>
                ))}

                {/* Playhead */}
                <div
                  className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10 pointer-events-none"
                  style={{ left: playheadPosition }}
                >
                  <div className="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full" />
                </div>
              </div>
            </ScrollArea>
          </div>
        </div>

        {/* Timeline Controls */}
        <div className="p-2 border-t bg-muted/30 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-xs text-muted-foreground">
              {formatTime(currentTime)} / {formatTime(duration)}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setZoom(Math.max(0.5, zoom - 0.5))}
            >
              -
            </Button>
            <span className="text-xs text-muted-foreground w-12 text-center">
              {Math.round(zoom * 100)}%
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setZoom(Math.min(3, zoom + 0.5))}
            >
              +
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
