'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { 
  ResizableHandle, 
  ResizablePanel, 
  ResizablePanelGroup 
} from '@/components/ui/resizable';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Pause, 
  SkipBack, 
  SkipForward,
  Volume2,
  Maximize2,
  Settings,
  PanelLeftClose,
  PanelLeftOpen,
  PanelRightClose,
  PanelRightOpen,
  LayoutGrid
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { VideoSystem } from '@/lib/video';
import { AIVideoIntegration } from '@/lib/video/ai-video-integration';
import { 
  AIPromptData,
  AIGenerationSettings,
  GeneratedVideo,
  VideoClip
} from '@/lib/video/types/video-types';
import { toast } from 'sonner';

// Import components
import { VideoEditorSidebar } from './video-editor-sidebar';
import { VideoPreviewPanel } from './video-preview-panel';
import { AIGenerationPanel } from './ai-generation-panel';
import { VideoTimeline } from './video-timeline';
import { VSCodeTabs, TabContent, TabPanel, DEFAULT_TABS } from './vscode-tabs';

interface VideoStudioLayoutProps {
  onVideoGenerated?: (video: GeneratedVideo) => void;
  onVideoExported?: (url: string) => void;
  className?: string;
}

export function VideoStudioLayout({
  onVideoGenerated,
  onVideoExported,
  className = ''
}: VideoStudioLayoutProps) {
  // Layout state
  const [leftPanelCollapsed, setLeftPanelCollapsed] = useState(false);
  const [rightPanelCollapsed, setRightPanelCollapsed] = useState(false);
  const [bottomPanelCollapsed, setBottomPanelCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState('ai-generate');
  const [tabs, setTabs] = useState(DEFAULT_TABS);

  // Video state
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(30);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  
  // Video system integration
  const videoSystemRef = useRef<VideoSystem | null>(null);
  const aiIntegrationRef = useRef<AIVideoIntegration | null>(null);
  
  // Content state
  const [generatedVideos, setGeneratedVideos] = useState<GeneratedVideo[]>([]);
  const [timelineClips, setTimelineClips] = useState<VideoClip[]>([]);
  const [selectedClipId, setSelectedClipId] = useState<string | undefined>();
  const [currentVideoUrl, setCurrentVideoUrl] = useState<string | undefined>();
  const [currentThumbnailUrl, setCurrentThumbnailUrl] = useState<string | undefined>();

  // Initialize video system
  useEffect(() => {
    const initializeVideoSystem = async () => {
      try {
        const videoSystem = new VideoSystem({
          tempDirectory: '/tmp/video-system',
          maxFileSize: 1024 * 1024 * 1024,
          supportedFormats: ['mp4', 'webm', 'mov'],
          webcodecs: {
            enabled: true,
            hardwareAcceleration: true
          }
        });

        const aiIntegration = new AIVideoIntegration(videoSystem);
        
        videoSystemRef.current = videoSystem;
        aiIntegrationRef.current = aiIntegration;

        // Set up event listeners
        aiIntegration.on('video:generated', (video: GeneratedVideo) => {
          setGeneratedVideos(prev => [...prev, video]);
          setCurrentVideoUrl(video.videoUrl);
          setCurrentThumbnailUrl(video.thumbnailUrl);
          onVideoGenerated?.(video);
          setIsGenerating(false);
          setGenerationProgress(0);
          toast.success('Video generated successfully!');
        });

        aiIntegration.on('generation:progress', (data: { progress: number }) => {
          setGenerationProgress(data.progress);
        });

        aiIntegration.on('generation:failed', (error: any) => {
          setIsGenerating(false);
          setGenerationProgress(0);
          toast.error(`Generation failed: ${error.message || 'Unknown error'}`);
        });

      } catch (error) {
        console.error('Failed to initialize video system:', error);
        toast.error('Failed to initialize video system');
      }
    };

    initializeVideoSystem();

    return () => {
      if (aiIntegrationRef.current) {
        aiIntegrationRef.current.removeAllListeners();
      }
    };
  }, [onVideoGenerated]);

  // Handlers
  const handleGenerateVideo = useCallback(async (
    promptData: AIPromptData, 
    settings: AIGenerationSettings
  ) => {
    if (!aiIntegrationRef.current) {
      toast.error('Video system not initialized');
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      await aiIntegrationRef.current.generateVideo(promptData, settings);
    } catch (error) {
      console.error('Video generation error:', error);
      setIsGenerating(false);
      setGenerationProgress(0);
      toast.error('Failed to generate video');
    }
  }, []);

  const handlePlayPause = useCallback(() => {
    setIsPlaying(prev => !prev);
  }, []);

  const handleTimeChange = useCallback((time: number) => {
    setCurrentTime(time);
  }, []);

  const handleClipSelect = useCallback((clip: VideoClip) => {
    setSelectedClipId(clip.id);
    if (clip.source && typeof clip.source === 'string') {
      setCurrentVideoUrl(clip.source);
    }
  }, []);

  const handleClipDelete = useCallback((clipId: string) => {
    setTimelineClips(prev => prev.filter(clip => clip.id !== clipId));
    if (selectedClipId === clipId) {
      setSelectedClipId(undefined);
    }
    toast.success('Clip deleted');
  }, [selectedClipId]);

  const handleClipDuplicate = useCallback((clip: VideoClip) => {
    const newClip: VideoClip = {
      ...clip,
      id: `${clip.id}-copy-${Date.now()}`,
      position: clip.position + clip.duration
    };
    setTimelineClips(prev => [...prev, newClip]);
    toast.success('Clip duplicated');
  }, []);

  const handleExport = useCallback(async () => {
    if (!aiIntegrationRef.current || timelineClips.length === 0) {
      toast.error('No clips to export');
      return;
    }

    try {
      const exportSettings = {
        resolution: { width: 1920, height: 1080, aspectRatio: '16:9' },
        fps: 30,
        bitrate: 5000,
        codec: 'h264' as const,
        format: 'mp4' as const,
        quality: 'high' as const,
        includeAudio: true,
        watermark: {
          enabled: false,
          position: 'bottom-right' as const,
          opacity: 0.7
        }
      };

      const videoIds = timelineClips.map(clip => clip.id);
      const exportUrls = await aiIntegrationRef.current.exportVideos(videoIds, exportSettings);
      
      if (exportUrls.length > 0) {
        onVideoExported?.(exportUrls[0]);
        toast.success('Video exported successfully!');
      }
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export video');
    }
  }, [timelineClips, onVideoExported]);

  const handleTabClose = useCallback((tabId: string) => {
    setTabs(prev => prev.filter(tab => tab.id !== tabId));
    if (activeTab === tabId) {
      const remainingTabs = tabs.filter(tab => tab.id !== tabId);
      if (remainingTabs.length > 0) {
        setActiveTab(remainingTabs[0].id);
      }
    }
  }, [activeTab, tabs]);

  const handleNewTab = useCallback(() => {
    const newTab = {
      id: `new-tab-${Date.now()}`,
      title: 'Untitled',
      modified: true
    };
    setTabs(prev => [...prev, newTab]);
    setActiveTab(newTab.id);
  }, []);

  return (
    <div className={cn("h-screen bg-background flex flex-col", className)}>
      {/* Top Menu Bar */}
      <div className="h-8 bg-muted/30 border-b border-border flex items-center justify-between px-4">
        <div className="flex items-center space-x-4">
          <span className="text-xs font-medium">AI Video Studio</span>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setLeftPanelCollapsed(!leftPanelCollapsed)}
              className="h-6 w-6 p-0"
            >
              {leftPanelCollapsed ? <PanelLeftOpen className="h-3 w-3" /> : <PanelLeftClose className="h-3 w-3" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setRightPanelCollapsed(!rightPanelCollapsed)}
              className="h-6 w-6 p-0"
            >
              {rightPanelCollapsed ? <PanelRightOpen className="h-3 w-3" /> : <PanelRightClose className="h-3 w-3" />}
            </Button>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {isGenerating && (
            <Badge variant="secondary" className="text-xs">
              <div className="w-2 h-2 bg-orange-500 rounded-full mr-1 animate-pulse" />
              Generating {Math.round(generationProgress)}%
            </Badge>
          )}
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
            <Settings className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Main Layout */}
      <div className="flex-1 overflow-hidden">
        <ResizablePanelGroup direction="horizontal">
          {/* Left Sidebar */}
          {!leftPanelCollapsed && (
            <>
              <ResizablePanel defaultSize={4} minSize={4} maxSize={8}>
                <VideoEditorSidebar 
                  activeTab={activeTab}
                  onTabChange={setActiveTab}
                />
              </ResizablePanel>
              <ResizableHandle />
            </>
          )}

          {/* Main Content Area */}
          <ResizablePanel defaultSize={leftPanelCollapsed ? 80 : 76} minSize={50}>
            <ResizablePanelGroup direction="vertical">
              {/* Top Section */}
              <ResizablePanel defaultSize={70} minSize={40}>
                <ResizablePanelGroup direction="horizontal">
                  {/* Video Preview */}
                  <ResizablePanel defaultSize={rightPanelCollapsed ? 100 : 70} minSize={50}>
                    <div className="h-full flex flex-col">
                      {/* Tab Bar */}
                      <VSCodeTabs
                        tabs={tabs}
                        activeTabId={activeTab}
                        onTabChange={setActiveTab}
                        onTabClose={handleTabClose}
                        onNewTab={handleNewTab}
                      />
                      
                      {/* Tab Content */}
                      <TabContent activeTabId={activeTab} className="flex-1 p-4">
                        <VideoPreviewPanel
                          videoUrl={currentVideoUrl}
                          thumbnailUrl={currentThumbnailUrl}
                          isGenerating={isGenerating}
                          generationProgress={generationProgress}
                          onExport={handleExport}
                          onShare={() => toast.info('Share functionality coming soon')}
                          className="h-full"
                        />
                      </TabContent>
                    </div>
                  </ResizablePanel>

                  {/* Right Panel */}
                  {!rightPanelCollapsed && (
                    <>
                      <ResizableHandle />
                      <ResizablePanel defaultSize={30} minSize={20} maxSize={40}>
                        <TabContent activeTabId={activeTab}>
                          <TabPanel tabId="ai-generate" activeTabId={activeTab}>
                            <AIGenerationPanel
                              onGenerate={handleGenerateVideo}
                              isGenerating={isGenerating}
                              className="h-full"
                            />
                          </TabPanel>
                          <TabPanel tabId="media-library" activeTabId={activeTab}>
                            <div className="p-4">
                              <h3 className="font-medium mb-4">Media Library</h3>
                              <p className="text-sm text-muted-foreground">Media library content here</p>
                            </div>
                          </TabPanel>
                          <TabPanel tabId="settings" activeTabId={activeTab}>
                            <div className="p-4">
                              <h3 className="font-medium mb-4">Settings</h3>
                              <p className="text-sm text-muted-foreground">Settings content here</p>
                            </div>
                          </TabPanel>
                        </TabContent>
                      </ResizablePanel>
                    </>
                  )}
                </ResizablePanelGroup>
              </ResizablePanel>

              {/* Bottom Timeline */}
              <ResizableHandle />
              <ResizablePanel defaultSize={30} minSize={20} maxSize={50}>
                <VideoTimeline
                  clips={timelineClips}
                  currentTime={currentTime}
                  duration={duration}
                  isPlaying={isPlaying}
                  onTimeChange={handleTimeChange}
                  onPlayPause={handlePlayPause}
                  onClipSelect={handleClipSelect}
                  onClipDelete={handleClipDelete}
                  onClipDuplicate={handleClipDuplicate}
                  selectedClipId={selectedClipId}
                  className="h-full"
                />
              </ResizablePanel>
            </ResizablePanelGroup>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>
  );
}
