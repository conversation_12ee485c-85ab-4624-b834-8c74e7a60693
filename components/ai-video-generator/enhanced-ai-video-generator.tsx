'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Download,
  Share2,
  Settings,
  History,
  Layers,
  Upload
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { VideoSystem } from '@/lib/video';
import { AIVideoIntegration } from '@/lib/video/ai-video-integration';
import { 
  AIPromptData,
  AIGenerationSettings,
  GeneratedVideo,
  VideoClip,
  ProcessingQueueItem
} from '@/lib/video/types/video-types';
import { toast } from 'sonner';

// Import modular components
import { VideoEditorSidebar } from './video-editor-sidebar';
import { VideoPreviewPanel } from './video-preview-panel';
import { AIGenerationPanel } from './ai-generation-panel';
import { VideoTimeline } from './video-timeline';

interface EnhancedAIVideoGeneratorProps {
  onVideoGenerated?: (video: GeneratedVideo) => void;
  onVideoExported?: (url: string) => void;
  className?: string;
}

export function EnhancedAIVideoGenerator({
  onVideoGenerated,
  onVideoExported,
  className = ''
}: EnhancedAIVideoGeneratorProps) {
  // Core state
  const [activeTab, setActiveTab] = useState('ai-generate');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(30); // Default 30 seconds
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  
  // Video system integration
  const videoSystemRef = useRef<VideoSystem | null>(null);
  const aiIntegrationRef = useRef<AIVideoIntegration | null>(null);
  
  // Generated content state
  const [generatedVideos, setGeneratedVideos] = useState<GeneratedVideo[]>([]);
  const [timelineClips, setTimelineClips] = useState<VideoClip[]>([]);
  const [selectedClipId, setSelectedClipId] = useState<string | undefined>();
  const [currentVideoUrl, setCurrentVideoUrl] = useState<string | undefined>();
  const [currentThumbnailUrl, setCurrentThumbnailUrl] = useState<string | undefined>();

  // Initialize video system
  useEffect(() => {
    const initializeVideoSystem = async () => {
      try {
        const videoSystem = new VideoSystem({
          tempDirectory: '/tmp/video-system',
          maxFileSize: 1024 * 1024 * 1024, // 1GB
          supportedFormats: ['mp4', 'webm', 'mov'],
          webcodecs: {
            enabled: true,
            hardwareAcceleration: true
          },
          aiProviders: {
            runway: { apiKey: process.env.NEXT_PUBLIC_RUNWAY_API_KEY || '' },
            openai: { apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY || '' }
          }
        });

        const aiIntegration = new AIVideoIntegration(videoSystem);
        
        videoSystemRef.current = videoSystem;
        aiIntegrationRef.current = aiIntegration;

        // Set up event listeners
        aiIntegration.on('video:generated', (video: GeneratedVideo) => {
          setGeneratedVideos(prev => [...prev, video]);
          setCurrentVideoUrl(video.videoUrl);
          setCurrentThumbnailUrl(video.thumbnailUrl);
          onVideoGenerated?.(video);
          setIsGenerating(false);
          setGenerationProgress(0);
          toast.success('Video generated successfully!');
        });

        aiIntegration.on('generation:progress', (data: { progress: number }) => {
          setGenerationProgress(data.progress);
        });

        aiIntegration.on('generation:failed', (error: any) => {
          setIsGenerating(false);
          setGenerationProgress(0);
          toast.error(`Generation failed: ${error.message || 'Unknown error'}`);
        });

        aiIntegration.on('clip:added', (clip: VideoClip) => {
          setTimelineClips(prev => [...prev, clip]);
        });

      } catch (error) {
        console.error('Failed to initialize video system:', error);
        toast.error('Failed to initialize video system');
      }
    };

    initializeVideoSystem();

    return () => {
      // Cleanup
      if (aiIntegrationRef.current) {
        aiIntegrationRef.current.removeAllListeners();
      }
    };
  }, [onVideoGenerated]);

  // Generate AI video
  const handleGenerateVideo = useCallback(async (
    promptData: AIPromptData, 
    settings: AIGenerationSettings
  ) => {
    if (!aiIntegrationRef.current) {
      toast.error('Video system not initialized');
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      await aiIntegrationRef.current.generateVideo(promptData, settings);
    } catch (error) {
      console.error('Video generation error:', error);
      setIsGenerating(false);
      setGenerationProgress(0);
      toast.error('Failed to generate video');
    }
  }, []);

  // Timeline handlers
  const handleTimeChange = useCallback((time: number) => {
    setCurrentTime(time);
  }, []);

  const handlePlayPause = useCallback(() => {
    setIsPlaying(prev => !prev);
  }, []);

  const handleClipSelect = useCallback((clip: VideoClip) => {
    setSelectedClipId(clip.id);
    if (clip.source && typeof clip.source === 'string') {
      setCurrentVideoUrl(clip.source);
    }
  }, []);

  const handleClipDelete = useCallback((clipId: string) => {
    setTimelineClips(prev => prev.filter(clip => clip.id !== clipId));
    if (selectedClipId === clipId) {
      setSelectedClipId(undefined);
    }
    toast.success('Clip deleted');
  }, [selectedClipId]);

  const handleClipDuplicate = useCallback((clip: VideoClip) => {
    const newClip: VideoClip = {
      ...clip,
      id: `${clip.id}-copy-${Date.now()}`,
      position: clip.position + clip.duration
    };
    setTimelineClips(prev => [...prev, newClip]);
    toast.success('Clip duplicated');
  }, []);

  // Add generated video to timeline
  const handleAddToTimeline = useCallback(async (video: GeneratedVideo) => {
    if (!aiIntegrationRef.current) return;

    try {
      await aiIntegrationRef.current.addGeneratedVideoToTimeline(video);
      toast.success('Video added to timeline');
    } catch (error) {
      console.error('Failed to add video to timeline:', error);
      toast.error('Failed to add video to timeline');
    }
  }, []);

  // Export video
  const handleExport = useCallback(async () => {
    if (!aiIntegrationRef.current || timelineClips.length === 0) {
      toast.error('No clips to export');
      return;
    }

    try {
      const exportSettings = {
        resolution: { width: 1920, height: 1080, aspectRatio: '16:9' },
        fps: 30,
        bitrate: 5000,
        codec: 'h264' as const,
        format: 'mp4' as const,
        quality: 'high' as const
      };

      const videoIds = timelineClips.map(clip => clip.id);
      const exportUrls = await aiIntegrationRef.current.exportVideos(videoIds, exportSettings);
      
      if (exportUrls.length > 0) {
        onVideoExported?.(exportUrls[0]);
        toast.success('Video exported successfully!');
      }
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export video');
    }
  }, [timelineClips, onVideoExported]);

  return (
    <div className={cn("h-screen bg-background flex", className)}>
      {/* Left Sidebar */}
      <VideoEditorSidebar 
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Top Section - Video Preview and Controls */}
        <div className="flex-1 flex">
          {/* Video Preview */}
          <div className="flex-1 p-4">
            <VideoPreviewPanel
              videoUrl={currentVideoUrl}
              thumbnailUrl={currentThumbnailUrl}
              isGenerating={isGenerating}
              generationProgress={generationProgress}
              onExport={handleExport}
              onShare={() => toast.info('Share functionality coming soon')}
            />
          </div>

          {/* Right Panel - Conditional Content */}
          {activeTab === 'ai-generate' && (
            <AIGenerationPanel
              onGenerate={handleGenerateVideo}
              isGenerating={isGenerating}
            />
          )}

          {activeTab === 'media' && (
            <Card className="w-80 m-4">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="h-5 w-5" />
                  Media Library
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96">
                  <div className="space-y-2">
                    {generatedVideos.map((video) => (
                      <div
                        key={video.id}
                        className="p-2 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
                        onClick={() => {
                          setCurrentVideoUrl(video.videoUrl);
                          setCurrentThumbnailUrl(video.thumbnailUrl);
                        }}
                      >
                        <div className="aspect-video bg-muted rounded mb-2">
                          {video.thumbnailUrl && (
                            <img 
                              src={video.thumbnailUrl} 
                              alt="Video thumbnail"
                              className="w-full h-full object-cover rounded"
                            />
                          )}
                        </div>
                        <p className="text-xs font-medium truncate">{video.prompt}</p>
                        <div className="flex justify-between items-center mt-1">
                          <Badge variant="secondary" className="text-xs">
                            {video.settings?.duration}s
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAddToTimeline(video);
                            }}
                            className="h-6 text-xs"
                          >
                            Add to Timeline
                          </Button>
                        </div>
                      </div>
                    ))}
                    
                    {generatedVideos.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        <Upload className="h-8 w-8 mx-auto mb-2" />
                        <p className="text-sm">No videos generated yet</p>
                        <p className="text-xs">Use AI Generate to create videos</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Bottom Section - Timeline */}
        <div className="h-80 border-t">
          <VideoTimeline
            clips={timelineClips}
            currentTime={currentTime}
            duration={duration}
            isPlaying={isPlaying}
            onTimeChange={handleTimeChange}
            onPlayPause={handlePlayPause}
            onClipSelect={handleClipSelect}
            onClipDelete={handleClipDelete}
            onClipDuplicate={handleClipDuplicate}
            selectedClipId={selectedClipId}
          />
        </div>
      </div>
    </div>
  );
}
