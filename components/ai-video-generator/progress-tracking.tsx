'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  Loader2,
  Play,
  Pause,
  X,
  RotateCcw,
  Eye,
  Download
} from 'lucide-react';
import { ProcessingQueueItem } from '@/lib/video/types/video-types';
import { toast } from 'sonner';

interface ProgressTrackingProps {
  queueItems: ProcessingQueueItem[];
  onItemCancel?: (itemId: string) => void;
  onItemRetry?: (itemId: string) => void;
  onItemView?: (itemId: string) => void;
  onItemDownload?: (itemId: string) => void;
  className?: string;
}

interface ProgressStepProps {
  title: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  estimatedTime?: number;
  startTime?: Date;
  endTime?: Date;
  error?: string;
}

function ProgressStep({ 
  title, 
  status, 
  progress = 0, 
  estimatedTime, 
  startTime, 
  endTime,
  error 
}: ProgressStepProps) {
  const getStatusIcon = () => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-muted-foreground" />;
      case 'processing':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'pending': return 'text-muted-foreground';
      case 'processing': return 'text-blue-600';
      case 'completed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      default: return 'text-muted-foreground';
    }
  };

  const formatDuration = (start?: Date, end?: Date) => {
    if (!start) return null;
    const endTime = end || new Date();
    const duration = Math.floor((endTime.getTime() - start.getTime()) / 1000);
    
    if (duration < 60) return `${duration}s`;
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}m ${seconds}s`;
  };

  const formatEstimatedTime = (seconds?: number) => {
    if (!seconds) return null;
    if (seconds < 60) return `~${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `~${minutes}m ${remainingSeconds}s`;
  };

  return (
    <div className="flex items-center gap-3 p-3 rounded-lg border">
      <div className="flex-shrink-0">
        {getStatusIcon()}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <h4 className={`font-medium ${getStatusColor()}`}>{title}</h4>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {status === 'processing' && estimatedTime && (
              <span>{formatEstimatedTime(estimatedTime)}</span>
            )}
            {(status === 'completed' || status === 'failed') && startTime && (
              <span>{formatDuration(startTime, endTime)}</span>
            )}
          </div>
        </div>
        
        {status === 'processing' && (
          <div className="space-y-1">
            <Progress value={progress} className="h-2" />
            <p className="text-xs text-muted-foreground">
              {Math.round(progress)}% complete
            </p>
          </div>
        )}
        
        {status === 'failed' && error && (
          <p className="text-xs text-red-500 mt-1">{error}</p>
        )}
      </div>
    </div>
  );
}

export function ProgressTracking({
  queueItems,
  onItemCancel,
  onItemRetry,
  onItemView,
  onItemDownload,
  className = ''
}: ProgressTrackingProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const getOverallProgress = () => {
    if (queueItems.length === 0) return 0;
    const totalProgress = queueItems.reduce((sum, item) => sum + item.progress, 0);
    return totalProgress / queueItems.length;
  };

  const getStatusCounts = () => {
    return queueItems.reduce((counts, item) => {
      counts[item.status] = (counts[item.status] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);
  };

  const statusCounts = getStatusCounts();
  const overallProgress = getOverallProgress();

  const getItemSteps = (item: ProcessingQueueItem) => {
    const steps: ProgressStepProps[] = [];
    
    switch (item.type) {
      case 'upload':
        steps.push({
          title: 'Uploading file',
          status: item.status === 'completed' ? 'completed' : 
                 item.status === 'failed' ? 'failed' :
                 item.status === 'processing' ? 'processing' : 'pending',
          progress: item.progress,
          startTime: item.startTime,
          endTime: item.endTime,
          error: item.error
        });
        break;
        
      case 'ai-generation':
        steps.push(
          {
            title: 'Processing prompt',
            status: item.progress > 0 ? 'completed' : 'pending'
          },
          {
            title: 'Generating video',
            status: item.status === 'completed' ? 'completed' : 
                   item.status === 'failed' ? 'failed' :
                   item.status === 'processing' && item.progress > 20 ? 'processing' : 'pending',
            progress: Math.max(0, item.progress - 20),
            startTime: item.startTime,
            error: item.error
          },
          {
            title: 'Finalizing output',
            status: item.status === 'completed' ? 'completed' : 'pending',
            endTime: item.endTime
          }
        );
        break;
        
      case 'render':
        steps.push(
          {
            title: 'Preparing timeline',
            status: item.progress > 0 ? 'completed' : 'pending'
          },
          {
            title: 'Rendering video',
            status: item.status === 'completed' ? 'completed' : 
                   item.status === 'failed' ? 'failed' :
                   item.status === 'processing' ? 'processing' : 'pending',
            progress: item.progress,
            startTime: item.startTime,
            error: item.error
          },
          {
            title: 'Encoding output',
            status: item.status === 'completed' ? 'completed' : 'pending',
            endTime: item.endTime
          }
        );
        break;
        
      case 'export':
        steps.push({
          title: 'Exporting video',
          status: item.status === 'completed' ? 'completed' : 
                 item.status === 'failed' ? 'failed' :
                 item.status === 'processing' ? 'processing' : 'pending',
          progress: item.progress,
          startTime: item.startTime,
          endTime: item.endTime,
          error: item.error
        });
        break;
    }
    
    return steps;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500';
      case 'normal': return 'bg-blue-500';
      case 'low': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'completed': return 'default';
      case 'processing': return 'secondary';
      case 'failed': return 'destructive';
      case 'queued': return 'outline';
      case 'cancelled': return 'secondary';
      default: return 'outline';
    }
  };

  if (queueItems.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <Clock className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="font-medium mb-2">No active processes</h3>
          <p className="text-sm text-muted-foreground">
            Your processing queue is empty
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Overall Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Processing Queue</span>
            <div className="flex items-center gap-2">
              {Object.entries(statusCounts).map(([status, count]) => (
                <Badge key={status} variant={getStatusBadgeVariant(status)} className="text-xs">
                  {status}: {count}
                </Badge>
              ))}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Overall Progress</span>
              <span>{Math.round(overallProgress)}%</span>
            </div>
            <Progress value={overallProgress} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Queue Items */}
      <div className="space-y-3">
        {queueItems.map((item) => (
          <Card key={item.id}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`w-2 h-2 rounded-full ${getPriorityColor(item.priority)}`} />
                  <div>
                    <h4 className="font-medium">{item.title}</h4>
                    <p className="text-sm text-muted-foreground capitalize">
                      {item.type.replace('-', ' ')}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Badge variant={getStatusBadgeVariant(item.status)}>
                    {item.status}
                  </Badge>
                  
                  {item.status === 'processing' && onItemCancel && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onItemCancel(item.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                  
                  {item.status === 'failed' && onItemRetry && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onItemRetry(item.id)}
                    >
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                  )}
                  
                  {item.status === 'completed' && (
                    <div className="flex gap-1">
                      {onItemView && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onItemView(item.id)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      )}
                      {onItemDownload && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onItemDownload(item.id)}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              {/* Main Progress */}
              {item.status === 'processing' && (
                <div className="mb-4">
                  <Progress value={item.progress} className="h-2" />
                  <p className="text-xs text-muted-foreground mt-1">
                    {Math.round(item.progress)}% complete
                    {item.estimatedTime && ` • ~${Math.round(item.estimatedTime / 60)}m remaining`}
                  </p>
                </div>
              )}
              
              {/* Detailed Steps */}
              <div className="space-y-2">
                {getItemSteps(item).map((step, index) => (
                  <ProgressStep key={index} {...step} />
                ))}
              </div>
              
              {/* Error Details */}
              {item.status === 'failed' && item.error && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-700">{item.error}</p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
