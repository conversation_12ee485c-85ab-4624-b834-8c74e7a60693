# AI Video Generator Components

A comprehensive suite of React components for AI-powered video generation, editing, and management. Built with TypeScript, Tailwind CSS, and modern React patterns.

## 🚀 Features

- **AI Video Generation**: Create videos from text prompts using multiple AI models
- **Video Upload & Management**: Drag-and-drop file uploads with progress tracking
- **Advanced Prompt Interface**: Rich prompt input with style presets and suggestions
- **Video Preview Player**: Full-featured video player with controls and settings
- **Progress Tracking**: Real-time progress monitoring for all operations
- **Video Editing Tools**: Trim, crop, filters, and transformations
- **Thumbnail Generation**: Automatic and manual thumbnail creation
- **Export System**: Flexible export with multiple formats and quality settings
- **Settings Management**: Comprehensive settings panel with presets
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## 📦 Components

### Core Components

#### `AIVideoGenerator`
Main orchestrator component that provides the complete AI video generation workflow.

```tsx
import { AIVideoGenerator } from '@/components/ai-video-generator';

<AIVideoGenerator
  onVideoGenerated={(video) => console.log('Generated:', video)}
  onVideoExported={(url) => console.log('Exported:', url)}
/>
```

#### `VideoUploadInterface`
Drag-and-drop file upload component with validation and progress tracking.

```tsx
import { VideoUploadInterface } from '@/components/ai-video-generator';

<VideoUploadInterface
  onFilesUploaded={(files) => setUploadedFiles(files)}
  maxFileSize={1024 * 1024 * 1024} // 1GB
  maxFiles={10}
  acceptedFormats={['video/*', 'audio/*', 'image/*']}
/>
```

#### `AIPromptInput`
Advanced prompt input interface with style selection and settings.

```tsx
import { AIPromptInput } from '@/components/ai-video-generator';

<AIPromptInput
  onPromptChange={(prompt) => setPrompt(prompt)}
  onSettingsChange={(settings) => setSettings(settings)}
  initialPrompt={prompt}
  initialSettings={settings}
/>
```

#### `VideoPreviewPlayer`
Full-featured video player with standard controls and customization options.

```tsx
import { VideoPreviewPlayer } from '@/components/ai-video-generator';

<VideoPreviewPlayer
  videoUrl="/path/to/video.mp4"
  thumbnailUrl="/path/to/thumbnail.jpg"
  title="My Generated Video"
  duration={30}
  onTimeUpdate={(time) => console.log('Current time:', time)}
  controls={true}
  autoPlay={false}
/>
```

#### `ProgressTracking`
Real-time progress monitoring for video processing operations.

```tsx
import { ProgressTracking } from '@/components/ai-video-generator';

<ProgressTracking
  queueItems={processingQueue}
  onItemCancel={(id) => cancelItem(id)}
  onItemRetry={(id) => retryItem(id)}
  onItemView={(id) => viewItem(id)}
  onItemDownload={(id) => downloadItem(id)}
/>
```

### Advanced Components

#### `SettingsPanel`
Comprehensive settings management for generation and export parameters.

```tsx
import { SettingsPanel } from '@/components/ai-video-generator';

<SettingsPanel
  generationSettings={generationSettings}
  exportSettings={exportSettings}
  onGenerationSettingsChange={setGenerationSettings}
  onExportSettingsChange={setExportSettings}
  onSavePreset={(name, preset) => savePreset(name, preset)}
  onLoadPreset={(preset) => loadPreset(preset)}
/>
```

#### `ExportInterface`
Flexible export system with batch processing and format options.

```tsx
import { ExportInterface } from '@/components/ai-video-generator';

<ExportInterface
  videos={generatedVideos}
  exportSettings={exportSettings}
  onExportSettingsChange={setExportSettings}
  onExport={handleExport}
  onDownload={(url, filename) => downloadFile(url, filename)}
/>
```

#### `VideoEditingControls`
Video editing tools including trim, crop, filters, and transformations.

```tsx
import { VideoEditingControls } from '@/components/ai-video-generator';

<VideoEditingControls
  video={selectedVideo}
  editingState={editingState}
  onEditingStateChange={setEditingState}
  onApplyChanges={applyChanges}
  onResetChanges={resetChanges}
/>
```

#### `ThumbnailGenerator`
Automatic and manual thumbnail generation with selection interface.

```tsx
import { ThumbnailGenerator } from '@/components/ai-video-generator';

<ThumbnailGenerator
  video={selectedVideo}
  thumbnails={thumbnails}
  selectedThumbnailId={selectedThumbnailId}
  onThumbnailsGenerated={setThumbnails}
  onThumbnailSelected={setSelectedThumbnailId}
  onThumbnailDeleted={deleteThumbnail}
  onCustomThumbnailUploaded={addCustomThumbnail}
/>
```

## 🔧 Integration

### With Existing Video Editor

The components integrate seamlessly with the existing video editor system through the `AIVideoIntegration` class:

```tsx
import { VideoSystem } from '@/lib/video';
import { AIVideoIntegration } from '@/lib/video/ai-video-integration';

// Initialize video system
const videoSystem = new VideoSystem(config);

// Create integration layer
const aiIntegration = new AIVideoIntegration(videoSystem);

// Listen for events
aiIntegration.on('video:generated', (video) => {
  console.log('New video generated:', video);
});

aiIntegration.on('state:updated', (state) => {
  console.log('State updated:', state);
});

// Generate video
const requestId = await aiIntegration.generateVideo(prompt, settings);

// Add to timeline
const clip = await aiIntegration.addGeneratedVideoToTimeline(video);

// Export videos
const exportUrls = await aiIntegration.exportVideos(videoIds, exportSettings);
```

## 📋 Type Definitions

### Core Types

```typescript
interface AIVideoGeneratorState {
  currentStep: 'upload' | 'prompt' | 'generate' | 'preview' | 'export';
  uploadedFiles: UploadedFile[];
  prompt: AIPromptData;
  generationSettings: AIGenerationSettings;
  generatedVideos: GeneratedVideo[];
  processingQueue: ProcessingQueueItem[];
  exportSettings: ExportSettings;
}

interface GeneratedVideo {
  id: string;
  prompt: string;
  style: AIVideoStyle;
  settings: AIGenerationSettings;
  status: 'generating' | 'completed' | 'failed';
  progress: number;
  videoUrl?: string;
  thumbnailUrl?: string;
  duration?: number;
  createdAt: Date;
  completedAt?: Date;
  error?: string;
  metadata?: VideoMetadata;
}

interface AIPromptData {
  text: string;
  style: AIVideoStyle;
  mood: string;
  duration: number;
  aspectRatio: string;
  tags: string[];
  negativePrompt?: string;
  seed?: number;
  strength?: number;
}
```

## 🎨 Styling

Components use Tailwind CSS classes and are fully customizable. The design system includes:

- **Dark/Light Mode Support**: Automatic theme switching
- **Responsive Design**: Mobile-first approach
- **Consistent Spacing**: Using Tailwind's spacing scale
- **Accessible Colors**: WCAG compliant color combinations
- **Custom Components**: Built on shadcn/ui component library

### Customization

```tsx
// Custom styling example
<AIVideoGenerator 
  className="custom-video-generator"
  onVideoGenerated={handleVideoGenerated}
/>

// CSS
.custom-video-generator {
  @apply bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20;
}
```

## 🧪 Testing

### Component Testing

```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { AIPromptInput } from '@/components/ai-video-generator';

test('updates prompt text', () => {
  const handlePromptChange = jest.fn();
  
  render(
    <AIPromptInput 
      onPromptChange={handlePromptChange}
      onSettingsChange={jest.fn()}
    />
  );
  
  const textArea = screen.getByPlaceholderText(/describe the video/i);
  fireEvent.change(textArea, { target: { value: 'A beautiful sunset' } });
  
  expect(handlePromptChange).toHaveBeenCalledWith(
    expect.objectContaining({
      text: 'A beautiful sunset'
    })
  );
});
```

### Integration Testing

```tsx
import { AIVideoIntegration } from '@/lib/video/ai-video-integration';
import { VideoSystem } from '@/lib/video';

test('generates video successfully', async () => {
  const videoSystem = new VideoSystem({});
  const integration = new AIVideoIntegration(videoSystem);
  
  const prompt = {
    text: 'A serene mountain landscape',
    style: mockStyle,
    // ... other properties
  };
  
  const settings = {
    model: 'runway',
    duration: 5,
    // ... other properties
  };
  
  const requestId = await integration.generateVideo(prompt, settings);
  expect(requestId).toBeDefined();
});
```

## 📚 Examples

### Complete Implementation

```tsx
import React, { useState } from 'react';
import { 
  AIVideoGenerator,
  AIVideoGeneratorState,
  GeneratedVideo 
} from '@/components/ai-video-generator';

export function VideoGeneratorPage() {
  const [selectedVideo, setSelectedVideo] = useState<GeneratedVideo | null>(null);
  
  const handleVideoGenerated = (video: GeneratedVideo) => {
    setSelectedVideo(video);
    console.log('Generated video:', video);
  };
  
  const handleVideoExported = (videoUrl: string) => {
    console.log('Exported video URL:', videoUrl);
  };
  
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">AI Video Generator</h1>
      
      <AIVideoGenerator
        onVideoGenerated={handleVideoGenerated}
        onVideoExported={handleVideoExported}
      />
      
      {selectedVideo && (
        <div className="mt-6">
          <h2 className="text-xl font-semibold mb-4">Generated Video</h2>
          <p>Prompt: {selectedVideo.prompt}</p>
          <p>Duration: {selectedVideo.duration}s</p>
          <p>Style: {selectedVideo.style.name}</p>
        </div>
      )}
    </div>
  );
}
```

## 🔗 Dependencies

- React 18+
- TypeScript 5+
- Tailwind CSS 3+
- Lucide React (icons)
- React Dropzone (file uploads)
- Radix UI (base components)

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For questions and support, please open an issue on GitHub or contact the development team.
