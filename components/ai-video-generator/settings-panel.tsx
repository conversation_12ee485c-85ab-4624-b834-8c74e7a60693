'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  Video, 
  Palette, 
  Zap, 
  Download,
  Monitor,
  Smartphone,
  Square,
  RotateCcw,
  Save,
  Upload
} from 'lucide-react';
import { 
  AIGenerationSettings, 
  ExportSettings, 
  VideoResolution,
  VideoCodec,
  VideoFormat
} from '@/lib/video/types/video-types';
import { toast } from 'sonner';

interface SettingsPanelProps {
  generationSettings: AIGenerationSettings;
  exportSettings: ExportSettings;
  onGenerationSettingsChange: (settings: AIGenerationSettings) => void;
  onExportSettingsChange: (settings: ExportSettings) => void;
  onSavePreset?: (name: string, settings: any) => void;
  onLoadPreset?: (preset: any) => void;
  className?: string;
}

const RESOLUTION_PRESETS = [
  { name: '4K UHD', width: 3840, height: 2160, aspectRatio: '16:9' },
  { name: 'Full HD', width: 1920, height: 1080, aspectRatio: '16:9' },
  { name: 'HD', width: 1280, height: 720, aspectRatio: '16:9' },
  { name: 'Instagram Story', width: 1080, height: 1920, aspectRatio: '9:16' },
  { name: 'Instagram Post', width: 1080, height: 1080, aspectRatio: '1:1' },
  { name: 'YouTube Shorts', width: 1080, height: 1920, aspectRatio: '9:16' },
  { name: 'TikTok', width: 1080, height: 1920, aspectRatio: '9:16' },
  { name: 'Twitter', width: 1280, height: 720, aspectRatio: '16:9' }
];

const QUALITY_PRESETS = [
  { 
    name: 'Draft', 
    description: 'Fast generation, lower quality',
    iterations: 1,
    guidanceScale: 5,
    quality: 'draft' as const
  },
  { 
    name: 'Standard', 
    description: 'Balanced quality and speed',
    iterations: 1,
    guidanceScale: 7.5,
    quality: 'standard' as const
  },
  { 
    name: 'High', 
    description: 'High quality, slower generation',
    iterations: 2,
    guidanceScale: 10,
    quality: 'high' as const
  },
  { 
    name: 'Ultra', 
    description: 'Maximum quality, slowest',
    iterations: 3,
    guidanceScale: 12,
    quality: 'ultra' as const
  }
];

export function SettingsPanel({
  generationSettings,
  exportSettings,
  onGenerationSettingsChange,
  onExportSettingsChange,
  onSavePreset,
  onLoadPreset,
  className = ''
}: SettingsPanelProps) {
  const [presetName, setPresetName] = useState('');
  const [activeTab, setActiveTab] = useState('generation');

  const updateGenerationSettings = useCallback((updates: Partial<AIGenerationSettings>) => {
    onGenerationSettingsChange({ ...generationSettings, ...updates });
  }, [generationSettings, onGenerationSettingsChange]);

  const updateExportSettings = useCallback((updates: Partial<ExportSettings>) => {
    onExportSettingsChange({ ...exportSettings, ...updates });
  }, [exportSettings, onExportSettingsChange]);

  const applyResolutionPreset = useCallback((preset: typeof RESOLUTION_PRESETS[0]) => {
    const resolution: VideoResolution = {
      width: preset.width,
      height: preset.height,
      aspectRatio: preset.aspectRatio
    };
    
    updateGenerationSettings({ resolution });
    updateExportSettings({ resolution });
    toast.success(`Applied ${preset.name} resolution`);
  }, [updateGenerationSettings, updateExportSettings]);

  const applyQualityPreset = useCallback((preset: typeof QUALITY_PRESETS[0]) => {
    updateGenerationSettings({
      quality: preset.quality,
      iterations: preset.iterations,
      guidanceScale: preset.guidanceScale
    });
    toast.success(`Applied ${preset.name} quality preset`);
  }, [updateGenerationSettings]);

  const resetToDefaults = useCallback(() => {
    updateGenerationSettings({
      model: 'runway',
      resolution: { width: 1920, height: 1080, aspectRatio: '16:9' },
      fps: 30,
      duration: 5,
      quality: 'high',
      iterations: 1,
      guidanceScale: 7.5,
      seed: undefined
    });
    
    updateExportSettings({
      format: 'mp4',
      resolution: { width: 1920, height: 1080, aspectRatio: '16:9' },
      fps: 30,
      bitrate: 5000000,
      codec: 'h264',
      quality: 'high',
      includeAudio: true,
      watermark: {
        enabled: false,
        position: 'bottom-right',
        opacity: 0.7
      }
    });
    
    toast.success('Settings reset to defaults');
  }, [updateGenerationSettings, updateExportSettings]);

  const savePreset = useCallback(() => {
    if (!presetName.trim()) {
      toast.error('Please enter a preset name');
      return;
    }
    
    const preset = {
      name: presetName,
      generationSettings,
      exportSettings,
      createdAt: new Date()
    };
    
    onSavePreset?.(presetName, preset);
    setPresetName('');
    toast.success(`Preset "${presetName}" saved`);
  }, [presetName, generationSettings, exportSettings, onSavePreset]);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Video Settings
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="generation">
              <Zap className="h-4 w-4 mr-2" />
              Generation
            </TabsTrigger>
            <TabsTrigger value="video">
              <Video className="h-4 w-4 mr-2" />
              Video
            </TabsTrigger>
            <TabsTrigger value="export">
              <Download className="h-4 w-4 mr-2" />
              Export
            </TabsTrigger>
            <TabsTrigger value="presets">
              <Save className="h-4 w-4 mr-2" />
              Presets
            </TabsTrigger>
          </TabsList>

          <TabsContent value="generation" className="space-y-6">
            {/* AI Model Selection */}
            <div>
              <Label className="text-base font-medium">AI Model</Label>
              <p className="text-sm text-muted-foreground mb-3">
                Choose the AI model for video generation
              </p>
              <Select 
                value={generationSettings.model} 
                onValueChange={(value: any) => updateGenerationSettings({ model: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="runway">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">Pro</Badge>
                      Runway ML - High quality, versatile
                    </div>
                  </SelectItem>
                  <SelectItem value="pika">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">Standard</Badge>
                      Pika Labs - Fast generation
                    </div>
                  </SelectItem>
                  <SelectItem value="stability">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">Pro</Badge>
                      Stability AI - Artistic styles
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Separator />

            {/* Quality Presets */}
            <div>
              <Label className="text-base font-medium">Quality Presets</Label>
              <p className="text-sm text-muted-foreground mb-3">
                Quick quality settings for different use cases
              </p>
              <div className="grid grid-cols-2 gap-2">
                {QUALITY_PRESETS.map((preset) => (
                  <Button
                    key={preset.name}
                    variant={generationSettings.quality === preset.quality ? "default" : "outline"}
                    size="sm"
                    onClick={() => applyQualityPreset(preset)}
                    className="h-auto p-3 flex flex-col items-start"
                  >
                    <span className="font-medium">{preset.name}</span>
                    <span className="text-xs opacity-70">{preset.description}</span>
                  </Button>
                ))}
              </div>
            </div>

            <Separator />

            {/* Advanced Generation Settings */}
            <div className="space-y-4">
              <Label className="text-base font-medium">Advanced Settings</Label>
              
              <div>
                <Label>Guidance Scale: {generationSettings.guidanceScale}</Label>
                <p className="text-xs text-muted-foreground mb-2">
                  Higher values follow the prompt more closely
                </p>
                <Slider
                  value={[generationSettings.guidanceScale]}
                  onValueChange={([value]) => updateGenerationSettings({ guidanceScale: value })}
                  min={1}
                  max={20}
                  step={0.5}
                />
              </div>

              <div>
                <Label>Iterations: {generationSettings.iterations}</Label>
                <p className="text-xs text-muted-foreground mb-2">
                  More iterations = higher quality but slower generation
                </p>
                <Slider
                  value={[generationSettings.iterations]}
                  onValueChange={([value]) => updateGenerationSettings({ iterations: value })}
                  min={1}
                  max={5}
                  step={1}
                />
              </div>

              <div>
                <Label>Seed (optional)</Label>
                <p className="text-xs text-muted-foreground mb-2">
                  Use the same seed for reproducible results
                </p>
                <Input
                  type="number"
                  placeholder="Random"
                  value={generationSettings.seed || ''}
                  onChange={(e) => updateGenerationSettings({ 
                    seed: e.target.value ? parseInt(e.target.value) : undefined 
                  })}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="video" className="space-y-6">
            {/* Resolution Presets */}
            <div>
              <Label className="text-base font-medium">Resolution Presets</Label>
              <p className="text-sm text-muted-foreground mb-3">
                Common video resolutions for different platforms
              </p>
              <div className="grid grid-cols-2 gap-2">
                {RESOLUTION_PRESETS.map((preset) => (
                  <Button
                    key={preset.name}
                    variant="outline"
                    size="sm"
                    onClick={() => applyResolutionPreset(preset)}
                    className="h-auto p-3 flex flex-col items-start"
                  >
                    <div className="flex items-center gap-2 mb-1">
                      {preset.aspectRatio === '16:9' && <Monitor className="h-3 w-3" />}
                      {preset.aspectRatio === '9:16' && <Smartphone className="h-3 w-3" />}
                      {preset.aspectRatio === '1:1' && <Square className="h-3 w-3" />}
                      <span className="font-medium text-xs">{preset.name}</span>
                    </div>
                    <span className="text-xs opacity-70">
                      {preset.width}×{preset.height}
                    </span>
                  </Button>
                ))}
              </div>
            </div>

            <Separator />

            {/* Custom Resolution */}
            <div>
              <Label className="text-base font-medium">Custom Resolution</Label>
              <div className="grid grid-cols-2 gap-3 mt-3">
                <div>
                  <Label>Width</Label>
                  <Input
                    type="number"
                    value={generationSettings.resolution.width}
                    onChange={(e) => updateGenerationSettings({
                      resolution: {
                        ...generationSettings.resolution,
                        width: parseInt(e.target.value) || 1920
                      }
                    })}
                  />
                </div>
                <div>
                  <Label>Height</Label>
                  <Input
                    type="number"
                    value={generationSettings.resolution.height}
                    onChange={(e) => updateGenerationSettings({
                      resolution: {
                        ...generationSettings.resolution,
                        height: parseInt(e.target.value) || 1080
                      }
                    })}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Frame Rate and Duration */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Frame Rate: {generationSettings.fps} fps</Label>
                <Slider
                  value={[generationSettings.fps]}
                  onValueChange={([value]) => updateGenerationSettings({ fps: value })}
                  min={15}
                  max={60}
                  step={15}
                  className="mt-2"
                />
              </div>
              
              <div>
                <Label>Duration: {generationSettings.duration}s</Label>
                <Slider
                  value={[generationSettings.duration]}
                  onValueChange={([value]) => updateGenerationSettings({ duration: value })}
                  min={1}
                  max={30}
                  step={1}
                  className="mt-2"
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="export" className="space-y-6">
            {/* Export Format */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Format</Label>
                <Select 
                  value={exportSettings.format} 
                  onValueChange={(value: VideoFormat) => updateExportSettings({ format: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mp4">MP4 (Recommended)</SelectItem>
                    <SelectItem value="webm">WebM</SelectItem>
                    <SelectItem value="mov">MOV</SelectItem>
                    <SelectItem value="avi">AVI</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Codec</Label>
                <Select 
                  value={exportSettings.codec} 
                  onValueChange={(value: VideoCodec) => updateExportSettings({ codec: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="h264">H.264 (Compatible)</SelectItem>
                    <SelectItem value="h265">H.265 (Efficient)</SelectItem>
                    <SelectItem value="vp9">VP9 (Web)</SelectItem>
                    <SelectItem value="av1">AV1 (Future)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Separator />

            {/* Quality and Bitrate */}
            <div>
              <Label>Bitrate: {(exportSettings.bitrate / 1000000).toFixed(1)} Mbps</Label>
              <p className="text-xs text-muted-foreground mb-2">
                Higher bitrate = better quality but larger file size
              </p>
              <Slider
                value={[exportSettings.bitrate]}
                onValueChange={([value]) => updateExportSettings({ bitrate: value })}
                min={1000000}
                max={50000000}
                step={1000000}
              />
            </div>

            <Separator />

            {/* Audio Settings */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label>Include Audio</Label>
                <Switch
                  checked={exportSettings.includeAudio}
                  onCheckedChange={(checked) => updateExportSettings({ includeAudio: checked })}
                />
              </div>
            </div>

            <Separator />

            {/* Watermark Settings */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label>Add Watermark</Label>
                <Switch
                  checked={exportSettings.watermark?.enabled || false}
                  onCheckedChange={(checked) => updateExportSettings({
                    watermark: { ...exportSettings.watermark, enabled: checked }
                  })}
                />
              </div>

              {exportSettings.watermark?.enabled && (
                <div className="space-y-3 pl-4 border-l-2 border-muted">
                  <div>
                    <Label>Watermark Text</Label>
                    <Input
                      placeholder="Your watermark text"
                      value={exportSettings.watermark.text || ''}
                      onChange={(e) => updateExportSettings({
                        watermark: { ...exportSettings.watermark, text: e.target.value }
                      })}
                    />
                  </div>

                  <div>
                    <Label>Position</Label>
                    <Select 
                      value={exportSettings.watermark.position} 
                      onValueChange={(value: any) => updateExportSettings({
                        watermark: { ...exportSettings.watermark, position: value }
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="top-left">Top Left</SelectItem>
                        <SelectItem value="top-right">Top Right</SelectItem>
                        <SelectItem value="bottom-left">Bottom Left</SelectItem>
                        <SelectItem value="bottom-right">Bottom Right</SelectItem>
                        <SelectItem value="center">Center</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Opacity: {Math.round((exportSettings.watermark.opacity || 0.7) * 100)}%</Label>
                    <Slider
                      value={[exportSettings.watermark.opacity || 0.7]}
                      onValueChange={([value]) => updateExportSettings({
                        watermark: { ...exportSettings.watermark, opacity: value }
                      })}
                      min={0.1}
                      max={1}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="presets" className="space-y-6">
            {/* Save Preset */}
            <div>
              <Label className="text-base font-medium">Save Current Settings</Label>
              <p className="text-sm text-muted-foreground mb-3">
                Save your current settings as a preset for future use
              </p>
              <div className="flex gap-2">
                <Input
                  placeholder="Preset name"
                  value={presetName}
                  onChange={(e) => setPresetName(e.target.value)}
                />
                <Button onClick={savePreset} disabled={!presetName.trim()}>
                  <Save className="h-4 w-4 mr-2" />
                  Save
                </Button>
              </div>
            </div>

            <Separator />

            {/* Reset to Defaults */}
            <div>
              <Label className="text-base font-medium">Reset Settings</Label>
              <p className="text-sm text-muted-foreground mb-3">
                Reset all settings to their default values
              </p>
              <Button variant="outline" onClick={resetToDefaults}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset to Defaults
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
