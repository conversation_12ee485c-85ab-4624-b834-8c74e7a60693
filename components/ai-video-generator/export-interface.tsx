'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Download, 
  FileVideo, 
  Settings, 
  CheckCircle,
  Clock,
  AlertCircle,
  X,
  Play,
  Folder,
  Share,
  Copy,
  Cloud,
  HardDrive
} from 'lucide-react';
import { 
  ExportSettings, 
  GeneratedVideo,
  VideoFormat,
  VideoCodec
} from '@/lib/video/types/video-types';
import { toast } from 'sonner';

interface ExportJob {
  id: string;
  videoId: string;
  videoTitle: string;
  format: VideoFormat;
  quality: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  outputUrl?: string;
  fileSize?: number;
  startTime?: Date;
  endTime?: Date;
  error?: string;
}

interface ExportInterfaceProps {
  videos: GeneratedVideo[];
  exportSettings: ExportSettings;
  onExportSettingsChange: (settings: ExportSettings) => void;
  onExport: (videoIds: string[], settings: ExportSettings) => Promise<void>;
  onDownload?: (url: string, filename: string) => void;
  className?: string;
}

export function ExportInterface({
  videos,
  exportSettings,
  onExportSettingsChange,
  onExport,
  onDownload,
  className = ''
}: ExportInterfaceProps) {
  const [selectedVideos, setSelectedVideos] = useState<Set<string>>(new Set());
  const [exportJobs, setExportJobs] = useState<ExportJob[]>([]);
  const [isExporting, setIsExporting] = useState(false);
  const [exportDestination, setExportDestination] = useState<'local' | 'cloud'>('local');

  const updateExportSettings = useCallback((updates: Partial<ExportSettings>) => {
    onExportSettingsChange({ ...exportSettings, ...updates });
  }, [exportSettings, onExportSettingsChange]);

  const toggleVideoSelection = useCallback((videoId: string) => {
    const newSelection = new Set(selectedVideos);
    if (newSelection.has(videoId)) {
      newSelection.delete(videoId);
    } else {
      newSelection.add(videoId);
    }
    setSelectedVideos(newSelection);
  }, [selectedVideos]);

  const selectAllVideos = useCallback(() => {
    setSelectedVideos(new Set(videos.map(v => v.id)));
  }, [videos]);

  const clearSelection = useCallback(() => {
    setSelectedVideos(new Set());
  }, []);

  const startExport = useCallback(async () => {
    if (selectedVideos.size === 0) {
      toast.error('Please select at least one video to export');
      return;
    }

    setIsExporting(true);

    // Create export jobs
    const newJobs: ExportJob[] = Array.from(selectedVideos).map(videoId => {
      const video = videos.find(v => v.id === videoId);
      return {
        id: `export-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        videoId,
        videoTitle: video?.prompt.slice(0, 50) || 'Untitled',
        format: exportSettings.format,
        quality: exportSettings.quality,
        status: 'pending' as const,
        progress: 0,
        startTime: new Date()
      };
    });

    setExportJobs(prev => [...prev, ...newJobs]);

    try {
      // Start export process
      await onExport(Array.from(selectedVideos), exportSettings);
      
      // Simulate export progress for each job
      for (const job of newJobs) {
        simulateExportProgress(job.id);
      }

      toast.success(`Started exporting ${selectedVideos.size} video(s)`);
    } catch (error) {
      toast.error('Failed to start export process');
      setIsExporting(false);
    }
  }, [selectedVideos, videos, exportSettings, onExport]);

  const simulateExportProgress = useCallback(async (jobId: string) => {
    // Update job status to processing
    setExportJobs(prev => prev.map(job =>
      job.id === jobId
        ? { ...job, status: 'processing' as const }
        : job
    ));

    // Simulate progress
    for (let progress = 0; progress <= 100; progress += 10) {
      await new Promise(resolve => setTimeout(resolve, 500));

      setExportJobs(prev => prev.map(job =>
        job.id === jobId
          ? { ...job, progress }
          : job
      ));
    }

    // Complete the job
    setExportJobs(prev => prev.map(job =>
      job.id === jobId
        ? {
            ...job,
            status: 'completed' as const,
            progress: 100,
            endTime: new Date(),
            outputUrl: '/exported-video.mp4',
            fileSize: Math.floor(Math.random() * 100) + 50 // MB
          }
        : job
    ));

    // Check if all jobs are complete
    setTimeout(() => {
      setExportJobs(current => {
        const allJobsComplete = current.every(job =>
          job.status === 'completed' || job.status === 'failed'
        );

        if (allJobsComplete) {
          setIsExporting(false);
        }

        return current;
      });
    }, 100);
  }, []);

  const downloadVideo = useCallback((job: ExportJob) => {
    if (job.outputUrl) {
      const filename = `${job.videoTitle.replace(/[^a-zA-Z0-9]/g, '_')}.${job.format}`;
      onDownload?.(job.outputUrl, filename);
      toast.success(`Downloading ${filename}`);
    }
  }, [onDownload]);

  const removeJob = useCallback((jobId: string) => {
    setExportJobs(prev => prev.filter(job => job.id !== jobId));
  }, []);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'processing': return <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed': return <AlertCircle className="h-4 w-4 text-red-500" />;
      default: return null;
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'completed': return 'default';
      case 'processing': return 'secondary';
      case 'failed': return 'destructive';
      default: return 'outline';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Export Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Export Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label>Format</Label>
              <Select 
                value={exportSettings.format} 
                onValueChange={(value: VideoFormat) => updateExportSettings({ format: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="mp4">MP4 (Recommended)</SelectItem>
                  <SelectItem value="webm">WebM</SelectItem>
                  <SelectItem value="mov">MOV</SelectItem>
                  <SelectItem value="avi">AVI</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Quality</Label>
              <Select 
                value={exportSettings.quality} 
                onValueChange={(value: any) => updateExportSettings({ quality: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low (Fast)</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="ultra">Ultra (Slow)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Destination</Label>
              <Select 
                value={exportDestination} 
                onValueChange={(value: any) => setExportDestination(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="local">
                    <div className="flex items-center gap-2">
                      <HardDrive className="h-4 w-4" />
                      Local Download
                    </div>
                  </SelectItem>
                  <SelectItem value="cloud">
                    <div className="flex items-center gap-2">
                      <Cloud className="h-4 w-4" />
                      Cloud Storage
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Video Selection */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileVideo className="h-5 w-5" />
              Select Videos to Export
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={selectAllVideos}>
                Select All
              </Button>
              <Button variant="outline" size="sm" onClick={clearSelection}>
                Clear
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {videos.length === 0 ? (
            <div className="text-center py-8">
              <FileVideo className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="font-medium mb-2">No videos available</h3>
              <p className="text-sm text-muted-foreground">
                Generate some videos first to export them
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {videos.map((video) => (
                <div key={video.id} className="flex items-center gap-3 p-3 border rounded-lg">
                  <Checkbox
                    checked={selectedVideos.has(video.id)}
                    onCheckedChange={() => toggleVideoSelection(video.id)}
                  />
                  
                  <div className="flex-shrink-0">
                    {video.thumbnailUrl ? (
                      <img 
                        src={video.thumbnailUrl} 
                        alt={video.prompt}
                        className="w-16 h-9 object-cover rounded"
                      />
                    ) : (
                      <div className="w-16 h-9 bg-muted rounded flex items-center justify-center">
                        <Play className="h-4 w-4 text-muted-foreground" />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium truncate">{video.prompt.slice(0, 60)}...</h4>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary" className="text-xs">
                        {video.style.name}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {video.duration}s
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {video.settings.resolution.width}×{video.settings.resolution.height}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Export Actions */}
      {selectedVideos.size > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">
                  {selectedVideos.size} video(s) selected
                </p>
                <p className="text-sm text-muted-foreground">
                  Export as {exportSettings.format.toUpperCase()} • {exportSettings.quality} quality
                </p>
              </div>
              
              <Button 
                onClick={startExport}
                disabled={isExporting}
                size="lg"
              >
                <Download className="h-4 w-4 mr-2" />
                {isExporting ? 'Exporting...' : 'Start Export'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Export Queue */}
      {exportJobs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Export Queue
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {exportJobs.map((job) => (
              <div key={job.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(job.status)}
                    <div>
                      <h4 className="font-medium">{job.videoTitle}</h4>
                      <p className="text-sm text-muted-foreground">
                        {job.format.toUpperCase()} • {job.quality} quality
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge variant={getStatusBadgeVariant(job.status)}>
                      {job.status}
                    </Badge>
                    
                    {job.status === 'completed' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => downloadVideo(job)}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    )}
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeJob(job.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                {job.status === 'processing' && (
                  <div className="space-y-1">
                    <Progress value={job.progress} className="h-2" />
                    <p className="text-xs text-muted-foreground">
                      {job.progress}% complete
                    </p>
                  </div>
                )}
                
                {job.status === 'completed' && job.fileSize && (
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>File size: {formatFileSize(job.fileSize * 1024 * 1024)}</span>
                    {job.startTime && job.endTime && (
                      <span>
                        Completed in {Math.round((job.endTime.getTime() - job.startTime.getTime()) / 1000)}s
                      </span>
                    )}
                  </div>
                )}
                
                {job.status === 'failed' && job.error && (
                  <p className="text-sm text-red-500 mt-2">{job.error}</p>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
