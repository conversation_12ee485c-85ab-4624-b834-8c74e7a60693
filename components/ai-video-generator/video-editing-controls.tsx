'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Scissors, 
  Crop, 
  Palette, 
  RotateCw,
  Move,
  ZoomIn,
  ZoomOut,
  FlipHorizontal,
  FlipVertical,
  Undo,
  Redo,
  Eye,
  EyeOff,
  RotateCcw
} from 'lucide-react';
import { 
  VideoEditingState, 
  VideoFilter, 
  VideoTransformation,
  GeneratedVideo
} from '@/lib/video/types/video-types';
import { toast } from 'sonner';

interface VideoEditingControlsProps {
  video: GeneratedVideo;
  editingState: VideoEditingState;
  onEditingStateChange: (state: VideoEditingState) => void;
  onApplyChanges: () => void;
  onResetChanges: () => void;
  className?: string;
}

const FILTER_PRESETS: Omit<VideoFilter, 'id'>[] = [
  { type: 'brightness', name: 'Brightness', value: 0, enabled: false },
  { type: 'contrast', name: 'Contrast', value: 0, enabled: false },
  { type: 'saturation', name: 'Saturation', value: 0, enabled: false },
  { type: 'blur', name: 'Blur', value: 0, enabled: false },
  { type: 'sharpen', name: 'Sharpen', value: 0, enabled: false },
  { type: 'vintage', name: 'Vintage', value: 50, enabled: false },
  { type: 'bw', name: 'Black & White', value: 100, enabled: false }
];

export function VideoEditingControls({
  video,
  editingState,
  onEditingStateChange,
  onApplyChanges,
  onResetChanges,
  className = ''
}: VideoEditingControlsProps) {
  const [activeTab, setActiveTab] = useState('trim');
  const [cropMode, setCropMode] = useState(false);
  const [previewChanges, setPreviewChanges] = useState(true);

  const updateEditingState = useCallback((updates: Partial<VideoEditingState>) => {
    onEditingStateChange({ ...editingState, ...updates });
  }, [editingState, onEditingStateChange]);

  // Trim Controls
  const handleTrimChange = useCallback((start: number, end: number) => {
    updateEditingState({
      trimStart: start,
      trimEnd: end
    });
  }, [updateEditingState]);

  // Crop Controls
  const handleCropChange = useCallback((cropArea: VideoEditingState['cropArea']) => {
    updateEditingState({ cropArea });
  }, [updateEditingState]);

  const resetCrop = useCallback(() => {
    updateEditingState({ cropArea: undefined });
  }, [updateEditingState]);

  // Filter Controls
  const updateFilter = useCallback((filterId: string, updates: Partial<VideoFilter>) => {
    const updatedFilters = editingState.filters.map(filter =>
      filter.id === filterId ? { ...filter, ...updates } : filter
    );
    updateEditingState({ filters: updatedFilters });
  }, [editingState.filters, updateEditingState]);

  const addFilter = useCallback((filterType: VideoFilter['type']) => {
    const preset = FILTER_PRESETS.find(f => f.type === filterType);
    if (!preset) return;

    const newFilter: VideoFilter = {
      ...preset,
      id: `filter-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      enabled: true
    };

    updateEditingState({
      filters: [...editingState.filters, newFilter]
    });
  }, [editingState.filters, updateEditingState]);

  const removeFilter = useCallback((filterId: string) => {
    updateEditingState({
      filters: editingState.filters.filter(f => f.id !== filterId)
    });
  }, [editingState.filters, updateEditingState]);

  // Transformation Controls
  const updateTransformation = useCallback((transformId: string, updates: Partial<VideoTransformation>) => {
    const updatedTransforms = editingState.transformations.map(transform =>
      transform.id === transformId ? { ...transform, ...updates } : transform
    );
    updateEditingState({ transformations: updatedTransforms });
  }, [editingState.transformations, updateEditingState]);

  const addTransformation = useCallback((type: VideoTransformation['type'], values: Record<string, number>) => {
    const newTransform: VideoTransformation = {
      id: `transform-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      name: type.charAt(0).toUpperCase() + type.slice(1),
      values,
      enabled: true
    };

    updateEditingState({
      transformations: [...editingState.transformations, newTransform]
    });
  }, [editingState.transformations, updateEditingState]);

  const removeTransformation = useCallback((transformId: string) => {
    updateEditingState({
      transformations: editingState.transformations.filter(t => t.id !== transformId)
    });
  }, [editingState.transformations, updateEditingState]);

  // Quick transformation actions
  const rotateClockwise = useCallback(() => {
    const existingRotate = editingState.transformations.find(t => t.type === 'rotate');
    if (existingRotate) {
      updateTransformation(existingRotate.id, {
        values: { ...existingRotate.values, angle: (existingRotate.values.angle || 0) + 90 }
      });
    } else {
      addTransformation('rotate', { angle: 90 });
    }
  }, [editingState.transformations, updateTransformation, addTransformation]);

  const flipHorizontal = useCallback(() => {
    const existingFlip = editingState.transformations.find(t => t.type === 'flip');
    if (existingFlip) {
      updateTransformation(existingFlip.id, {
        values: { ...existingFlip.values, horizontal: existingFlip.values.horizontal ? 0 : 1 }
      });
    } else {
      addTransformation('flip', { horizontal: 1, vertical: 0 });
    }
  }, [editingState.transformations, updateTransformation, addTransformation]);

  const flipVertical = useCallback(() => {
    const existingFlip = editingState.transformations.find(t => t.type === 'flip');
    if (existingFlip) {
      updateTransformation(existingFlip.id, {
        values: { ...existingFlip.values, vertical: existingFlip.values.vertical ? 0 : 1 }
      });
    } else {
      addTransformation('flip', { horizontal: 0, vertical: 1 });
    }
  }, [editingState.transformations, updateTransformation, addTransformation]);

  const hasChanges = editingState.trimStart !== undefined || 
                    editingState.trimEnd !== undefined ||
                    editingState.cropArea !== undefined ||
                    editingState.filters.length > 0 ||
                    editingState.transformations.length > 0;

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Scissors className="h-5 w-5" />
            Video Editing
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <Label htmlFor="preview-changes" className="text-sm">Live Preview</Label>
              <Switch
                id="preview-changes"
                checked={previewChanges}
                onCheckedChange={setPreviewChanges}
              />
            </div>
            
            {hasChanges && (
              <>
                <Button variant="outline" size="sm" onClick={onResetChanges}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reset
                </Button>
                <Button size="sm" onClick={onApplyChanges}>
                  Apply Changes
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="trim">
              <Scissors className="h-4 w-4 mr-2" />
              Trim
            </TabsTrigger>
            <TabsTrigger value="crop">
              <Crop className="h-4 w-4 mr-2" />
              Crop
            </TabsTrigger>
            <TabsTrigger value="filters">
              <Palette className="h-4 w-4 mr-2" />
              Filters
            </TabsTrigger>
            <TabsTrigger value="transform">
              <RotateCw className="h-4 w-4 mr-2" />
              Transform
            </TabsTrigger>
          </TabsList>

          <TabsContent value="trim" className="space-y-4">
            <div>
              <Label className="text-base font-medium">Trim Video</Label>
              <p className="text-sm text-muted-foreground mb-4">
                Adjust the start and end points of your video
              </p>
              
              <div className="space-y-4">
                <div>
                  <Label>Start Time: {editingState.trimStart?.toFixed(1) || 0}s</Label>
                  <Slider
                    value={[editingState.trimStart || 0]}
                    onValueChange={([value]) => handleTrimChange(value, editingState.trimEnd || video.duration || 5)}
                    max={video.duration || 5}
                    step={0.1}
                    className="mt-2"
                  />
                </div>
                
                <div>
                  <Label>End Time: {editingState.trimEnd?.toFixed(1) || video.duration || 5}s</Label>
                  <Slider
                    value={[editingState.trimEnd || video.duration || 5]}
                    onValueChange={([value]) => handleTrimChange(editingState.trimStart || 0, value)}
                    max={video.duration || 5}
                    step={0.1}
                    className="mt-2"
                  />
                </div>
                
                <div className="text-sm text-muted-foreground">
                  Duration: {((editingState.trimEnd || video.duration || 5) - (editingState.trimStart || 0)).toFixed(1)}s
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="crop" className="space-y-4">
            <div>
              <Label className="text-base font-medium">Crop Video</Label>
              <p className="text-sm text-muted-foreground mb-4">
                Adjust the visible area of your video
              </p>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>X Position: {editingState.cropArea?.x || 0}%</Label>
                    <Slider
                      value={[editingState.cropArea?.x || 0]}
                      onValueChange={([value]) => handleCropChange({
                        ...editingState.cropArea,
                        x: value,
                        y: editingState.cropArea?.y || 0,
                        width: editingState.cropArea?.width || 100,
                        height: editingState.cropArea?.height || 100
                      })}
                      max={100}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  
                  <div>
                    <Label>Y Position: {editingState.cropArea?.y || 0}%</Label>
                    <Slider
                      value={[editingState.cropArea?.y || 0]}
                      onValueChange={([value]) => handleCropChange({
                        ...editingState.cropArea,
                        x: editingState.cropArea?.x || 0,
                        y: value,
                        width: editingState.cropArea?.width || 100,
                        height: editingState.cropArea?.height || 100
                      })}
                      max={100}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  
                  <div>
                    <Label>Width: {editingState.cropArea?.width || 100}%</Label>
                    <Slider
                      value={[editingState.cropArea?.width || 100]}
                      onValueChange={([value]) => handleCropChange({
                        ...editingState.cropArea,
                        x: editingState.cropArea?.x || 0,
                        y: editingState.cropArea?.y || 0,
                        width: value,
                        height: editingState.cropArea?.height || 100
                      })}
                      min={10}
                      max={100}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  
                  <div>
                    <Label>Height: {editingState.cropArea?.height || 100}%</Label>
                    <Slider
                      value={[editingState.cropArea?.height || 100]}
                      onValueChange={([value]) => handleCropChange({
                        ...editingState.cropArea,
                        x: editingState.cropArea?.x || 0,
                        y: editingState.cropArea?.y || 0,
                        width: editingState.cropArea?.width || 100,
                        height: value
                      })}
                      min={10}
                      max={100}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                </div>
                
                <Button variant="outline" onClick={resetCrop}>
                  Reset Crop
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="filters" className="space-y-4">
            <div>
              <Label className="text-base font-medium">Video Filters</Label>
              <p className="text-sm text-muted-foreground mb-4">
                Apply color and visual effects to your video
              </p>
              
              {/* Add Filter Buttons */}
              <div className="grid grid-cols-3 gap-2 mb-4">
                {FILTER_PRESETS.map((preset) => (
                  <Button
                    key={preset.type}
                    variant="outline"
                    size="sm"
                    onClick={() => addFilter(preset.type)}
                    disabled={editingState.filters.some(f => f.type === preset.type)}
                  >
                    {preset.name}
                  </Button>
                ))}
              </div>
              
              <Separator />
              
              {/* Active Filters */}
              <div className="space-y-3">
                {editingState.filters.length === 0 ? (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No filters applied. Add filters using the buttons above.
                  </p>
                ) : (
                  editingState.filters.map((filter) => (
                    <div key={filter.id} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={filter.enabled}
                            onCheckedChange={(enabled) => updateFilter(filter.id, { enabled })}
                          />
                          <Label className="font-medium">{filter.name}</Label>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFilter(filter.id)}
                        >
                          ×
                        </Button>
                      </div>
                      
                      {filter.enabled && (
                        <div>
                          <Label>Intensity: {filter.value}%</Label>
                          <Slider
                            value={[filter.value]}
                            onValueChange={([value]) => updateFilter(filter.id, { value })}
                            min={filter.type === 'brightness' || filter.type === 'contrast' ? -100 : 0}
                            max={100}
                            step={1}
                            className="mt-2"
                          />
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="transform" className="space-y-4">
            <div>
              <Label className="text-base font-medium">Transform Video</Label>
              <p className="text-sm text-muted-foreground mb-4">
                Rotate, flip, and scale your video
              </p>
              
              {/* Quick Actions */}
              <div className="grid grid-cols-3 gap-2 mb-4">
                <Button variant="outline" onClick={rotateClockwise}>
                  <RotateCw className="h-4 w-4 mr-2" />
                  Rotate 90°
                </Button>
                <Button variant="outline" onClick={flipHorizontal}>
                  <FlipHorizontal className="h-4 w-4 mr-2" />
                  Flip H
                </Button>
                <Button variant="outline" onClick={flipVertical}>
                  <FlipVertical className="h-4 w-4 mr-2" />
                  Flip V
                </Button>
              </div>
              
              <Separator />
              
              {/* Active Transformations */}
              <div className="space-y-3">
                {editingState.transformations.length === 0 ? (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No transformations applied. Use the buttons above to add transformations.
                  </p>
                ) : (
                  editingState.transformations.map((transform) => (
                    <div key={transform.id} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={transform.enabled}
                            onCheckedChange={(enabled) => updateTransformation(transform.id, { enabled })}
                          />
                          <Label className="font-medium">{transform.name}</Label>
                          <Badge variant="secondary" className="text-xs">
                            {transform.type}
                          </Badge>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeTransformation(transform.id)}
                        >
                          ×
                        </Button>
                      </div>
                      
                      {transform.enabled && (
                        <div className="space-y-2">
                          {Object.entries(transform.values).map(([key, value]) => (
                            <div key={key}>
                              <Label>{key}: {value}{key === 'angle' ? '°' : key.includes('scale') ? 'x' : ''}</Label>
                              <Slider
                                value={[value]}
                                onValueChange={([newValue]) => updateTransformation(transform.id, {
                                  values: { ...transform.values, [key]: newValue }
                                })}
                                min={key === 'angle' ? -180 : key.includes('scale') ? 0.1 : -100}
                                max={key === 'angle' ? 180 : key.includes('scale') ? 3 : 100}
                                step={key === 'angle' ? 1 : key.includes('scale') ? 0.1 : 1}
                                className="mt-1"
                              />
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
