'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  X, 
  Plus,
  MoreHorizontal,
  FileVideo,
  Wand2,
  Upload,
  Settings,
  Download
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Tab {
  id: string;
  title: string;
  icon?: React.ComponentType<{ className?: string }>;
  closable?: boolean;
  modified?: boolean;
  active?: boolean;
}

interface VSCodeTabsProps {
  tabs: Tab[];
  activeTabId: string;
  onTabChange: (tabId: string) => void;
  onTabClose?: (tabId: string) => void;
  onNewTab?: () => void;
  className?: string;
}

export function VSCodeTabs({
  tabs,
  activeTabId,
  onTabChange,
  onTabClose,
  onNewTab,
  className
}: VSCodeTabsProps) {
  const [draggedTab, setDraggedTab] = useState<string | null>(null);

  const handleTabClick = (tabId: string) => {
    onTabChange(tabId);
  };

  const handleTabClose = (e: React.MouseEvent, tabId: string) => {
    e.stopPropagation();
    onTabClose?.(tabId);
  };

  const handleDragStart = (e: React.DragEvent, tabId: string) => {
    setDraggedTab(tabId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragEnd = () => {
    setDraggedTab(null);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  return (
    <div className={cn(
      "flex items-center bg-background border-b border-border h-9",
      className
    )}>
      {/* Tab List */}
      <ScrollArea className="flex-1">
        <div className="flex items-center h-9">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = tab.id === activeTabId;
            const isDragged = draggedTab === tab.id;
            
            return (
              <div
                key={tab.id}
                className={cn(
                  "group relative flex items-center h-9 px-3 min-w-0 max-w-48 cursor-pointer border-r border-border/50 transition-colors",
                  isActive 
                    ? "bg-background text-foreground border-b-2 border-b-primary" 
                    : "bg-muted/30 text-muted-foreground hover:bg-muted/50 hover:text-foreground",
                  isDragged && "opacity-50"
                )}
                onClick={() => handleTabClick(tab.id)}
                draggable
                onDragStart={(e) => handleDragStart(e, tab.id)}
                onDragEnd={handleDragEnd}
                onDragOver={handleDragOver}
              >
                {/* Tab Content */}
                <div className="flex items-center space-x-2 min-w-0 flex-1">
                  {Icon && (
                    <Icon className="h-3.5 w-3.5 flex-shrink-0" />
                  )}
                  <span className="text-xs font-medium truncate">
                    {tab.title}
                    {tab.modified && (
                      <span className="ml-1 text-orange-500">●</span>
                    )}
                  </span>
                </div>

                {/* Close Button */}
                {tab.closable !== false && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => handleTabClose(e, tab.id)}
                    className={cn(
                      "h-5 w-5 p-0 ml-1 opacity-0 group-hover:opacity-100 hover:bg-muted-foreground/20 transition-opacity",
                      isActive && "opacity-100"
                    )}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}

                {/* Active Tab Indicator */}
                {isActive && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary" />
                )}
              </div>
            );
          })}
        </div>
      </ScrollArea>

      {/* Tab Actions */}
      <div className="flex items-center border-l border-border/50">
        {onNewTab && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onNewTab}
            className="h-8 w-8 p-0 hover:bg-muted/50"
          >
            <Plus className="h-3.5 w-3.5" />
          </Button>
        )}
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 hover:bg-muted/50"
        >
          <MoreHorizontal className="h-3.5 w-3.5" />
        </Button>
      </div>
    </div>
  );
}

// Predefined tab configurations
export const DEFAULT_TABS: Tab[] = [
  {
    id: 'project',
    title: 'Project',
    icon: FileVideo,
    closable: false
  },
  {
    id: 'ai-generate',
    title: 'AI Generate',
    icon: Wand2,
    modified: false
  },
  {
    id: 'media-library',
    title: 'Media Library',
    icon: Upload
  },
  {
    id: 'export',
    title: 'Export',
    icon: Download
  },
  {
    id: 'settings',
    title: 'Settings',
    icon: Settings
  }
];

// Tab content wrapper component
interface TabContentProps {
  activeTabId: string;
  children: React.ReactNode;
  className?: string;
}

export function TabContent({ activeTabId, children, className }: TabContentProps) {
  return (
    <div className={cn("flex-1 overflow-hidden", className)}>
      {children}
    </div>
  );
}

// Individual tab panel component
interface TabPanelProps {
  tabId: string;
  activeTabId: string;
  children: React.ReactNode;
  className?: string;
}

export function TabPanel({ tabId, activeTabId, children, className }: TabPanelProps) {
  const isActive = tabId === activeTabId;
  
  return (
    <div 
      className={cn(
        "h-full",
        isActive ? "block" : "hidden",
        className
      )}
    >
      {children}
    </div>
  );
}
