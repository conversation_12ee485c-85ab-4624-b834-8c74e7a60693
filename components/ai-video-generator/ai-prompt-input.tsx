'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Separator } from '@/components/ui/separator';
import { 
  Wand2, 
  Sparkles, 
  Clock, 
  Ratio, 
  Palette, 
  Tag,
  X,
  Plus,
  Shuffle,
  Copy,
  Save,
  RotateCcw
} from 'lucide-react';
import { AIPromptData, AIVideoStyle, AIGenerationSettings } from '@/lib/video/types/video-types';
import { toast } from 'sonner';

interface AIPromptInputProps {
  onPromptChange: (prompt: AIPromptData) => void;
  onSettingsChange: (settings: AIGenerationSettings) => void;
  initialPrompt?: Partial<AIPromptData>;
  initialSettings?: Partial<AIGenerationSettings>;
  className?: string;
}

const VIDEO_STYLES: AIVideoStyle[] = [
  {
    id: 'realistic',
    name: 'Realistic',
    description: 'Photorealistic video generation',
    thumbnail: '/styles/realistic.jpg',
    category: 'realistic',
    parameters: { style_strength: 0.8 }
  },
  {
    id: 'cinematic',
    name: 'Cinematic',
    description: 'Movie-like cinematography',
    thumbnail: '/styles/cinematic.jpg',
    category: 'cinematic',
    parameters: { style_strength: 0.9, lighting: 'dramatic' }
  },
  {
    id: 'animated',
    name: 'Animated',
    description: '3D animation style',
    thumbnail: '/styles/animated.jpg',
    category: 'animated',
    parameters: { style_strength: 1.0, animation_type: '3d' }
  },
  {
    id: 'artistic',
    name: 'Artistic',
    description: 'Artistic and stylized',
    thumbnail: '/styles/artistic.jpg',
    category: 'artistic',
    parameters: { style_strength: 0.7, artistic_style: 'painterly' }
  }
];

const PROMPT_SUGGESTIONS = [
  'A serene mountain landscape at sunset',
  'Bustling city street with neon lights',
  'Ocean waves crashing on a rocky shore',
  'Forest path with dappled sunlight',
  'Futuristic cityscape with flying cars',
  'Cozy cabin in a snowy winter scene'
];

const ASPECT_RATIOS = [
  { label: '16:9 (Landscape)', value: '16:9' },
  { label: '9:16 (Portrait)', value: '9:16' },
  { label: '1:1 (Square)', value: '1:1' },
  { label: '4:3 (Classic)', value: '4:3' },
  { label: '21:9 (Ultrawide)', value: '21:9' }
];

export function AIPromptInput({
  onPromptChange,
  onSettingsChange,
  initialPrompt = {},
  initialSettings = {},
  className = ''
}: AIPromptInputProps) {
  const [prompt, setPrompt] = useState<AIPromptData>({
    text: '',
    style: VIDEO_STYLES[0],
    mood: '',
    duration: 5,
    aspectRatio: '16:9',
    tags: [],
    negativePrompt: '',
    seed: undefined,
    strength: 0.8,
    ...initialPrompt
  });

  const [settings, setSettings] = useState<AIGenerationSettings>({
    model: 'runway',
    resolution: { width: 1920, height: 1080, aspectRatio: '16:9' },
    fps: 30,
    duration: 5,
    quality: 'high',
    iterations: 1,
    guidanceScale: 7.5,
    seed: undefined,
    ...initialSettings
  });

  const [newTag, setNewTag] = useState('');
  const [characterCount, setCharacterCount] = useState(0);
  const maxCharacters = 500;

  useEffect(() => {
    setCharacterCount(prompt.text.length);
  }, [prompt.text]);

  useEffect(() => {
    onPromptChange(prompt);
  }, [prompt, onPromptChange]);

  useEffect(() => {
    onSettingsChange(settings);
  }, [settings, onSettingsChange]);

  const updatePrompt = useCallback((updates: Partial<AIPromptData>) => {
    setPrompt(prev => ({ ...prev, ...updates }));
  }, []);

  const updateSettings = useCallback((updates: Partial<AIGenerationSettings>) => {
    setSettings(prev => ({ ...prev, ...updates }));
  }, []);

  const addTag = useCallback(() => {
    if (newTag.trim() && !prompt.tags.includes(newTag.trim())) {
      updatePrompt({ tags: [...prompt.tags, newTag.trim()] });
      setNewTag('');
    }
  }, [newTag, prompt.tags, updatePrompt]);

  const removeTag = useCallback((tagToRemove: string) => {
    updatePrompt({ tags: prompt.tags.filter(tag => tag !== tagToRemove) });
  }, [prompt.tags, updatePrompt]);

  const generateRandomSeed = useCallback(() => {
    const seed = Math.floor(Math.random() * 1000000);
    updatePrompt({ seed });
    updateSettings({ seed });
  }, [updatePrompt, updateSettings]);

  const useSuggestion = useCallback((suggestion: string) => {
    updatePrompt({ text: suggestion });
  }, [updatePrompt]);

  const copyPrompt = useCallback(() => {
    navigator.clipboard.writeText(prompt.text);
    toast.success('Prompt copied to clipboard');
  }, [prompt.text]);

  const resetPrompt = useCallback(() => {
    setPrompt({
      text: '',
      style: VIDEO_STYLES[0],
      mood: '',
      duration: 5,
      aspectRatio: '16:9',
      tags: [],
      negativePrompt: '',
      seed: undefined,
      strength: 0.8
    });
  }, []);

  const updateAspectRatio = useCallback((aspectRatio: string) => {
    updatePrompt({ aspectRatio });
    
    // Update resolution based on aspect ratio
    let resolution;
    switch (aspectRatio) {
      case '16:9':
        resolution = { width: 1920, height: 1080, aspectRatio };
        break;
      case '9:16':
        resolution = { width: 1080, height: 1920, aspectRatio };
        break;
      case '1:1':
        resolution = { width: 1080, height: 1080, aspectRatio };
        break;
      case '4:3':
        resolution = { width: 1440, height: 1080, aspectRatio };
        break;
      case '21:9':
        resolution = { width: 2560, height: 1080, aspectRatio };
        break;
      default:
        resolution = { width: 1920, height: 1080, aspectRatio: '16:9' };
    }
    
    updateSettings({ resolution });
  }, [updatePrompt, updateSettings]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Main Prompt Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5" />
            AI Video Prompt
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="prompt-text">Describe your video</Label>
            <Textarea
              id="prompt-text"
              placeholder="Describe the video you want to generate in detail..."
              value={prompt.text}
              onChange={(e) => updatePrompt({ text: e.target.value })}
              rows={4}
              className="resize-none"
            />
            <div className="flex justify-between items-center mt-2">
              <span className={`text-sm ${characterCount > maxCharacters ? 'text-red-500' : 'text-muted-foreground'}`}>
                {characterCount}/{maxCharacters} characters
              </span>
              <div className="flex gap-2">
                <Button variant="ghost" size="sm" onClick={copyPrompt}>
                  <Copy className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" onClick={resetPrompt}>
                  <RotateCcw className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Prompt Suggestions */}
          <div>
            <Label>Quick suggestions</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {PROMPT_SUGGESTIONS.map((suggestion, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => useSuggestion(suggestion)}
                  className="text-xs"
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          </div>

          {/* Negative Prompt */}
          <div>
            <Label htmlFor="negative-prompt">Negative prompt (optional)</Label>
            <Textarea
              id="negative-prompt"
              placeholder="What you don't want to see in the video..."
              value={prompt.negativePrompt}
              onChange={(e) => updatePrompt({ negativePrompt: e.target.value })}
              rows={2}
              className="resize-none"
            />
          </div>
        </CardContent>
      </Card>

      {/* Style Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Video Style
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {VIDEO_STYLES.map((style) => (
              <div
                key={style.id}
                className={`
                  cursor-pointer rounded-lg border-2 p-3 transition-all
                  ${prompt.style.id === style.id 
                    ? 'border-primary bg-primary/5' 
                    : 'border-muted hover:border-primary/50'
                  }
                `}
                onClick={() => updatePrompt({ style })}
              >
                <div className="aspect-video bg-muted rounded mb-2 flex items-center justify-center">
                  <Sparkles className="h-6 w-6 text-muted-foreground" />
                </div>
                <h4 className="font-medium text-sm">{style.name}</h4>
                <p className="text-xs text-muted-foreground">{style.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Tags */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Tag className="h-5 w-5" />
            Tags
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex gap-2">
            <Input
              placeholder="Add a tag..."
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && addTag()}
            />
            <Button onClick={addTag} size="sm">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          
          {prompt.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {prompt.tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeTag(tag)}
                  />
                </Badge>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Generation Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Generation Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Duration */}
            <div>
              <Label className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Duration: {prompt.duration}s
              </Label>
              <Slider
                value={[prompt.duration]}
                onValueChange={([value]) => {
                  updatePrompt({ duration: value });
                  updateSettings({ duration: value });
                }}
                min={1}
                max={30}
                step={1}
                className="mt-2"
              />
            </div>

            {/* Aspect Ratio */}
            <div>
              <Label className="flex items-center gap-2">
                <Ratio className="h-4 w-4" />
                Aspect Ratio
              </Label>
              <Select value={prompt.aspectRatio} onValueChange={updateAspectRatio}>
                <SelectTrigger className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {ASPECT_RATIOS.map((ratio) => (
                    <SelectItem key={ratio.value} value={ratio.value}>
                      {ratio.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* AI Model */}
            <div>
              <Label>AI Model</Label>
              <Select 
                value={settings.model} 
                onValueChange={(value: any) => updateSettings({ model: value })}
              >
                <SelectTrigger className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="runway">Runway ML</SelectItem>
                  <SelectItem value="pika">Pika Labs</SelectItem>
                  <SelectItem value="stability">Stability AI</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Quality */}
            <div>
              <Label>Quality</Label>
              <Select 
                value={settings.quality} 
                onValueChange={(value: any) => updateSettings({ quality: value })}
              >
                <SelectTrigger className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="standard">Standard</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="ultra">Ultra</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator />

          {/* Advanced Settings */}
          <div className="space-y-4">
            <h4 className="font-medium">Advanced Settings</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Guidance Scale */}
              <div>
                <Label>Guidance Scale: {settings.guidanceScale}</Label>
                <Slider
                  value={[settings.guidanceScale]}
                  onValueChange={([value]) => updateSettings({ guidanceScale: value })}
                  min={1}
                  max={20}
                  step={0.5}
                  className="mt-2"
                />
              </div>

              {/* Seed */}
              <div>
                <Label>Seed (optional)</Label>
                <div className="flex gap-2 mt-2">
                  <Input
                    type="number"
                    placeholder="Random"
                    value={prompt.seed || ''}
                    onChange={(e) => {
                      const seed = e.target.value ? parseInt(e.target.value) : undefined;
                      updatePrompt({ seed });
                      updateSettings({ seed });
                    }}
                  />
                  <Button variant="outline" size="sm" onClick={generateRandomSeed}>
                    <Shuffle className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
