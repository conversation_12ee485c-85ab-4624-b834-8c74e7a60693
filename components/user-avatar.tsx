"use client"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"

// Helper function to get user initials
function getUserInitials(name?: string | null, email?: string | null): string {
  if (name) {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }
  if (email) {
    return email.charAt(0).toUpperCase();
  }
  return 'U';
}

interface UserAvatarProps {
  user?: {
    name?: string | null
    email?: string | null
    image?: string | null
  }
  size?: "sm" | "md" | "lg" | "xl"
  className?: string
  showFallback?: boolean
}

const sizeClasses = {
  sm: "h-6 w-6 text-xs",
  md: "h-8 w-8 text-sm", 
  lg: "h-10 w-10 text-base",
  xl: "h-16 w-16 text-lg"
}

export function UserAvatar({ 
  user, 
  size = "md", 
  className,
  showFallback = true 
}: UserAvatarProps) {
  const userInitials = getUserInitials(user?.name, user?.email)
  
  return (
    <Avatar className={cn(sizeClasses[size], className)}>
      <AvatarImage 
        src={user?.image || ''} 
        alt={user?.name || user?.email || 'User'} 
      />
      {showFallback && (
        <AvatarFallback className="bg-primary text-primary-foreground font-medium">
          {userInitials}
        </AvatarFallback>
      )}
    </Avatar>
  )
}