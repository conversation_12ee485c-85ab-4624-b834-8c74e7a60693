import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

// Create a simple Progress component since it doesn't exist
const Progress = ({ value, className }: { value: number, className?: string }) => {
  return (
    <div className={`w-full bg-muted rounded-full ${className || ''}`}>
      <div 
        className="bg-primary rounded-full h-full" 
        style={{ width: `${value}%` }}
      />
    </div>
  );
};

export function ContentPerformance() {
  const tracks = [
    {
      title: "Summer Nights",
      streams: 1245678,
      growth: 12.5,
      platforms: [
        { name: "Spotify", percentage: 65 },
        { name: "Apple Music", percentage: 20 },
        { name: "YouTube", percentage: 10 },
        { name: "Others", percentage: 5 },
      ],
      sentiment: 92,
    },
    {
      title: "Midnight Dreams",
      streams: 987543,
      growth: 8.3,
      platforms: [
        { name: "Spotify", percentage: 55 },
        { name: "Apple Music", percentage: 25 },
        { name: "YouTube", percentage: 15 },
        { name: "Others", percentage: 5 },
      ],
      sentiment: 88,
    },
    {
      title: "Lost in the Echo",
      streams: 654321,
      growth: -2.1,
      platforms: [
        { name: "Spotify", percentage: 60 },
        { name: "Apple Music", percentage: 15 },
        { name: "YouTube", percentage: 20 },
        { name: "Others", percentage: 5 },
      ],
      sentiment: 76,
    },
  ];

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M";
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K";
    }
    return num.toString();
  };

  return (
    <Card className="border-none shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Content Performance</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {tracks.map((track, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">{track.title}</h3>
                  <div className="flex gap-2 items-center">
                    <span className="text-sm text-muted-foreground">{formatNumber(track.streams)} streams</span>
                    <Badge variant={track.growth >= 0 ? "secondary" : "destructive"} className="text-xs">
                      {track.growth > 0 ? "+" : ""}{track.growth}%
                    </Badge>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">AI Sentiment</span>
                  <Badge 
                    variant={track.sentiment > 85 ? "secondary" : "default"}
                    className={`font-medium ${track.sentiment > 85 ? 'bg-green-100 text-green-700' : track.sentiment > 70 ? 'bg-yellow-100 text-yellow-700' : 'bg-red-100 text-red-700'}`}
                  >
                    {track.sentiment}%
                  </Badge>
                </div>
              </div>
              <div className="space-y-1.5">
                {track.platforms.map((platform, pIndex) => (
                  <div key={pIndex} className="space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span>{platform.name}</span>
                      <span>{platform.percentage}%</span>
                    </div>
                    <Progress value={platform.percentage} className="h-1.5" />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
} 