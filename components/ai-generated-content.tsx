'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

import { Progress } from '@/components/ui/progress';
import { 
  Music, 
  TrendingUp, 
  MessageSquare, 
  Calendar, 
  Copy, 
  Target,
  Lightbulb,
  Hash,
  Users,
  Zap
} from 'lucide-react';
import { useState } from 'react';

interface ToolInvocation {
  toolCallId: string;
  toolName: string;
  state: 'call' | 'result';
  result?: {
    id: string;
    genre?: string;
    mood?: string;
    theme?: string;
    keySignature?: string;
    suggestedBPM?: string;
    structure?: string[];
    instruments?: string[];
    insights?: {
      popularity: number;
      growthRate: string;
      competitionLevel: string;
      optimalReleaseTime: string;
      peakDays: string[];
    };
    opportunities?: string[];
    recommendations?: string[];
    text?: string;
    hashtags?: string[];
    specs?: {
      maxLength: number;
      hashtagLimit: number;
    };
    platform?: string;
    contentType?: string;
    tone?: string;
    trackName?: string;
    artist?: string;
    releaseType?: string;
    releaseDate?: string;
    timeline?: Array<{
      date: string;
      task: string;
      priority: 'high' | 'medium' | 'low';
    }>;
    marketingStrategy?: {
      focus: string;
      platforms: string[];
      tactics: string[];
    };
    budget?: {
      total: number;
      marketing: number;
      production: number;
      distribution: number;
      contingency: number;
    };
    platforms?: string[];
  };
  args?: Record<string, unknown>;
}

interface GeneratedContentProps {
  toolInvocation: ToolInvocation;
}

export function GeneratedContent({ toolInvocation }: GeneratedContentProps) {
  const [copiedId, setCopiedId] = useState<string | null>(null);

  const handleCopy = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedId(id);
      setTimeout(() => setCopiedId(null), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  if (toolInvocation.state !== 'result' || !toolInvocation.result) {
    return null;
  }

  const { result } = toolInvocation;

  switch (toolInvocation.toolName) {
    case 'generateSongConcept':
      return <SongConceptCard result={result} onCopy={handleCopy} copiedId={copiedId} />;
    case 'analyzeMarketTrends':
      return <MarketTrendsCard result={result} onCopy={handleCopy} copiedId={copiedId} />;
    case 'generateMarketingContent':
      return <MarketingContentCard result={result} onCopy={handleCopy} copiedId={copiedId} />;
    case 'generateReleasePlan':
      return <ReleasePlanCard result={result} onCopy={handleCopy} copiedId={copiedId} />;
    default:
      return null;
  }
}

function SongConceptCard({ result, onCopy, copiedId }: { result: NonNullable<ToolInvocation['result']>; onCopy: (text: string, id: string) => void; copiedId: string | null }) {
  const conceptText = `
Song Concept: ${result.theme}
Genre: ${result.genre}
Mood: ${result.mood}
Key: ${result.keySignature}
BPM: ${result.suggestedBPM}
Structure: ${result.structure.join(' → ')}
Instruments: ${result.instruments.join(', ')}
  `.trim();

  return (
    <Card className="mt-2">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Music className="h-5 w-5 text-purple-600" />
            <CardTitle className="text-lg">Song Concept Generated</CardTitle>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onCopy(conceptText, 'concept')}
            className="gap-2"
          >
            <Copy className="h-4 w-4" />
            {copiedId === 'concept' ? 'Copied!' : 'Copy'}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-1">Genre & Mood</h4>
              <div className="flex gap-2">
                <Badge variant="secondary">{result.genre}</Badge>
                <Badge variant="outline">{result.mood}</Badge>
              </div>
            </div>
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-1">Technical Details</h4>
              <div className="flex gap-2">
                <Badge variant="secondary">{result.keySignature}</Badge>
                <Badge variant="outline">{result.suggestedBPM} BPM</Badge>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">Song Structure</h4>
            <div className="flex flex-wrap gap-2">
              {result.structure.map((section: string, index: number) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {section}
                </Badge>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">Suggested Instruments</h4>
            <div className="flex flex-wrap gap-2">
              {result.instruments.map((instrument: string, index: number) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {instrument}
                </Badge>
              ))}
            </div>
          </div>
          
          <div className="p-3 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-sm mb-1">Theme</h4>
            <p className="text-sm text-muted-foreground">{result.theme}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function MarketTrendsCard({ result, onCopy, copiedId }: { result: NonNullable<ToolInvocation['result']>; onCopy: (text: string, id: string) => void; copiedId: string | null }) {
  const trendsText = `
Market Analysis: ${result.genre}
Timeframe: ${result.timeframe}
Platform: ${result.platform}
Popularity Score: ${result.insights.popularity}/100
Growth Rate: ${result.insights.growthRate}
Competition: ${result.insights.competitionLevel}
Optimal Release: ${result.insights.optimalReleaseTime}
Peak Days: ${result.insights.peakDays.join(', ')}
  `.trim();

  return (
    <Card className="mt-2">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-green-600" />
            <CardTitle className="text-lg">Market Trends Analysis</CardTitle>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onCopy(trendsText, 'trends')}
            className="gap-2"
          >
            <Copy className="h-4 w-4" />
            {copiedId === 'trends' ? 'Copied!' : 'Copy'}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-1">Analysis Scope</h4>
              <div className="flex gap-2">
                <Badge variant="secondary">{result.genre}</Badge>
                <Badge variant="outline">{result.timeframe}</Badge>
              </div>
            </div>
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-1">Platform Focus</h4>
              <Badge variant="secondary">{result.platform}</Badge>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">Key Metrics</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Popularity Score</span>
                <div className="flex items-center gap-2">
                  <Progress value={result.insights.popularity} className="w-20" />
                  <span className="text-sm font-medium">{result.insights.popularity}/100</span>
                </div>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Growth Rate</span>
                <Badge variant="outline" className="text-green-600">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {result.insights.growthRate}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Competition Level</span>
                <Badge variant={result.insights.competitionLevel === 'Low' ? 'secondary' : 
                              result.insights.competitionLevel === 'Medium' ? 'outline' : 'destructive'}>
                  {result.insights.competitionLevel}
                </Badge>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">Optimal Timing</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="text-sm font-medium">Release Time</span>
                <p className="text-sm text-muted-foreground">{result.insights.optimalReleaseTime}</p>
              </div>
              <div>
                <span className="text-sm font-medium">Peak Days</span>
                <p className="text-sm text-muted-foreground">{result.insights.peakDays.join(', ')}</p>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">Opportunities</h4>
            <ul className="space-y-1">
              {result.opportunities.map((opportunity: string, index: number) => (
                <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                  <Lightbulb className="h-3 w-3 text-yellow-500 mt-0.5 flex-shrink-0" />
                  {opportunity}
                </li>
              ))}
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">Recommendations</h4>
            <ul className="space-y-1">
              {result.recommendations.map((recommendation: string, index: number) => (
                <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                  <Target className="h-3 w-3 text-blue-500 mt-0.5 flex-shrink-0" />
                  {recommendation}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function MarketingContentCard({ result, onCopy, copiedId }: { result: NonNullable<ToolInvocation['result']>; onCopy: (text: string, id: string) => void; copiedId: string | null }) {
  return (
    <Card className="mt-2">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-blue-600" />
            <CardTitle className="text-lg">Marketing Content Created</CardTitle>
          </div>
          <Badge variant="secondary">{result.platform}</Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-1">Content Details</h4>
              <div className="flex gap-2">
                <Badge variant="secondary">{result.contentType}</Badge>
                <Badge variant="outline">{result.tone}</Badge>
              </div>
            </div>
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-1">Platform Limits</h4>
              <p className="text-sm text-muted-foreground">
                Max: {result.specs.maxLength} chars, {result.specs.hashtagLimit} hashtags
              </p>
            </div>
          </div>
          
          <div>
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-sm text-muted-foreground">Generated Content</h4>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCopy(result.text, 'content')}
                className="gap-2"
              >
                <Copy className="h-4 w-4" />
                {copiedId === 'content' ? 'Copied!' : 'Copy'}
              </Button>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm whitespace-pre-wrap">{result.text}</p>
            </div>
          </div>
          
          <div>
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-sm text-muted-foreground">Suggested Hashtags</h4>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCopy(result.hashtags.join(' '), 'hashtags')}
                className="gap-2"
              >
                <Hash className="h-4 w-4" />
                {copiedId === 'hashtags' ? 'Copied!' : 'Copy'}
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {result.hashtags.map((hashtag: string, index: number) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {hashtag}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function ReleasePlanCard({ result, onCopy, copiedId }: { result: NonNullable<ToolInvocation['result']>; onCopy: (text: string, id: string) => void; copiedId: string | null }) {
  const planText = `
Release Plan: ${result.trackName} by ${result.artist}
Release Type: ${result.releaseType}
Release Date: ${result.releaseDate}
Total Budget: $${result.budget.total}
Platforms: ${result.platforms.join(', ')}
  `.trim();

  return (
    <Card className="mt-2">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-indigo-600" />
            <CardTitle className="text-lg">Release Plan Created</CardTitle>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onCopy(planText, 'plan')}
            className="gap-2"
          >
            <Copy className="h-4 w-4" />
            {copiedId === 'plan' ? 'Copied!' : 'Copy'}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-1">Release Details</h4>
              <div className="space-y-1">
                <p className="text-sm font-medium">{result.trackName}</p>
                <p className="text-sm text-muted-foreground">by {result.artist}</p>
                <div className="flex gap-2">
                  <Badge variant="secondary">{result.releaseType}</Badge>
                  <Badge variant="outline">{result.releaseDate}</Badge>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-1">Budget Overview</h4>
              <div className="space-y-1">
                <p className="text-lg font-bold text-green-600">${result.budget.total}</p>
                <p className="text-sm text-muted-foreground">Total Budget</p>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">Budget Breakdown</h4>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-sm">Marketing</span>
                <span className="text-sm font-medium">${result.budget.marketing}</span>
              </div>
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-sm">Production</span>
                <span className="text-sm font-medium">${result.budget.production}</span>
              </div>
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-sm">Distribution</span>
                <span className="text-sm font-medium">${result.budget.distribution}</span>
              </div>
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-sm">Contingency</span>
                <span className="text-sm font-medium">${result.budget.contingency}</span>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">Platform Distribution</h4>
            <div className="flex flex-wrap gap-2">
              {result.platforms.map((platform: string, index: number) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {platform}
                </Badge>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">Marketing Strategy</h4>
            <div className="p-3 bg-gray-50 rounded-lg space-y-2">
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">Focus:</span>
                <span className="text-sm">{result.marketingStrategy.focus}</span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">Platforms:</span>
                <span className="text-sm">{result.marketingStrategy.platforms.join(', ')}</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-yellow-500" />
                <span className="text-sm font-medium">Tactics:</span>
                <span className="text-sm">{result.marketingStrategy.tactics.join(', ')}</span>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">Timeline</h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {result.timeline.map((item: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant={item.priority === 'high' ? 'destructive' : 
                              item.priority === 'medium' ? 'outline' : 'secondary'}
                      className="text-xs"
                    >
                      {item.priority}
                    </Badge>
                    <span className="text-sm">{item.task}</span>
                  </div>
                  <span className="text-xs text-muted-foreground">{item.date}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}