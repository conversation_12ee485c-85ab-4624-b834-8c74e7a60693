"use client"

import { useMemo } from 'react'
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  RadarChart,
  Radar,
  ScatterChart,
  Scatter,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
} from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useChartData } from '@/hooks/use-chart-data'
import { useChartTheme } from '@/hooks/use-chart-theme'
import { useChartCalculations } from '@/hooks/use-chart-calculations'
import { useChartFilters } from '@/hooks/use-chart-filters'
import { useIsMobile } from '@/hooks/use-mobile'

interface ChartVariationsProps {
  endpoint: string
  metrics: string[]
  title: string
  description?: string
}

export function ChartVariations({
  endpoint,
  metrics,
  title,
  description,
}: ChartVariationsProps) {
  const isMobile = useIsMobile()
  const initialTimeRange = isMobile ? '7d' : '30d'

  const {
    data,
    metrics: metricData,
    loading,
    error,
    timeRange,
    chartType,
    handleTimeRangeChange,
    handleChartTypeChange,
    handleRefresh,
    handleExport,
  } = useChartData({
    endpoint,
    initialTimeRange,
    initialChartType: 'area',
  })

  const { chartTheme, getChartStyle, getColor } = useChartTheme()
  const {
    calculateGrowthRate,
    calculateMovingAverage,
    calculateTotals,
    calculatePercentageChange,
    getTimeRangeLabel,
    formatValue,
  } = useChartCalculations({
    data,
    metrics,
    timeRange,
  })

  const { filteredData, addFilter, removeFilter, updateSort, clearFilters } = useChartFilters({
    data,
  })

  const chartStyle = getChartStyle;

  const renderChart = useMemo(() => {
    if (loading) {
      return <div className="flex items-center justify-center h-80">Loading...</div>
    }

    if (error) {
      return <div className="flex items-center justify-center h-80 text-red-500">{error}</div>
    }

    const tooltipStyle = {
      contentStyle: {
        backgroundColor: chartTheme.tooltipBackground,
        border: 'none',
        borderRadius: '6px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        color: chartTheme.tooltipText,
      },
    }

    const axisStyle = {
      tick: { fill: chartTheme.textColor },
      axisLine: { stroke: chartTheme.gridColor },
    }

    switch (chartType) {
      case 'area':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <AreaChart data={filteredData} style={chartStyle.style}>
              <CartesianGrid strokeDasharray={chartStyle.cartesianGrid.strokeDasharray} stroke={chartStyle.cartesianGrid.stroke} />
              <XAxis dataKey="date" tick={chartStyle.xAxis.tick} axisLine={chartStyle.xAxis.axisLine} />
              <YAxis tick={chartStyle.yAxis.tick} axisLine={chartStyle.yAxis.axisLine} />
              <Tooltip {...chartStyle.tooltip} />
              <Legend wrapperStyle={chartStyle.legend.textStyle} />
              {metrics.map((metric, index) => (
                <Area
                  key={metric}
                  type="monotone"
                  dataKey={metric}
                  fill={getColor(index)}
                  stroke={getColor(index)}
                  fillOpacity={chartTheme.areaOpacity}
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        )

      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={filteredData} style={chartStyle.style}>
              <CartesianGrid strokeDasharray={chartStyle.cartesianGrid.strokeDasharray} stroke={chartStyle.cartesianGrid.stroke} />
              <XAxis dataKey="date" tick={chartStyle.xAxis.tick} axisLine={chartStyle.xAxis.axisLine} />
              <YAxis tick={chartStyle.yAxis.tick} axisLine={chartStyle.yAxis.axisLine} />
              <Tooltip {...chartStyle.tooltip} />
              <Legend wrapperStyle={chartStyle.legend.textStyle} />
              {metrics.map((metric, index) => (
                <Bar key={metric} dataKey={metric} fill={getColor(index)} />
              ))}
            </BarChart>
          </ResponsiveContainer>
        )

      case 'line':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={filteredData} style={chartStyle.style}>
              <CartesianGrid strokeDasharray={chartStyle.cartesianGrid.strokeDasharray} stroke={chartStyle.cartesianGrid.stroke} />
              <XAxis dataKey="date" tick={chartStyle.xAxis.tick} axisLine={chartStyle.xAxis.axisLine} />
              <YAxis tick={chartStyle.yAxis.tick} axisLine={chartStyle.yAxis.axisLine} />
              <Tooltip {...chartStyle.tooltip} />
              <Legend wrapperStyle={chartStyle.legend.textStyle} />
              {metrics.map((metric, index) => (
                <Line
                  key={metric}
                  type="monotone"
                  dataKey={metric}
                  stroke={getColor(index)}
                  dot={false}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        )

      case 'pie':
        const totals = calculateTotals
        return (
          <ResponsiveContainer width="100%" height={400}>
            <PieChart style={chartStyle.style}>
              <Pie
                data={Object.entries(totals).map(([name, value], index) => ({
                  name,
                  value,
                  fill: getColor(index),
                }))}
                dataKey="value"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={150}
                label
              />
              <Tooltip {...chartStyle.tooltip} />
              <Legend wrapperStyle={chartStyle.legend.textStyle} />
            </PieChart>
          </ResponsiveContainer>
        )

      case 'radar':
        const percentages = calculatePercentageChange
        const radarData = Object.entries(percentages).map(([name, value]) => ({
          name,
          value: Number(value) || 0,
        }))
        return (
          <ResponsiveContainer width="100%" height={400}>
            <RadarChart data={radarData} style={chartStyle.style}>
              <PolarGrid stroke={chartTheme.gridColor} />
              <PolarAngleAxis
                dataKey="name"
                tick={{ fill: chartTheme.textColor }}
              />
              <PolarRadiusAxis tick={{ fill: chartTheme.textColor }} />
              <Radar
                dataKey="value"
                stroke={getColor(0)}
                fill={getColor(0)}
                fillOpacity={0.6}
              />
              <Tooltip {...chartStyle.tooltip} />
              <Legend wrapperStyle={chartStyle.legend.textStyle} />
            </RadarChart>
          </ResponsiveContainer>
        )

      case 'scatter':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <ScatterChart style={chartStyle.style}>
              <CartesianGrid strokeDasharray={chartStyle.cartesianGrid.strokeDasharray} stroke={chartStyle.cartesianGrid.stroke} />
              <XAxis dataKey={metrics[0]} tick={chartStyle.xAxis.tick} axisLine={chartStyle.xAxis.axisLine} />
              <YAxis dataKey={metrics[1]} tick={chartStyle.yAxis.tick} axisLine={chartStyle.yAxis.axisLine} />
              <Tooltip {...chartStyle.tooltip} />
              <Legend wrapperStyle={chartStyle.legend.textStyle} />
              <Scatter
                data={filteredData}
                fill={getColor(0)}
              />
            </ScatterChart>
          </ResponsiveContainer>
        )

      default:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <AreaChart data={filteredData} style={chartStyle.style}>
              <CartesianGrid strokeDasharray={chartStyle.cartesianGrid.strokeDasharray} stroke={chartStyle.cartesianGrid.stroke} />
              <XAxis dataKey="date" tick={chartStyle.xAxis.tick} axisLine={chartStyle.xAxis.axisLine} />
              <YAxis tick={chartStyle.yAxis.tick} axisLine={chartStyle.yAxis.axisLine} />
              <Tooltip {...chartStyle.tooltip} />
              <Legend wrapperStyle={chartStyle.legend.textStyle} />
              {metrics.map((metric, index) => (
                <Area
                  key={metric}
                  type="monotone"
                  dataKey={metric}
                  fill={getColor(index)}
                  stroke={getColor(index)}
                  fillOpacity={chartTheme.areaOpacity}
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        )
    }
  }, [chartType, filteredData, metrics, chartTheme, getColor, loading, error, calculateTotals, calculatePercentageChange, chartStyle, metrics])

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <p className="text-sm text-muted-foreground">{description}</p>}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <Tabs
            defaultValue={chartType}
            className="w-full sm:w-auto"
            onValueChange={value => handleChartTypeChange(value as any)}
          >
            <TabsList>
              <TabsTrigger value="area">Area</TabsTrigger>
              <TabsTrigger value="bar">Bar</TabsTrigger>
              <TabsTrigger value="line">Line</TabsTrigger>
              <TabsTrigger value="pie">Pie</TabsTrigger>
              <TabsTrigger value="radar">Radar</TabsTrigger>
              <TabsTrigger value="scatter">Scatter</TabsTrigger>
            </TabsList>
          </Tabs>
          <Tabs
            defaultValue={timeRange}
            className="w-full sm:w-auto"
            onValueChange={value => handleTimeRangeChange(value as any)}
          >
            <TabsList>
              <TabsTrigger value="7d">7D</TabsTrigger>
              <TabsTrigger value="30d">30D</TabsTrigger>
              <TabsTrigger value="90d">90D</TabsTrigger>
              <TabsTrigger value="1y">1Y</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[400px]">{renderChart}</div>
      </CardContent>
    </Card>
  )
}