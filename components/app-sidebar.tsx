"use client"

import * as React from "react"
import {
  IconCamera,
  IconChartBar,
  IconDashboard,
  IconDatabase,
  IconFileAi,
  IconFileDescription,
  IconFileWord,
  IconFolder,
  IconHelp,
  IconInnerShadowTop,
  IconListDetails,
  IconReport,
  IconSearch,
  IconSettings,
  IconShare,
  IconSparkles,
  IconUsers,
  IconVideo,
  IconDeviceDesktop,
  IconPalette,
  IconWand,
  IconMicrophone,
} from "@tabler/icons-react"

import { NavDocuments } from "@/components/nav-documents"
import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import { NavVideo } from "@/components/nav-video"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { useUIStore } from "@/lib/stores/ui-store"
import { usePathname } from "next/navigation"
import { useEffect } from "react"

const data = {
  navMain: [
    {
      title: "Dashboard",
      url: "#",
      icon: IconDashboard,
    },
    {
      title: "Lifecycle",
      url: "#",
      icon: IconListDetails,
    },
    {
      title: "Analytics",
      url: "#",
      icon: IconChartBar,
    },
    {
      title: "Projects",
      url: "#",
      icon: IconFolder,
    },
    {
      title: "Team",
      url: "#",
      icon: IconUsers,
    },
  ],
  navClouds: [
    {
      title: "Capture",
      icon: IconCamera,
      isActive: true,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Proposal",
      icon: IconFileDescription,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Prompts",
      icon: IconFileAi,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "Settings",
      url: "#",
      icon: IconSettings,
    },
    {
      title: "Get Help",
      url: "#",
      icon: IconHelp,
    },
    {
      title: "Search",
      url: "#",
      icon: IconSearch,
    },
  ],
  documents: [
    {
      name: "Data Library",
      url: "#",
      icon: IconDatabase,
    },
    {
      name: "Reports",
      url: "#",
      icon: IconReport,
    },
    {
      name: "Word Assistant",
      url: "#",
      icon: IconFileWord,
    },
  ],
}

interface AppSidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "inset"
}

export function AppSidebar({
  className,
  variant = "default",
  ...props
}: AppSidebarProps) {
  const { 
    activeItem,
    setActiveItem,
    isSidebarOpen, 
    toggleSidebar
  } = useUIStore()

  const pathname = usePathname()

  useEffect(() => {
    setActiveItem(pathname)
  }, [pathname, setActiveItem])
  

  return (
    <Sidebar
      className={cn(
        "fixed inset-y-0 left-0 z-20 flex w-[var(--sidebar-width)] flex-col border-r bg-background data-[inset=true]:top-[var(--header-height)]",
        className
      )}
      data-inset={variant === "inset"}
      {...props}
    >
      <SidebarHeader className="px-2 pt-2">
        <Button variant="secondary" size="sm" className="w-full" asChild>
          <a href="/" className="flex items-center justify-start gap-2">
            <MusicIcon className="h-4 w-4" />
            <span className="text-xs font-medium">TuneBase</span>
          </a>
        </Button>
      </SidebarHeader>
      <SidebarContent className="flex flex-1 flex-col gap-1">
        <NavMain
          items={[
            {
              title: "Dashboard",
              url: "/dashboard",
              icon: IconDashboard,
            },
            {
              title: "Content Hub",
              url: "/dashboard/content-hub",
              icon: IconFolder,
            },
            {
              title: "Release Planning",
              url: "/dashboard/release-planning",
              icon: IconChartBar,
            },
            {
              title: "Audience Analytics",
              url: "/dashboard/audience-analytics",
              icon: IconChartBar,
            },
            {
              title: "Marketing AI",
              url: "/dashboard/marketing-ai",
              icon: IconFileAi,
            },
            {
              title: "Royalty Management",
              url: "/dashboard/royalty-management",
              icon: IconDatabase,
            },
            {
              title: "Distribution",
              url: "/dashboard/distribution",
              icon: IconShare,
            }
          ]}
        />
        <NavVideo />
        <NavSecondary
          items={[
            {
              title: "AI Insights",
              url: "/dashboard/ai-insights",
              icon: IconSparkles,
            },
            {
              title: "Collaborators",
              url: "/dashboard/collaborators",
              icon: IconUsers,
            },
            {
              title: "Settings",
              url: "/dashboard/settings",
              icon: IconSettings,
            },
          ]}
        />
        <NavDocuments items={data.documents} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  )
}

function MusicIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M9 18V5l12-2v13" />
      <circle cx="6" cy="18" r="3" />
      <circle cx="18" cy="16" r="3" />
    </svg>
  )
}
